<svg width="88" height="88" viewBox="0 0 88 88" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 定义动画 -->
    <style>
      .rotating-bagua {
        animation: rotate-clockwise 12s linear infinite;
        transform-origin: 44px 44px;
      }
      
      @keyframes rotate-clockwise {
        from {
          transform: rotate(0deg);
        }
        to {
          transform: rotate(360deg);
        }
      }
      
      /* 确保在不同主题下都能正常显示 */
      .bagua-light {
        fill: #d4af37;
        stroke: #d4af37;
      }
      
      .bagua-dark {
        fill: #ff6b6b;
        stroke: #ff6b6b;
      }
      
      /* 响应式设计 */
      @media (prefers-color-scheme: dark) {
        .bagua-light {
          fill: #ff6b6b;
          stroke: #ff6b6b;
        }
      }
    </style>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="44" cy="44" r="42" fill="rgba(255,255,255,0.1)" stroke="none"/>
  
  <!-- 旋转的八卦图组 -->
  <g class="rotating-bagua">
    <!-- 外圈八卦环 -->
    <circle cx="44" cy="44" r="38" stroke="currentColor" stroke-width="2" fill="none" class="bagua-light"/>
    
    <!-- 太极图主体 -->
    <circle cx="44" cy="44" r="28" fill="currentColor" class="bagua-light"/>
    
    <!-- 阴阳分界线 -->
    <path d="M 44 16 A 28 28 0 0 1 44 72 A 14 14 0 0 1 44 44 A 14 14 0 0 0 44 16" fill="white"/>
    
    <!-- 阴阳鱼眼 -->
    <circle cx="44" cy="30" r="4" fill="white"/>
    <circle cx="44" cy="58" r="4" fill="currentColor" class="bagua-light"/>
    
    <!-- 八卦符号 - 八个方位 -->
    <g stroke="currentColor" stroke-width="2" fill="none" class="bagua-light">
      <!-- 乾卦 (上) - 三条实线 -->
      <g transform="translate(44, 6)">
        <line x1="-6" y1="0" x2="6" y2="0"/>
        <line x1="-6" y1="3" x2="6" y2="3"/>
        <line x1="-6" y1="6" x2="6" y2="6"/>
      </g>
      
      <!-- 兑卦 (右上) -->
      <g transform="translate(75, 13) rotate(45)">
        <line x1="-6" y1="0" x2="6" y2="0"/>
        <line x1="-3" y1="3" x2="-1" y2="3"/>
        <line x1="1" y1="3" x2="3" y2="3"/>
        <line x1="-6" y1="6" x2="6" y2="6"/>
      </g>
      
      <!-- 离卦 (右) -->
      <g transform="translate(82, 44) rotate(90)">
        <line x1="-6" y1="0" x2="6" y2="0"/>
        <line x1="-3" y1="3" x2="-1" y2="3"/>
        <line x1="1" y1="3" x2="3" y2="3"/>
        <line x1="-6" y1="6" x2="6" y2="6"/>
      </g>
      
      <!-- 震卦 (右下) -->
      <g transform="translate(75, 75) rotate(135)">
        <line x1="-3" y1="0" x2="-1" y2="0"/>
        <line x1="1" y1="0" x2="3" y2="0"/>
        <line x1="-3" y1="3" x2="-1" y2="3"/>
        <line x1="1" y1="3" x2="3" y2="3"/>
        <line x1="-6" y1="6" x2="6" y2="6"/>
      </g>
      
      <!-- 坤卦 (下) - 三条虚线 -->
      <g transform="translate(44, 82) rotate(180)">
        <line x1="-3" y1="0" x2="-1" y2="0"/>
        <line x1="1" y1="0" x2="3" y2="0"/>
        <line x1="-3" y1="3" x2="-1" y2="3"/>
        <line x1="1" y1="3" x2="3" y2="3"/>
        <line x1="-3" y1="6" x2="-1" y2="6"/>
        <line x1="1" y1="6" x2="3" y2="6"/>
      </g>
      
      <!-- 艮卦 (左下) -->
      <g transform="translate(13, 75) rotate(225)">
        <line x1="-6" y1="0" x2="6" y2="0"/>
        <line x1="-3" y1="3" x2="-1" y2="3"/>
        <line x1="1" y1="3" x2="3" y2="3"/>
        <line x1="-3" y1="6" x2="-1" y2="6"/>
        <line x1="1" y1="6" x2="3" y2="6"/>
      </g>
      
      <!-- 坎卦 (左) -->
      <g transform="translate(6, 44) rotate(270)">
        <line x1="-3" y1="0" x2="-1" y2="0"/>
        <line x1="1" y1="0" x2="3" y2="0"/>
        <line x1="-6" y1="3" x2="6" y2="3"/>
        <line x1="-3" y1="6" x2="-1" y2="6"/>
        <line x1="1" y1="6" x2="3" y2="6"/>
      </g>
      
      <!-- 巽卦 (左上) -->
      <g transform="translate(13, 13) rotate(315)">
        <line x1="-6" y1="0" x2="6" y2="0"/>
        <line x1="-6" y1="3" x2="6" y2="3"/>
        <line x1="-3" y1="6" x2="-1" y2="6"/>
        <line x1="1" y1="6" x2="3" y2="6"/>
      </g>
    </g>
  </g>
  
  <!-- 中心点装饰 -->
  <circle cx="44" cy="44" r="2" fill="currentColor" class="bagua-light" opacity="0.8"/>
</svg>
