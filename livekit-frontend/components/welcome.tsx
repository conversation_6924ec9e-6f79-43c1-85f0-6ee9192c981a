import React, { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';

interface WelcomeProps {
  disabled: boolean;
  startButtonText: string;
  onStartCall: () => void;
}

export const Welcome = ({
  disabled,
  startButtonText,
  onStartCall,
  ref,
}: React.ComponentProps<'div'> & WelcomeProps) => {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  return (
    <div
      ref={ref}
      inert={disabled}
      className="fixed inset-0 z-10 mx-auto flex h-svh flex-col items-center justify-center text-center"
    >
      {/* 动态旋转八卦图标 */}
      {isClient ? (
        <div className="mb-4 size-20 flex items-center justify-center">
          <img
            src="/bagua-animated.svg"
            alt="动态八卦图"
            className="w-20 h-20 text-fg0"
            style={{
              filter: 'drop-shadow(0 4px 8px rgba(0,0,0,0.1))'
            }}
          />
        </div>
      ) : (
        <div className="mb-4 size-20 flex items-center justify-center">
          <div className="w-20 h-20 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-full animate-pulse" />
        </div>
      )}

      <p className="text-fg1 max-w-prose pt-1 leading-6 font-medium">
        🏮 与专业风水大师AI实时语音咨询
      </p>
      <p className="text-fg2 max-w-prose pt-2 text-sm leading-5">
        • 房屋布局风水分析 • 家具摆放建议 • 颜色搭配指导 • 风水改善方案
      </p>
      <Button variant="primary" size="lg" onClick={onStartCall} className="mt-6 w-64 font-mono">
        {startButtonText}
      </Button>
      <p className="text-fg1 m fixed bottom-5 left-1/2 w-full max-w-prose -translate-x-1/2 pt-1 text-xs leading-5 font-normal text-pretty md:text-sm">
        基于归云.中国和DeepSeek技术构建的专业风水知识库 |{' '}
        <a
          target="_blank"
          rel="noopener noreferrer"
          href="https://docs.livekit.io/agents/start/voice-ai/"
          className="underline"
        >
          技术文档
        </a>
      </p>
    </div>
  );
};
