import React from 'react';
import { Button } from '@/components/ui/button';

interface OriginalWelcomeProps {
  disabled?: boolean;
  startButtonText?: string;
  onStartCall?: () => void;
}

export const OriginalWelcome = ({
  disabled,
  startButtonText = 'Start Voice Assistant',
  onStartCall,
  ref,
}: React.ComponentProps<'div'> & OriginalWelcomeProps) => {
  return (
    <div
      ref={ref}
      className="fixed inset-0 z-10 mx-auto flex h-svh flex-col items-center justify-center text-center"
    >
      {/* 原始LiveKit图标 */}
      <div className="mb-8">
        <div className="w-16 h-16 bg-blue-600 rounded-2xl flex items-center justify-center mb-4 mx-auto">
          <svg
            width="32"
            height="32"
            viewBox="0 0 32 32"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="text-white"
          >
            <path
              d="M16 2L20.5 6.5L16 11L11.5 6.5L16 2Z"
              fill="currentColor"
            />
            <path
              d="M6.5 11.5L11 16L6.5 20.5L2 16L6.5 11.5Z"
              fill="currentColor"
            />
            <path
              d="M25.5 11.5L30 16L25.5 20.5L21 16L25.5 11.5Z"
              fill="currentColor"
            />
            <path
              d="M16 21L20.5 25.5L16 30L11.5 25.5L16 21Z"
              fill="currentColor"
            />
            <circle
              cx="16"
              cy="16"
              r="4"
              fill="currentColor"
            />
          </svg>
        </div>
      </div>

      {/* 标题和描述 */}
      <h1 className="text-4xl font-bold text-gray-900 mb-4">
        LiveKit Voice Assistant
      </h1>
      
      <p className="text-lg text-gray-600 max-w-2xl mb-2">
        Experience real-time voice interaction powered by LiveKit and AI
      </p>
      
      <p className="text-sm text-gray-500 max-w-prose mb-8">
        • Real-time voice processing • Natural language understanding • Low-latency responses • Scalable infrastructure
      </p>

      {/* 开始按钮 */}
      <Button
        onClick={onStartCall}
        disabled={disabled}
        className="mt-6 w-64 h-12 text-base font-semibold bg-blue-600 hover:bg-blue-700 text-white"
      >
        {startButtonText}
      </Button>

      {/* 底部信息 */}
      <p className="fixed bottom-5 left-1/2 w-full max-w-prose -translate-x-1/2 pt-1 text-xs leading-5 font-normal text-gray-500 md:text-sm">
        Powered by LiveKit real-time infrastructure |{' '}
        <a
          target="_blank"
          rel="noopener noreferrer"
          href="https://docs.livekit.io/agents/start/voice-ai/"
          className="underline hover:text-blue-600"
        >
          Learn more
        </a>
      </p>
    </div>
  );
};
