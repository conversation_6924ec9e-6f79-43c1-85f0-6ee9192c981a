import { type AgentState, BarVisualizer, type TrackReference } from '@livekit/components-react';
import { cn } from '@/lib/utils';

interface AgentAudioTileProps {
  state: AgentState;
  audioTrack: TrackReference;
  className?: string;
}

export const AgentTile = ({
  state,
  audioTrack,
  className,
  ref,
}: React.ComponentProps<'div'> & AgentAudioTileProps) => {
  return (
    <div ref={ref} className={cn(className)}>
      <BarVisualizer
        barCount={5}
        state={state}
        options={{ minHeight: 5 }}
        trackRef={audioTrack}
        className={cn('flex aspect-video w-40 items-center justify-center gap-1')}
      >
        <span
          className={cn([
            'min-h-4 w-4 rounded-full lk-audio-bar',
            'origin-center transition-all duration-300 ease-out',
            'data-[lk-highlighted=true]:lk-highlighted data-[lk-muted=true]:bg-muted',
            'bg-[#8b4513] data-[lk-highlighted=true]:bg-[#d4af37]',
            'dark:bg-[#4a5568] dark:data-[lk-highlighted=true]:bg-[#ff6b6b]',
            'shadow-sm data-[lk-highlighted=true]:shadow-lg',
            'data-[lk-highlighted=true]:scale-110',
          ])}
        />
      </BarVisualizer>
    </div>
  );
};
