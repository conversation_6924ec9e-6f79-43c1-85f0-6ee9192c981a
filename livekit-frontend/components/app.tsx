'use client';

import { useEffect, useMemo, useState } from 'react';
import { Room, RoomEvent } from 'livekit-client';
import { motion } from 'motion/react';
import { RoomAudioRenderer, RoomContext, StartAudio } from '@livekit/components-react';
import { toastAlert } from '@/components/alert-toast';
import { SessionView } from '@/components/session-view';
import { Toaster } from '@/components/ui/sonner';
import { Welcome } from '@/components/welcome';
import useConnectionDetails from '@/hooks/useConnectionDetails';
import type { AppConfig } from '@/lib/types';

const MotionWelcome = motion.create(Welcome);
const MotionSessionView = motion.create(SessionView);

interface AppProps {
  appConfig: AppConfig;
}

export function App({ appConfig }: AppProps) {
  const room = useMemo(() => new Room(), []);
  const [sessionStarted, setSessionStarted] = useState(false);
  const { connectionDetails, refreshConnectionDetails } = useConnectionDetails();

  useEffect(() => {
    const onDisconnected = () => {
      setSessionStarted(false);
      refreshConnectionDetails();
    };

    // 🎯 网络状态监控
    const handleOnline = () => {
      console.log('网络已恢复，尝试重新连接...');
      if (sessionStarted && room.state === 'disconnected') {
        refreshConnectionDetails();
      }
    };

    const handleOffline = () => {
      toastAlert({
        title: '🌐 网络连接中断',
        description: '检测到网络连接问题，请检查您的网络状态。网络恢复后将自动重连。',
      });
    };

    // 添加网络状态监听
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    const onMediaDevicesError = (error: Error) => {
      // 🎯 改进媒体设备错误的用户引导
      const isPermissionDenied = error.name === 'NotAllowedError' ||
                                error.message.includes('permission') ||
                                error.message.includes('denied');

      const isDeviceNotFound = error.name === 'NotFoundError' ||
                              error.message.includes('not found') ||
                              error.message.includes('no device');

      let title = '🎤 麦克风设备问题';
      let description = '';

      if (isPermissionDenied) {
        title = '🔒 需要麦克风权限';
        description = (
          <div className="space-y-2">
            <p>风水AI助手需要使用您的麦克风进行语音交流</p>
            <p className="text-sm text-muted-foreground">
              请点击浏览器地址栏的🔒图标，选择"允许"麦克风权限，然后刷新页面
            </p>
          </div>
        );
      } else if (isDeviceNotFound) {
        title = '🎤 未找到麦克风设备';
        description = (
          <div className="space-y-2">
            <p>请确保您的设备已连接麦克风</p>
            <p className="text-sm text-muted-foreground">
              检查麦克风是否正常工作，或尝试重新连接设备
            </p>
          </div>
        );
      } else {
        description = (
          <div className="space-y-2">
            <p>麦克风设备遇到未知问题</p>
            <details className="text-xs text-muted-foreground">
              <summary className="cursor-pointer">技术详情</summary>
              <p className="mt-1">{error.name}: {error.message}</p>
            </details>
          </div>
        );
      }

      toastAlert({
        title,
        description,
      });
    };
    room.on(RoomEvent.MediaDevicesError, onMediaDevicesError);
    room.on(RoomEvent.Disconnected, onDisconnected);
    return () => {
      room.off(RoomEvent.Disconnected, onDisconnected);
      room.off(RoomEvent.MediaDevicesError, onMediaDevicesError);
      // 清理网络状态监听
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [room, refreshConnectionDetails, sessionStarted]);

  useEffect(() => {
    let aborted = false;

    // ⚡ 房间连接重试机制
    const connectWithRetry = async () => {
      if (!sessionStarted || room.state !== 'disconnected' || !connectionDetails) {
        return;
      }

      const maxRetries = 3;
      let lastError: Error | null = null;

      for (let attempt = 0; attempt <= maxRetries; attempt++) {
        if (aborted) return; // 检查是否已取消

        try {
          await Promise.all([
            room.localParticipant.setMicrophoneEnabled(true, undefined, {
              preConnectBuffer: appConfig.isPreConnectBufferEnabled,
            }),
            room.connect(connectionDetails.serverUrl, connectionDetails.participantToken),
          ]);
          return; // 连接成功，退出重试循环
        } catch (error) {
          lastError = error as Error;
          console.warn(`LiveKit连接失败 (尝试 ${attempt + 1}/${maxRetries + 1}):`, error);

          // 如果不是最后一次尝试，等待后重试
          if (attempt < maxRetries && !aborted) {
            const delay = Math.pow(2, attempt) * 1000; // 1秒、2秒、4秒
            console.log(`${delay/1000}秒后重试连接...`);
            await new Promise(resolve => setTimeout(resolve, delay));
          }
        }
      }

      // 所有重试都失败了，显示友好的错误提示
      if (!aborted && lastError) {
        const isNetworkError = lastError.message.includes('fetch') ||
                              lastError.message.includes('network') ||
                              lastError.message.includes('timeout');

        const isPermissionError = lastError.message.includes('permission') ||
                                 lastError.message.includes('denied') ||
                                 lastError.message.includes('NotAllowed');

        let userFriendlyMessage = '';
        let troubleshootingTips = '';

        if (isNetworkError) {
          userFriendlyMessage = '网络连接不稳定，请检查您的网络状态';
          troubleshootingTips = '建议：刷新页面重试，或检查网络连接';
        } else if (isPermissionError) {
          userFriendlyMessage = '需要麦克风权限才能与风水AI助手对话';
          troubleshootingTips = '请在浏览器地址栏点击🔒图标，允许麦克风权限后刷新页面';
        } else {
          userFriendlyMessage = '暂时无法连接到风水AI助手服务';
          troubleshootingTips = '请稍后重试，或联系技术支持';
        }

        toastAlert({
          title: '🏮 连接风水AI助手遇到问题',
          description: (
            <div className="space-y-2">
              <p>{userFriendlyMessage}</p>
              <p className="text-sm text-muted-foreground">{troubleshootingTips}</p>
              <details className="text-xs text-muted-foreground">
                <summary className="cursor-pointer">技术详情</summary>
                <p className="mt-1">{lastError.name}: {lastError.message}</p>
              </details>
            </div>
          ),
        });
      }
    };

    connectWithRetry();

    return () => {
      aborted = true;
      room.disconnect();
    };
  }, [room, sessionStarted, connectionDetails, appConfig.isPreConnectBufferEnabled]);

  const { startButtonText } = appConfig;

  return (
    <>
      <MotionWelcome
        key="welcome"
        startButtonText={startButtonText}
        onStartCall={() => setSessionStarted(true)}
        disabled={sessionStarted}
        initial={{ opacity: 0 }}
        animate={{ opacity: sessionStarted ? 0 : 1 }}
        transition={{ duration: 0.5, ease: 'linear', delay: sessionStarted ? 0 : 0.5 }}
      />

      <RoomContext.Provider value={room}>
        <RoomAudioRenderer />
        <StartAudio label="Start Audio" />
        {/* --- */}
        <MotionSessionView
          key="session-view"
          appConfig={appConfig}
          disabled={!sessionStarted}
          sessionStarted={sessionStarted}
          initial={{ opacity: 0 }}
          animate={{ opacity: sessionStarted ? 1 : 0 }}
          transition={{
            duration: 0.5,
            ease: 'linear',
            delay: sessionStarted ? 0.5 : 0,
          }}
        />
      </RoomContext.Provider>

      <Toaster />
    </>
  );
}
