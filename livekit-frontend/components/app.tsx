'use client';

import { useEffect, useMemo, useState } from 'react';
import { Room, RoomEvent } from 'livekit-client';
import { motion } from 'motion/react';
import { RoomAudioRenderer, RoomContext, StartAudio } from '@livekit/components-react';
import { toastAlert } from '@/components/alert-toast';
import { SessionView } from '@/components/session-view';
import { Toaster } from '@/components/ui/sonner';
import { Welcome } from '@/components/welcome';
import useConnectionDetails from '@/hooks/useConnectionDetails';
import type { AppConfig } from '@/lib/types';

const MotionWelcome = motion.create(Welcome);
const MotionSessionView = motion.create(SessionView);

interface AppProps {
  appConfig: AppConfig;
}

export function App({ appConfig }: AppProps) {
  const room = useMemo(() => new Room(), []);
  const [sessionStarted, setSessionStarted] = useState(false);
  const { connectionDetails, refreshConnectionDetails } = useConnectionDetails();

  useEffect(() => {
    const onDisconnected = () => {
      setSessionStarted(false);
      refreshConnectionDetails();
    };

    // 🎯 网络状态监控
    const handleOnline = () => {
      console.log('网络已恢复，尝试重新连接...');
      if (sessionStarted && room.state === 'disconnected') {
        refreshConnectionDetails();
      }
    };

    const handleOffline = () => {
      toastAlert({
        title: '🌐 网络连接中断',
        description: '检测到网络连接问题，请检查您的网络状态。网络恢复后将自动重连。',
      });
    };

    // 添加网络状态监听
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    const onMediaDevicesError = (error: Error) => {
      // 🎯 改进媒体设备错误的用户引导
      const isPermissionDenied = error.name === 'NotAllowedError' ||
                                error.message.includes('permission') ||
                                error.message.includes('denied');

      const isDeviceNotFound = error.name === 'NotFoundError' ||
                              error.message.includes('not found') ||
                              error.message.includes('no device');

      let title = '🎤 麦克风设备问题';
      let description = '';

      if (isPermissionDenied) {
        title = '🔒 需要麦克风权限';
        description = (
          <div className="space-y-2">
            <p>风水AI助手需要使用您的麦克风进行语音交流</p>
            <p className="text-sm text-muted-foreground">
              请点击浏览器地址栏的🔒图标，选择"允许"麦克风权限，然后刷新页面
            </p>
          </div>
        );
      } else if (isDeviceNotFound) {
        title = '🎤 未找到麦克风设备';
        description = (
          <div className="space-y-2">
            <p>请确保您的设备已连接麦克风</p>
            <p className="text-sm text-muted-foreground">
              检查麦克风是否正常工作，或尝试重新连接设备
            </p>
          </div>
        );
      } else {
        description = (
          <div className="space-y-2">
            <p>麦克风设备遇到未知问题</p>
            <details className="text-xs text-muted-foreground">
              <summary className="cursor-pointer">技术详情</summary>
              <p className="mt-1">{error.name}: {error.message}</p>
            </details>
          </div>
        );
      }

      toastAlert({
        title,
        description,
      });
    };
    room.on(RoomEvent.MediaDevicesError, onMediaDevicesError);
    room.on(RoomEvent.Disconnected, onDisconnected);
    return () => {
      room.off(RoomEvent.Disconnected, onDisconnected);
      room.off(RoomEvent.MediaDevicesError, onMediaDevicesError);
      // 清理网络状态监听
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [room, refreshConnectionDetails, sessionStarted]);

  useEffect(() => {
    let aborted = false;
    console.log('🔍 连接状态检查:', {
      sessionStarted,
      roomState: room.state,
      hasConnectionDetails: !!connectionDetails,
      serverUrl: connectionDetails?.serverUrl,
    });

    if (sessionStarted && room.state === 'disconnected' && connectionDetails) {
      console.log('🚀 开始连接LiveKit...', connectionDetails.serverUrl);
      Promise.all([
        room.localParticipant.setMicrophoneEnabled(true, undefined, {
          preConnectBuffer: appConfig.isPreConnectBufferEnabled,
        }),
        room.connect(connectionDetails.serverUrl, connectionDetails.participantToken),
      ]).then(() => {
        console.log('✅ LiveKit连接成功');
      }).catch((error) => {
        if (aborted) {
          return;
        }
        console.error('❌ LiveKit连接失败:', error);
        toastAlert({
          title: '连接风水AI助手失败',
          description: `${error.name}: ${error.message}`,
        });
      });
    }
    return () => {
      aborted = true;
      room.disconnect();
    };
  }, [room, sessionStarted, connectionDetails, appConfig.isPreConnectBufferEnabled]);

  const { startButtonText } = appConfig;

  return (
    <>
      <MotionWelcome
        key="welcome"
        startButtonText={startButtonText}
        onStartCall={() => setSessionStarted(true)}
        disabled={sessionStarted}
        initial={{ opacity: 0 }}
        animate={{ opacity: sessionStarted ? 0 : 1 }}
        transition={{ duration: 0.5, ease: 'linear', delay: sessionStarted ? 0 : 0.5 }}
      />

      <RoomContext.Provider value={room}>
        <RoomAudioRenderer />
        <StartAudio label="Start Audio" />
        {/* --- */}
        <MotionSessionView
          key="session-view"
          appConfig={appConfig}
          disabled={!sessionStarted}
          sessionStarted={sessionStarted}
          initial={{ opacity: 0 }}
          animate={{ opacity: sessionStarted ? 1 : 0 }}
          transition={{
            duration: 0.5,
            ease: 'linear',
            delay: sessionStarted ? 0.5 : 0,
          }}
        />
      </RoomContext.Provider>

      <Toaster />
    </>
  );
}
