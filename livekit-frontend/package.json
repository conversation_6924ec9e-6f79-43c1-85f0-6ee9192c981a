{"name": "agent-starter-react", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack -p 3001", "build": "next build", "start": "next start -p 3001", "lint": "next lint", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"@livekit/components-react": "^2.9.9", "@phosphor-icons/react": "^2.1.8", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toolbar": "^1.1.10", "buffer-image-size": "^0.6.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "livekit-client": "^2.13.3", "livekit-server-sdk": "^2.13.0", "mime": "^4.0.7", "motion": "^12.16.0", "next": "15.4.5", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "sonner": "^2.0.3", "tailwind-merge": "^3.3.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/node": "^22.0.0", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.5", "eslint-config-prettier": "^10.1.5", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.5.0", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4", "tw-animate-css": "^1.3.0", "typescript": "^5"}, "packageManager": "pnpm@9.15.9"}