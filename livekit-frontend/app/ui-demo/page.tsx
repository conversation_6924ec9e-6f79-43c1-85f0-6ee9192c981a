'use client';

import { useState } from 'react';

export default function UIDemo() {
  const [speed, setSpeed] = useState(65);
  const [isAutomatic, setIsAutomatic] = useState(false);
  const [isActive, setIsActive] = useState(true);

  return (
    <div className="min-h-screen flex items-center justify-center p-4" style={{
      background: 'linear-gradient(135deg, #4a5568 0%, #2d3748 100%)'
    }}>
      {/* 主卡片容器 */}
      <div className="relative w-full max-w-sm">
        {/* AI机器人 - 突出卡片顶部 */}
        <div className="absolute -top-20 left-1/2 transform -translate-x-1/2 z-10">
          <div className="relative">
            {/* 机器人主体 */}
            <div className="w-40 h-32 relative">
              {/* 机器人身体 */}
              <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-24 h-20 bg-gradient-to-b from-gray-100 via-white to-gray-200 rounded-t-3xl shadow-2xl">
                {/* 胸部细节 */}
                <div className="absolute top-4 left-1/2 transform -translate-x-1/2 w-16 h-12 bg-gradient-to-b from-gray-50 to-gray-100 rounded-2xl shadow-inner"></div>
              </div>

              {/* 机器人头部 */}
              <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-20 h-20 bg-gradient-to-b from-white via-gray-50 to-gray-100 rounded-2xl shadow-2xl">
                {/* 显示屏 */}
                <div className="absolute top-3 left-1/2 transform -translate-x-1/2 w-16 h-10 bg-gray-900 rounded-lg flex flex-col items-center justify-center shadow-inner">
                  <span className="text-white text-xs font-mono font-bold">
                    {isActive ? 'ACTIVE' : 'IDLE'}
                  </span>
                  <span className="text-blue-400 text-xs font-mono">
                    {speed}
                  </span>
                </div>
              </div>

              {/* 右手臂和手 */}
              <div className="absolute top-8 -right-2 w-8 h-16 bg-gradient-to-b from-gray-100 to-gray-200 rounded-full shadow-lg transform rotate-12">
                {/* 手掌 */}
                <div className="absolute -top-2 -right-1 w-6 h-6 bg-gradient-to-br from-gray-50 to-gray-200 rounded-full shadow-md">
                  {/* 手指 */}
                  <div className="absolute -top-1 left-1 w-1 h-3 bg-gray-200 rounded-full"></div>
                  <div className="absolute -top-1 left-2.5 w-1 h-3 bg-gray-200 rounded-full"></div>
                  <div className="absolute -top-1 left-4 w-1 h-3 bg-gray-200 rounded-full"></div>
                </div>
              </div>

              {/* 左手臂 */}
              <div className="absolute top-8 -left-2 w-6 h-12 bg-gradient-to-b from-gray-100 to-gray-200 rounded-full shadow-lg transform -rotate-12"></div>
            </div>

            {/* 机器人阴影 */}
            <div className="absolute -bottom-4 left-1/2 transform -translate-x-1/2 w-32 h-6 bg-black/20 rounded-full blur-md"></div>
          </div>
        </div>

        {/* 卡片主体 */}
        <div className="bg-gray-50 rounded-3xl p-8 pt-24 shadow-2xl" style={{
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.1)'
        }}>
            {/* 速度滑块控件 */}
            <div className="space-y-4">
              <div className="flex items-center justify-between text-sm text-gray-600">
                <div className="flex items-center space-x-2">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                  </svg>
                  <span className="font-medium">Slow</span>
                </div>
                <span className="font-medium">Fast</span>
              </div>

              {/* 现代滑块 */}
              <div className="relative h-2 bg-gray-300 rounded-full">
                <div
                  className="absolute top-0 left-0 h-2 rounded-full"
                  style={{
                    width: `${speed}%`,
                    background: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)'
                  }}
                ></div>
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={speed}
                  onChange={(e) => setSpeed(parseInt(e.target.value))}
                  className="absolute top-0 left-0 w-full h-2 opacity-0 cursor-pointer"
                />
                <div
                  className="absolute top-1/2 w-5 h-5 bg-white border-2 border-blue-500 rounded-full shadow-lg transform -translate-y-1/2 cursor-pointer"
                  style={{
                    left: `calc(${speed}% - 10px)`
                  }}
                ></div>
              </div>
            </div>

            {/* 自动模式开关 */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <svg className="w-6 h-6 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd"/>
                </svg>
                <span className="text-gray-700 font-medium text-lg">Automatic</span>
              </div>

              {/* 现代Toggle开关 */}
              <button
                onClick={() => setIsAutomatic(!isAutomatic)}
                className={`relative inline-flex h-8 w-14 items-center rounded-full transition-all duration-300 focus:outline-none ${
                  isAutomatic ? 'bg-gray-600' : 'bg-gray-400'
                }`}
              >
                <span
                  className={`inline-block h-6 w-6 transform rounded-full bg-white transition-transform duration-300 shadow-lg ${
                    isAutomatic ? 'translate-x-7' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>

            {/* 保存按钮 */}
            <button
              onClick={() => setIsActive(!isActive)}
              className="w-full bg-black text-white py-4 rounded-3xl font-medium text-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98] hover:bg-gray-800"
            >
              Save
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
