'use client';

import { useState } from 'react';

export default function UIDemo() {
  const [speed, setSpeed] = useState(65);
  const [isAutomatic, setIsAutomatic] = useState(false);
  const [isActive, setIsActive] = useState(true);

  return (
    <div className="min-h-screen bg-slate-600 flex items-center justify-center p-4">
      {/* 主卡片容器 */}
      <div className="relative bg-gray-50 rounded-3xl p-8 w-full max-w-sm shadow-2xl overflow-visible">
        
        {/* AI助手头像 - 突出卡片顶部 */}
        <div className="absolute -top-16 left-1/2 transform -translate-x-1/2">
          <div className="relative">
            {/* AI助手容器 */}
            <div className="w-32 h-32 bg-gradient-to-br from-gray-100 to-gray-300 rounded-2xl shadow-xl flex items-center justify-center overflow-hidden">
              {/* AI助手身体 */}
              <div className="w-24 h-24 bg-gradient-to-br from-white to-gray-200 rounded-xl relative">
                {/* 头盔/显示屏 */}
                <div className="absolute top-2 left-1/2 transform -translate-x-1/2 w-16 h-8 bg-gray-900 rounded-lg flex items-center justify-center">
                  <span className="text-white text-xs font-mono">
                    {isActive ? 'ACTIVE' : 'IDLE'}
                  </span>
                </div>
                <div className="absolute top-10 left-1/2 transform -translate-x-1/2 text-white text-xs font-mono">
                  {speed}
                </div>
                
                {/* 手臂/手势 */}
                <div className="absolute -right-2 top-8 w-6 h-6 bg-gradient-to-br from-white to-gray-200 rounded-full shadow-md">
                  <div className="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-br from-white to-gray-200 rounded-full"></div>
                </div>
              </div>
            </div>
            
            {/* 阴影效果 */}
            <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-24 h-4 bg-black/10 rounded-full blur-sm"></div>
          </div>
        </div>

        {/* 内容区域 - 增加顶部间距为头像留空间 */}
        <div className="pt-20 space-y-8">
          
          {/* 速度滑块控件 */}
          <div className="space-y-4">
            <div className="flex items-center justify-between text-sm text-gray-600">
              <div className="flex items-center space-x-2">
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                  <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd"/>
                </svg>
                <span>Slow</span>
              </div>
              <span>Fast</span>
            </div>
            
            {/* 滑块 */}
            <div className="relative">
              <input
                type="range"
                min="0"
                max="100"
                value={speed}
                onChange={(e) => setSpeed(parseInt(e.target.value))}
                className="w-full h-2 rounded-lg appearance-none cursor-pointer slider"
                style={{
                  background: `linear-gradient(to right, #3b82f6 0%, #8b5cf6 ${speed}%, #d1d5db ${speed}%, #d1d5db 100%)`
                }}
              />
            </div>
          </div>

          {/* 自动模式开关 */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <svg className="w-6 h-6 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd"/>
              </svg>
              <span className="text-gray-700 font-medium">Automatic</span>
            </div>
            
            {/* Toggle开关 */}
            <button
              onClick={() => setIsAutomatic(!isAutomatic)}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                isAutomatic ? 'bg-blue-600' : 'bg-gray-300'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  isAutomatic ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>

          {/* 保存按钮 */}
          <button
            onClick={() => setIsActive(!isActive)}
            className="w-full bg-gray-900 text-white py-4 rounded-2xl font-medium text-lg hover:bg-gray-800 transition-colors duration-200 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]"
          >
            Save
          </button>
        </div>
      </div>
    </div>
  );
}
