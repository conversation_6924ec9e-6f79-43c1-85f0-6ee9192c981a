'use client';

import React from 'react';
import { App } from '@/components/app';

// 原始LiveKit配置 - 不包含风水定制
const originalConfig = {
  companyName: 'LiveKit',
  pageTitle: 'LiveKit Voice Assistant',
  pageDescription: 'A voice assistant built with LiveKit and OpenAI',
  supportsChatInput: true,
  supportsVideoInput: true,
  supportsScreenShare: true,
  isPreConnectBufferEnabled: true,
  logo: '/livekit-logo.svg',
  accent: '#007DFF',
  logoDark: '/livekit-logo.svg',
  accentDark: '#007DFF',
  startButtonText: 'Start Voice Assistant',
};

export default function OriginalLiveKitPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* 原始LiveKit样式的头部 */}
      <div className="fixed top-0 left-0 right-0 z-50 bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between max-w-7xl mx-auto">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">LK</span>
            </div>
            <h1 className="text-xl font-semibold text-gray-900">LiveKit Voice Assistant</h1>
          </div>
          <div className="text-sm text-gray-500">
            Built with <a href="https://livekit.io" className="text-blue-600 hover:underline">LiveKit</a>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="pt-20">
        <App appConfig={originalConfig} />
      </div>

      {/* 页面底部说明 */}
      <div className="fixed bottom-4 left-4 right-4 z-40">
        <div className="max-w-2xl mx-auto">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
            <p className="text-sm text-blue-800">
              <strong>原始LiveKit演示页面</strong> - 这是未经定制的原始LiveKit界面效果
            </p>
            <p className="text-xs text-blue-600 mt-1">
              <a href="/" className="hover:underline">← 返回风水AI助手</a> | 
              <a href="https://docs.livekit.io/agents" className="hover:underline ml-2">LiveKit文档</a>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
