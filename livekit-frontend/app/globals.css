@import 'tailwindcss';
@import 'tw-animate-css';

@custom-variant dark (&:is(.dark *));

:root {
  --fg0: #000000;
  --fg1: #3b3b3b;
  --fg2: #4d4d4d;
  --fg3: #636363;
  --fg4: #707070;
  --fgSerious: #db1b06;
  --fgSuccess: #006430;
  --fgModerate: #a65006;
  --fgAccent: #002cf2;

  --bg1: #f9f9f6;
  --bg2: #f3f3f1;
  --bg3: #e2e2df;
  --bgSerious: #fae6e6;
  --bgSerious2: #ffcdc7;
  --bgSuccess: #d1fadf;
  --bgModerate: #faedd1;
  --bgAccent: #b3ccff;
  --bgAccentPrimary: #e2ebfd;

  --separator1: #dbdbd8;
  --separator2: #bdbdbb;
  --separatorSerious: #ffcdc7;
  --separatorSuccess: #94dcb5;
  --separatorModerate: #fbd7a0;
  --separatorAccent: #b3ccff;

  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: #002cf2;
  --primary-hover: #0020b9;
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: #f3f3f1;
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --fg0: #ffffff;
  --fg1: #cccccc;
  --fg2: #b2b2b2;
  --fg3: #999999;
  --fg4: #666666;
  --fgSerious: #ff7566;
  --fgSuccess: #3bc981;
  --fgModerate: #ffb752;
  --fgAccent: #6e9dfe;

  --bg1: #070707;
  --bg2: #131313;
  --bg3: #202020;
  --bgSerious: #1f0e0b;
  --bgSerious2: #5a1c16;
  --bgSuccess: #001905;
  --bgModerate: #1a0e04;
  --bgAccent: #090c17;
  --bgAccentPrimary: #0c1640;

  --separator1: #202020;
  --separator2: #30302f;
  --separatorSerious: #5a1c16;
  --separatorSuccess: #003213;
  --separatorModerate: #3f2208;
  --separatorAccent: #0c1640;

  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.269 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: #1fd5f9;
  --primary-hover: #19a7c7;
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: #131313;
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.371 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.439 0 0);
}

@theme inline {
  --color-fg1: var(--fg1);
  --color-fg2: var(--fg2);
  --color-fg3: var(--fg3);
  --color-fg4: var(--fg4);
  --color-fgSerious: var(--fgSerious);
  --color-fgSuccess: var(--fgSuccess);
  --color-fgModerate: var(--fgModerate);
  --color-fgAccent: var(--fgAccent);

  --color-bg1: var(--bg1);
  --color-bg2: var(--bg2);
  --color-bg3: var(--bg3);
  --color-bgSerious: var(--bgSerious);
  --color-bgSerious2: var(--bgSerious2);
  --color-bgSuccess: var(--bgSuccess);
  --color-bgModerate: var(--bgModerate);
  --color-bgAccent: var(--bgAccent);
  --color-bgAccentPrimary: var(--bgAccentPrimary);

  --color-separator1: var(--separator1);
  --color-separator2: var(--separator2);
  --color-separatorSerious: var(--separatorSerious);
  --color-separatorSuccess: var(--separatorSuccess);
  --color-separatorModerate: var(--separatorModerate);
  --color-separatorAccent: var(--separatorAccent);

  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-hover: var(--primary-hover);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--separator1);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --color-button: var(--bg2);
  --color-button-hover: var(--bg3);
  --color-button-foreground: var(--fg1);
  --color-button-primary: var(--bg2);
  --color-button-primary-foreground: var(--fgSerious);
  --color-button-secondary: var(--bgAccentPrimary);
  --color-button-secondary-foreground: var(--fgAccent);

  --color-destructive: var(--bgSerious);
  --color-destructive-hover: var(--bgSerious2);
  --color-destructive-foreground: var(--fgSerious);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer utils {
  .animate-text-shimmer {
    animation-delay: 0.5s;
    animation-duration: 3s;
    animation-iteration-count: infinite;
    animation-name: text-shimmer;
    background: var(--muted-foreground)
      gradient(
        linear,
        100% 0,
        0 0,
        from(var(--muted-foreground)),
        color-stop(0.5, var(--secondary-foreground)),
        to(var(--muted-foreground))
      );
    background: var(--muted-foreground) -webkit-gradient(
        linear,
        100% 0,
        0 0,
        from(var(--muted-foreground)),
        color-stop(0.5, var(--secondary-foreground)),
        to(var(--muted-foreground))
      );
    background-repeat: no-repeat;
    background-size: 50% 200%;
    display: inline-block;
  }

  @keyframes text-shimmer {
    0% {
      background-position: -100% 0;
    }
    100% {
      background-position: 250% 0;
    }
  }

  /* 八卦图动画优化 */
  .bagua-logo {
    will-change: transform;
    backface-visibility: hidden;
    transform-style: preserve-3d;
  }

  /* 确保动画在所有设备上流畅运行 */
  @media (prefers-reduced-motion: reduce) {
    .rotating-bagua {
      animation: none !important;
    }
  }

  /* 为低性能设备优化 */
  @media (max-width: 768px) {
    .rotating-bagua {
      animation-duration: 15s; /* 移动设备上稍慢一些 */
    }
  }
}
