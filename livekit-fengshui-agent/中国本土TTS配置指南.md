# 🇨🇳 中国本土免费TTS服务配置指南

## 🎯 推荐服务对比

| 服务商 | 免费额度 | 中文质量 | 获取难度 | 推荐度 |
|--------|----------|----------|----------|--------|
| **科大讯飞** | 500次/天 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | 🥇 最推荐 |
| **百度智能云** | 2000次/月 | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | 🥈 易用 |
| **腾讯云** | 1000次/月 | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | 🥉 稳定 |

## 🚀 方案1: 科大讯飞TTS (最推荐)

### 📋 获取步骤

1. **注册讯飞开放平台**
   - 访问: https://www.xfyun.cn/
   - 点击"注册/登录"
   - 完成实名认证

2. **创建应用**
   - 进入控制台 → 我的应用
   - 点击"创建新应用"
   - 应用名称: `风水AI助手`
   - 应用类型: `WebAPI`

3. **开通语音合成服务**
   - 在应用详情页面
   - 点击"语音合成" → "开通服务"
   - 选择"免费版" (每日500次)

4. **获取密钥信息**
   ```
   APPID: 应用详情页面的APPID
   APIKey: 应用详情页面的APIKey  
   APISecret: 应用详情页面的APISecret
   ```

### 🔧 配置到项目

```bash
# 在.env文件中添加:
XUNFEI_APP_ID=你的APPID
XUNFEI_API_KEY=你的APIKey
XUNFEI_API_SECRET=你的APISecret
```

### 🎵 声音选项
- `xiaoyan` - 小燕女声 (温和亲切)
- `aisjiuxu` - 许久男声 (成熟稳重)
- `aisxping` - 小萍女声 (清新自然)

---

## 🔵 方案2: 百度智能云TTS (简单易用)

### 📋 获取步骤

1. **注册百度智能云**
   - 访问: https://cloud.baidu.com/
   - 注册并完成实名认证

2. **创建应用**
   - 进入控制台 → 语音技术
   - 点击"创建应用"
   - 应用名称: `风水AI助手`
   - 应用类型: `不限制`

3. **获取密钥**
   - 在应用列表中查看详情
   - 复制 `API Key` 和 `Secret Key`

### 🔧 配置到项目

```bash
# 在.env文件中添加:
BAIDU_API_KEY=你的API_Key
BAIDU_SECRET_KEY=你的Secret_Key
```

### 🎵 声音选项
- `0` - 女声 (标准)
- `1` - 男声 (标准)
- `3` - 情感男声 (推荐)
- `4` - 情感女声 (推荐)

---

## 🟢 方案3: 腾讯云TTS (企业级)

### 📋 获取步骤

1. **注册腾讯云**
   - 访问: https://cloud.tencent.com/
   - 注册并完成实名认证

2. **开通语音合成服务**
   - 进入控制台 → 语音合成
   - 点击"立即开通"
   - 选择"免费版" (每月1000次)

3. **创建密钥**
   - 访问: https://console.cloud.tencent.com/cam/capi
   - 点击"新建密钥"
   - 复制 `SecretId` 和 `SecretKey`

### 🔧 配置到项目

```bash
# 在.env文件中添加:
TENCENT_SECRET_ID=你的SecretId
TENCENT_SECRET_KEY=你的SecretKey
```

---

## 🧪 测试TTS服务

### 测试科大讯飞TTS
```bash
cd livekit-fengshui-agent
source venv/bin/activate
python xunfei_tts.py
```

### 测试百度TTS
```bash
cd livekit-fengshui-agent
source venv/bin/activate
python baidu_tts.py
```

---

## 💡 使用建议

### 🎯 最佳配置组合
1. **主力**: 科大讯飞TTS (中文质量最佳)
2. **备用**: 百度智能云TTS (额度更多)
3. **国际**: Azure AI Speech (需付费)

### 📊 成本对比
- **科大讯飞**: 500次/天 = 15,000次/月 (免费)
- **百度智能云**: 2000次/月 (免费)
- **腾讯云**: 1000次/月 (免费)
- **Azure**: 500,000字符/月 (免费)

### 🚀 性能优化
- 使用缓存减少API调用
- 短文本优先使用讯飞
- 长文本使用百度或腾讯云
- 关键对话使用Azure (付费但质量最佳)

---

## 🔧 集成到LiveKit Agent

配置完成后，Agent会自动按优先级选择TTS服务:

1. 🥇 科大讯飞TTS (中国本土免费)
2. 🥈 百度智能云TTS (中国本土免费)  
3. 🥉 Azure AI Speech (国际付费)
4. 🏅 ElevenLabs (国际付费)
5. 🎖️ Cartesia (最终备用)

**配置任意一个服务后，重启Agent即可享受高质量中文语音合成！** 🎵✨
