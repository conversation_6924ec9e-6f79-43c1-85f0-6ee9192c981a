#!/usr/bin/env python3
"""
百度智能云TTS集成 - 中国本土免费TTS解决方案
每月2000次免费调用，API简单易用
"""

import asyncio
import base64
import json
import time
from typing import Optional
import httpx
import os
from dotenv import load_dotenv

load_dotenv()

class BaiduTTS:
    """百度智能云语音合成服务"""
    
    def __init__(self, api_key: str, secret_key: str):
        self.api_key = api_key
        self.secret_key = secret_key
        self.access_token = None
        self.token_expires = 0
        
    async def _get_access_token(self) -> str:
        """获取访问令牌"""
        if self.access_token and time.time() < self.token_expires:
            return self.access_token
            
        url = "https://aip.baidubce.com/oauth/2.0/token"
        params = {
            "grant_type": "client_credentials",
            "client_id": self.api_key,
            "client_secret": self.secret_key
        }
        
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(url, params=params)
                
                if response.status_code == 200:
                    result = response.json()
                    self.access_token = result["access_token"]
                    self.token_expires = time.time() + result["expires_in"] - 60  # 提前1分钟过期
                    return self.access_token
                else:
                    raise Exception(f"获取token失败: {response.status_code}")
                    
        except Exception as e:
            raise Exception(f"获取百度TTS token失败: {e}")
    
    async def synthesize(self, text: str, voice: int = 0) -> bytes:
        """
        语音合成
        
        Args:
            text: 要合成的文本
            voice: 声音类型 (0-女声, 1-男声, 3-情感男声, 4-情感女声)
        
        Returns:
            合成的音频数据 (MP3格式)
        """
        access_token = await self._get_access_token()
        
        url = "https://tsn.baidu.com/text2audio"
        
        # 构造请求参数
        params = {
            "tok": access_token,
            "tex": text,
            "per": voice,  # 发音人选择
            "spd": 5,      # 语速，取值0-15，默认为5中语速
            "pit": 5,      # 音调，取值0-15，默认为5中语调
            "vol": 8,      # 音量，取值0-15，默认为5中音量
            "aue": 3,      # 音频编码，3为mp3格式
            "cuid": "python_client",
            "lan": "zh",   # 语言选择，zh中文
            "ctp": 1       # 客户端类型选择，1为web端
        }
        
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(url, data=params)
                
                if response.status_code == 200:
                    # 检查返回的内容类型
                    content_type = response.headers.get("content-type", "")
                    
                    if "audio" in content_type:
                        # 返回音频数据
                        return response.content
                    else:
                        # 返回错误信息
                        try:
                            error_info = response.json()
                            raise Exception(f"百度TTS错误: {error_info}")
                        except:
                            raise Exception(f"百度TTS未知错误: {response.text}")
                else:
                    raise Exception(f"百度TTS请求失败: {response.status_code}")
                    
        except Exception as e:
            raise Exception(f"百度TTS合成失败: {e}")


class BaiduTTSLiveKit:
    """百度TTS的LiveKit适配器"""
    
    def __init__(self):
        self.api_key = os.getenv("BAIDU_API_KEY")
        self.secret_key = os.getenv("BAIDU_SECRET_KEY")
        
        if not all([self.api_key, self.secret_key]):
            raise ValueError("请设置百度TTS环境变量: BAIDU_API_KEY, BAIDU_SECRET_KEY")
        
        self.tts = BaiduTTS(self.api_key, self.secret_key)
        
    async def synthesize_text(self, text: str) -> bytes:
        """合成文本为语音"""
        return await self.tts.synthesize(text, voice=4)  # 使用情感女声


async def test_baidu_tts():
    """测试百度TTS"""
    print("🧪 测试百度智能云TTS")
    
    try:
        tts = BaiduTTSLiveKit()
        
        test_text = "您好！我是张大师，专业的风水顾问。今天我将为您提供专业的风水咨询服务。"
        print(f"📝 测试文本: {test_text}")
        
        audio_data = await tts.synthesize_text(test_text)
        
        if audio_data:
            print(f"✅ 合成成功！音频长度: {len(audio_data)} 字节")
            
            # 保存测试音频文件
            with open("test_baidu_tts.mp3", "wb") as f:
                f.write(audio_data)
            
            print("🎵 测试音频已保存为 test_baidu_tts.mp3")
            return True
        else:
            print("❌ 合成失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


if __name__ == "__main__":
    asyncio.run(test_baidu_tts())
