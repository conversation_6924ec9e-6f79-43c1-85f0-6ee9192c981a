#!/usr/bin/env python3
"""
科大讯飞TTS集成 - 中国本土免费TTS解决方案
每日500次免费调用，中文语音质量极佳
"""

import asyncio
import base64
import hashlib
import hmac
import json
import time
import urllib.parse
import websockets
from typing import AsyncIterator, Optional
import os
from dotenv import load_dotenv

load_dotenv()

class XunfeiTTS:
    """科大讯飞语音合成服务"""
    
    def __init__(self, app_id: str, api_key: str, api_secret: str):
        self.app_id = app_id
        self.api_key = api_key
        self.api_secret = api_secret
        self.base_url = "wss://tts-api.xfyun.cn/v2/tts"
        
    def _create_auth_url(self) -> str:
        """创建认证URL"""
        # 生成RFC1123格式的时间戳
        now = time.strftime('%a, %d %b %Y %H:%M:%S GMT', time.gmtime())
        
        # 拼接字符串
        signature_origin = f"host: ws-api.xfyun.cn\ndate: {now}\nGET /v2/tts HTTP/1.1"
        
        # 进行hmac-sha256进行加密
        signature_sha = hmac.new(
            self.api_secret.encode('utf-8'),
            signature_origin.encode('utf-8'),
            digestmod=hashlib.sha256
        ).digest()
        signature_sha = base64.b64encode(signature_sha).decode(encoding='utf-8')
        
        authorization_origin = f'api_key="{self.api_key}", algorithm="hmac-sha256", headers="host date request-line", signature="{signature_sha}"'
        authorization = base64.b64encode(authorization_origin.encode('utf-8')).decode(encoding='utf-8')
        
        # 将请求的鉴权参数组合为字典
        v = {
            "authorization": authorization,
            "date": now,
            "host": "ws-api.xfyun.cn"
        }
        
        # 拼接鉴权参数，生成url
        url = self.base_url + '?' + urllib.parse.urlencode(v)
        return url
    
    async def synthesize(self, text: str, voice: str = "xiaoyan") -> bytes:
        """
        语音合成
        
        Args:
            text: 要合成的文本
            voice: 声音类型 (xiaoyan-女声, aisjiuxu-男声, aisxping-女声)
        
        Returns:
            合成的音频数据 (PCM格式)
        """
        url = self._create_auth_url()
        
        # 构造请求数据
        data = {
            "common": {
                "app_id": self.app_id,
            },
            "business": {
                "aue": "raw",  # 音频编码，raw(生成wav)或lame(生成mp3)
                "auf": "audio/L16;rate=16000",  # 音频采样率
                "vcn": voice,  # 发音人
                "tte": "UTF8",  # 文本编码格式
                "speed": 50,   # 语速 [0-100]
                "volume": 80,  # 音量 [0-100]  
                "pitch": 50,   # 音调 [0-100]
                "bgs": 0,      # 背景音乐
            },
            "data": {
                "status": 2,  # 数据状态，固定为2
                "text": base64.b64encode(text.encode('utf-8')).decode('utf-8')
            }
        }
        
        audio_data = b""
        
        try:
            async with websockets.connect(url) as websocket:
                # 发送数据
                await websocket.send(json.dumps(data))
                
                # 接收数据
                while True:
                    try:
                        response = await websocket.recv()
                        result = json.loads(response)
                        
                        if result.get("code") != 0:
                            print(f"❌ 讯飞TTS错误: {result.get('message', '未知错误')}")
                            break
                        
                        # 获取音频数据
                        if "data" in result and "audio" in result["data"]:
                            audio_chunk = base64.b64decode(result["data"]["audio"])
                            audio_data += audio_chunk
                        
                        # 检查是否结束
                        if result.get("data", {}).get("status") == 2:
                            break
                            
                    except websockets.exceptions.ConnectionClosed:
                        break
                        
        except Exception as e:
            print(f"❌ 讯飞TTS连接失败: {e}")
            
        return audio_data


class XunfeiTTSLiveKit:
    """科大讯飞TTS的LiveKit适配器"""
    
    def __init__(self):
        self.app_id = os.getenv("XUNFEI_APP_ID")
        self.api_key = os.getenv("XUNFEI_API_KEY") 
        self.api_secret = os.getenv("XUNFEI_API_SECRET")
        
        if not all([self.app_id, self.api_key, self.api_secret]):
            raise ValueError("请设置讯飞TTS环境变量: XUNFEI_APP_ID, XUNFEI_API_KEY, XUNFEI_API_SECRET")
        
        self.tts = XunfeiTTS(self.app_id, self.api_key, self.api_secret)
        
    async def synthesize_text(self, text: str) -> bytes:
        """合成文本为语音"""
        return await self.tts.synthesize(text, voice="xiaoyan")  # 使用小燕女声


async def test_xunfei_tts():
    """测试科大讯飞TTS"""
    print("🧪 测试科大讯飞TTS")
    
    try:
        tts = XunfeiTTSLiveKit()
        
        test_text = "您好！我是张大师，专业的风水顾问。今天我将为您提供专业的风水咨询服务。"
        print(f"📝 测试文本: {test_text}")
        
        audio_data = await tts.synthesize_text(test_text)
        
        if audio_data:
            print(f"✅ 合成成功！音频长度: {len(audio_data)} 字节")
            
            # 保存测试音频文件
            with open("test_xunfei_tts.wav", "wb") as f:
                # 写入WAV文件头
                f.write(b'RIFF')
                f.write((len(audio_data) + 36).to_bytes(4, 'little'))
                f.write(b'WAVE')
                f.write(b'fmt ')
                f.write((16).to_bytes(4, 'little'))
                f.write((1).to_bytes(2, 'little'))  # PCM
                f.write((1).to_bytes(2, 'little'))  # 单声道
                f.write((16000).to_bytes(4, 'little'))  # 采样率
                f.write((32000).to_bytes(4, 'little'))  # 字节率
                f.write((2).to_bytes(2, 'little'))  # 块对齐
                f.write((16).to_bytes(2, 'little'))  # 位深度
                f.write(b'data')
                f.write(len(audio_data).to_bytes(4, 'little'))
                f.write(audio_data)
            
            print("🎵 测试音频已保存为 test_xunfei_tts.wav")
            return True
        else:
            print("❌ 合成失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


if __name__ == "__main__":
    asyncio.run(test_xunfei_tts())
