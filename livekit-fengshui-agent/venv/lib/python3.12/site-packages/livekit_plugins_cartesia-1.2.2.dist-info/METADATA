Metadata-Version: 2.4
Name: livekit-plugins-cartesia
Version: 1.2.2
Summary: LiveKit Agents Plugin for Cartesia
Project-URL: Documentation, https://docs.livekit.io
Project-URL: Website, https://livekit.io/
Project-URL: Source, https://github.com/livekit/agents
Author-email: LiveKit <<EMAIL>>
License-Expression: Apache-2.0
Keywords: audio,livekit,realtime,video,webrtc
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Topic :: Multimedia :: Sound/Audio
Classifier: Topic :: Multimedia :: Video
Classifier: Topic :: Scientific/Engineering :: Artificial Intelligence
Requires-Python: >=3.9.0
Requires-Dist: livekit-agents>=1.2.2
Description-Content-Type: text/markdown

# Cartesia plugin for LiveKit Agents

Support for [Cartesia](https://cartesia.ai/)'s voice AI services in LiveKit Agents.

More information is available in the docs for the [STT](https://docs.livekit.io/agents/integrations/stt/cartesia/) and [TTS](https://docs.livekit.io/agents/integrations/tts/cartesia/) integrations.

## Installation

```bash
pip install livekit-plugins-cartesia
```

## Pre-requisites

You'll need an API key from Cartesia. It can be set as an environment variable: `CARTESIA_API_KEY`
