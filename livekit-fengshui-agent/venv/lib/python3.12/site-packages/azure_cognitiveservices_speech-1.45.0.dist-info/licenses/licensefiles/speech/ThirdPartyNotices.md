NOTICES AND INFORMATION
Do Not Translate or Localize

This software incorporates material from third parties.
Microsoft makes certain open source code available at https://3rdpartysource.microsoft.com,
or you may send a check or money order for US $5.00, including the product name,
the open source component name, platform, and version number, to:

Source Code Compliance Team
Microsoft Corporation
One Microsoft Way
Redmond, WA 98052
USA

Notwithstanding any other terms, you may reverse engineer this software to the extent
required to debug changes to any libraries licensed under the GNU Lesser General Public License.

---------------------------------------------------------

com.fasterxml.jackson.core/jackson-annotations 2.13.4 - Apache-2.0



Apache License

Version 2.0, January 2004

http://www.apache.org/licenses/ TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION

   1. Definitions.

      

      "License" shall mean the terms and conditions for use, reproduction, and distribution as defined by Sections 1 through 9 of this document.

      

      "Licensor" shall mean the copyright owner or entity authorized by the copyright owner that is granting the License.

      

      "Legal Entity" shall mean the union of the acting entity and all other entities that control, are controlled by, or are under common control with that entity. For the purposes of this definition, "control" means (i) the power, direct or indirect, to cause the direction or management of such entity, whether by contract or otherwise, or (ii) ownership of fifty percent (50%) or more of the outstanding shares, or (iii) beneficial ownership of such entity.

      

      "You" (or "Your") shall mean an individual or Legal Entity exercising permissions granted by this License.

      

      "Source" form shall mean the preferred form for making modifications, including but not limited to software source code, documentation source, and configuration files.

      

      "Object" form shall mean any form resulting from mechanical transformation or translation of a Source form, including but not limited to compiled object code, generated documentation, and conversions to other media types.

      

      "Work" shall mean the work of authorship, whether in Source or Object form, made available under the License, as indicated by a copyright notice that is included in or attached to the work (an example is provided in the Appendix below).

      

      "Derivative Works" shall mean any work, whether in Source or Object form, that is based on (or derived from) the Work and for which the editorial revisions, annotations, elaborations, or other modifications represent, as a whole, an original work of authorship. For the purposes of this License, Derivative Works shall not include works that remain separable from, or merely link (or bind by name) to the interfaces of, the Work and Derivative Works thereof.

      

      "Contribution" shall mean any work of authorship, including the original version of the Work and any modifications or additions to that Work or Derivative Works thereof, that is intentionally submitted to Licensor for inclusion in the Work by the copyright owner or by an individual or Legal Entity authorized to submit on behalf of the copyright owner. For the purposes of this definition, "submitted" means any form of electronic, verbal, or written communication sent to the Licensor or its representatives, including but not limited to communication on electronic mailing lists, source code control systems, and issue tracking systems that are managed by, or on behalf of, the Licensor for the purpose of discussing and improving the Work, but excluding communication that is conspicuously marked or otherwise designated in writing by the copyright owner as "Not a Contribution."

      

      "Contributor" shall mean Licensor and any individual or Legal Entity on behalf of whom a Contribution has been received by Licensor and subsequently incorporated within the Work.

   2. Grant of Copyright License. Subject to the terms and conditions of this License, each Contributor hereby grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable copyright license to reproduce, prepare Derivative Works of, publicly display, publicly perform, sublicense, and distribute the Work and such Derivative Works in Source or Object form.

   3. Grant of Patent License. Subject to the terms and conditions of this License, each Contributor hereby grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable (except as stated in this section) patent license to make, have made, use, offer to sell, sell, import, and otherwise transfer the Work, where such license applies only to those patent claims licensable by such Contributor that are necessarily infringed by their Contribution(s) alone or by combination of their Contribution(s) with the Work to which such Contribution(s) was submitted. If You institute patent litigation against any entity (including a cross-claim or counterclaim in a lawsuit) alleging that the Work or a Contribution incorporated within the Work constitutes direct or contributory patent infringement, then any patent licenses granted to You under this License for that Work shall terminate as of the date such litigation is filed.

   4. Redistribution. You may reproduce and distribute copies of the Work or Derivative Works thereof in any medium, with or without modifications, and in Source or Object form, provided that You meet the following conditions:

      (a) You must give any other recipients of the Work or Derivative Works a copy of this License; and

      (b) You must cause any modified files to carry prominent notices stating that You changed the files; and

      (c) You must retain, in the Source form of any Derivative Works that You distribute, all copyright, patent, trademark, and attribution notices from the Source form of the Work, excluding those notices that do not pertain to any part of the Derivative Works; and

      (d) If the Work includes a "NOTICE" text file as part of its distribution, then any Derivative Works that You distribute must include a readable copy of the attribution notices contained within such NOTICE file, excluding those notices that do not pertain to any part of the Derivative Works, in at least one of the following places: within a NOTICE text file distributed as part of the Derivative Works; within the Source form or documentation, if provided along with the Derivative Works; or, within a display generated by the Derivative Works, if and wherever such third-party notices normally appear. The contents of the NOTICE file are for informational purposes only and do not modify the License. You may add Your own attribution notices within Derivative Works that You distribute, alongside or as an addendum to the NOTICE text from the Work, provided that such additional attribution notices cannot be construed as modifying the License.

      You may add Your own copyright statement to Your modifications and may provide additional or different license terms and conditions for use, reproduction, or distribution of Your modifications, or for any such Derivative Works as a whole, provided Your use, reproduction, and distribution of the Work otherwise complies with the conditions stated in this License.

   5. Submission of Contributions. Unless You explicitly state otherwise, any Contribution intentionally submitted for inclusion in the Work by You to the Licensor shall be under the terms and conditions of this License, without any additional terms or conditions. Notwithstanding the above, nothing herein shall supersede or modify the terms of any separate license agreement you may have executed with Licensor regarding such Contributions.

   6. Trademarks. This License does not grant permission to use the trade names, trademarks, service marks, or product names of the Licensor, except as required for reasonable and customary use in describing the origin of the Work and reproducing the content of the NOTICE file.

   7. Disclaimer of Warranty. Unless required by applicable law or agreed to in writing, Licensor provides the Work (and each Contributor provides its Contributions) on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied, including, without limitation, any warranties or conditions of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A PARTICULAR PURPOSE. You are solely responsible for determining the appropriateness of using or redistributing the Work and assume any risks associated with Your exercise of permissions under this License.

   8. Limitation of Liability. In no event and under no legal theory, whether in tort (including negligence), contract, or otherwise, unless required by applicable law (such as deliberate and grossly negligent acts) or agreed to in writing, shall any Contributor be liable to You for damages, including any direct, indirect, special, incidental, or consequential damages of any character arising as a result of this License or out of the use or inability to use the Work (including but not limited to damages for loss of goodwill, work stoppage, computer failure or malfunction, or any and all other commercial damages or losses), even if such Contributor has been advised of the possibility of such damages.

   9. Accepting Warranty or Additional Liability. While redistributing the Work or Derivative Works thereof, You may choose to offer, and charge a fee for, acceptance of support, warranty, indemnity, or other liability obligations and/or rights consistent with this License. However, in accepting such obligations, You may act only on Your own behalf and on Your sole responsibility, not on behalf of any other Contributor, and only if You agree to indemnify, defend, and hold each Contributor harmless for any liability incurred by, or claims asserted against, such Contributor by reason of your accepting any such warranty or additional liability. END OF TERMS AND CONDITIONS

APPENDIX: How to apply the Apache License to your work.

To apply the Apache License to your work, attach the following boilerplate notice, with the fields enclosed by brackets "[]" replaced with your own identifying information. (Don't include the brackets!) The text should be enclosed in the appropriate comment syntax for the file format. We also recommend that a file or class name and description of purpose be included on the same "printed page" as the copyright notice for easier identification within third-party archives.

Copyright [yyyy] [name of copyright owner]

Licensed under the Apache License, Version 2.0 (the "License");

you may not use this file except in compliance with the License.

You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software

distributed under the License is distributed on an "AS IS" BASIS,

WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

See the License for the specific language governing permissions and

limitations under the License.

---------------------------------------------------------

---------------------------------------------------------

com.fasterxml.jackson.core/jackson-core 2.13.4 - Apache-2.0



Apache License

Version 2.0, January 2004

http://www.apache.org/licenses/ TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION

   1. Definitions.

      

      "License" shall mean the terms and conditions for use, reproduction, and distribution as defined by Sections 1 through 9 of this document.

      

      "Licensor" shall mean the copyright owner or entity authorized by the copyright owner that is granting the License.

      

      "Legal Entity" shall mean the union of the acting entity and all other entities that control, are controlled by, or are under common control with that entity. For the purposes of this definition, "control" means (i) the power, direct or indirect, to cause the direction or management of such entity, whether by contract or otherwise, or (ii) ownership of fifty percent (50%) or more of the outstanding shares, or (iii) beneficial ownership of such entity.

      

      "You" (or "Your") shall mean an individual or Legal Entity exercising permissions granted by this License.

      

      "Source" form shall mean the preferred form for making modifications, including but not limited to software source code, documentation source, and configuration files.

      

      "Object" form shall mean any form resulting from mechanical transformation or translation of a Source form, including but not limited to compiled object code, generated documentation, and conversions to other media types.

      

      "Work" shall mean the work of authorship, whether in Source or Object form, made available under the License, as indicated by a copyright notice that is included in or attached to the work (an example is provided in the Appendix below).

      

      "Derivative Works" shall mean any work, whether in Source or Object form, that is based on (or derived from) the Work and for which the editorial revisions, annotations, elaborations, or other modifications represent, as a whole, an original work of authorship. For the purposes of this License, Derivative Works shall not include works that remain separable from, or merely link (or bind by name) to the interfaces of, the Work and Derivative Works thereof.

      

      "Contribution" shall mean any work of authorship, including the original version of the Work and any modifications or additions to that Work or Derivative Works thereof, that is intentionally submitted to Licensor for inclusion in the Work by the copyright owner or by an individual or Legal Entity authorized to submit on behalf of the copyright owner. For the purposes of this definition, "submitted" means any form of electronic, verbal, or written communication sent to the Licensor or its representatives, including but not limited to communication on electronic mailing lists, source code control systems, and issue tracking systems that are managed by, or on behalf of, the Licensor for the purpose of discussing and improving the Work, but excluding communication that is conspicuously marked or otherwise designated in writing by the copyright owner as "Not a Contribution."

      

      "Contributor" shall mean Licensor and any individual or Legal Entity on behalf of whom a Contribution has been received by Licensor and subsequently incorporated within the Work.

   2. Grant of Copyright License. Subject to the terms and conditions of this License, each Contributor hereby grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable copyright license to reproduce, prepare Derivative Works of, publicly display, publicly perform, sublicense, and distribute the Work and such Derivative Works in Source or Object form.

   3. Grant of Patent License. Subject to the terms and conditions of this License, each Contributor hereby grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable (except as stated in this section) patent license to make, have made, use, offer to sell, sell, import, and otherwise transfer the Work, where such license applies only to those patent claims licensable by such Contributor that are necessarily infringed by their Contribution(s) alone or by combination of their Contribution(s) with the Work to which such Contribution(s) was submitted. If You institute patent litigation against any entity (including a cross-claim or counterclaim in a lawsuit) alleging that the Work or a Contribution incorporated within the Work constitutes direct or contributory patent infringement, then any patent licenses granted to You under this License for that Work shall terminate as of the date such litigation is filed.

   4. Redistribution. You may reproduce and distribute copies of the Work or Derivative Works thereof in any medium, with or without modifications, and in Source or Object form, provided that You meet the following conditions:

      (a) You must give any other recipients of the Work or Derivative Works a copy of this License; and

      (b) You must cause any modified files to carry prominent notices stating that You changed the files; and

      (c) You must retain, in the Source form of any Derivative Works that You distribute, all copyright, patent, trademark, and attribution notices from the Source form of the Work, excluding those notices that do not pertain to any part of the Derivative Works; and

      (d) If the Work includes a "NOTICE" text file as part of its distribution, then any Derivative Works that You distribute must include a readable copy of the attribution notices contained within such NOTICE file, excluding those notices that do not pertain to any part of the Derivative Works, in at least one of the following places: within a NOTICE text file distributed as part of the Derivative Works; within the Source form or documentation, if provided along with the Derivative Works; or, within a display generated by the Derivative Works, if and wherever such third-party notices normally appear. The contents of the NOTICE file are for informational purposes only and do not modify the License. You may add Your own attribution notices within Derivative Works that You distribute, alongside or as an addendum to the NOTICE text from the Work, provided that such additional attribution notices cannot be construed as modifying the License.

      You may add Your own copyright statement to Your modifications and may provide additional or different license terms and conditions for use, reproduction, or distribution of Your modifications, or for any such Derivative Works as a whole, provided Your use, reproduction, and distribution of the Work otherwise complies with the conditions stated in this License.

   5. Submission of Contributions. Unless You explicitly state otherwise, any Contribution intentionally submitted for inclusion in the Work by You to the Licensor shall be under the terms and conditions of this License, without any additional terms or conditions. Notwithstanding the above, nothing herein shall supersede or modify the terms of any separate license agreement you may have executed with Licensor regarding such Contributions.

   6. Trademarks. This License does not grant permission to use the trade names, trademarks, service marks, or product names of the Licensor, except as required for reasonable and customary use in describing the origin of the Work and reproducing the content of the NOTICE file.

   7. Disclaimer of Warranty. Unless required by applicable law or agreed to in writing, Licensor provides the Work (and each Contributor provides its Contributions) on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied, including, without limitation, any warranties or conditions of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A PARTICULAR PURPOSE. You are solely responsible for determining the appropriateness of using or redistributing the Work and assume any risks associated with Your exercise of permissions under this License.

   8. Limitation of Liability. In no event and under no legal theory, whether in tort (including negligence), contract, or otherwise, unless required by applicable law (such as deliberate and grossly negligent acts) or agreed to in writing, shall any Contributor be liable to You for damages, including any direct, indirect, special, incidental, or consequential damages of any character arising as a result of this License or out of the use or inability to use the Work (including but not limited to damages for loss of goodwill, work stoppage, computer failure or malfunction, or any and all other commercial damages or losses), even if such Contributor has been advised of the possibility of such damages.

   9. Accepting Warranty or Additional Liability. While redistributing the Work or Derivative Works thereof, You may choose to offer, and charge a fee for, acceptance of support, warranty, indemnity, or other liability obligations and/or rights consistent with this License. However, in accepting such obligations, You may act only on Your own behalf and on Your sole responsibility, not on behalf of any other Contributor, and only if You agree to indemnify, defend, and hold each Contributor harmless for any liability incurred by, or claims asserted against, such Contributor by reason of your accepting any such warranty or additional liability. END OF TERMS AND CONDITIONS

APPENDIX: How to apply the Apache License to your work.

To apply the Apache License to your work, attach the following boilerplate notice, with the fields enclosed by brackets "[]" replaced with your own identifying information. (Don't include the brackets!) The text should be enclosed in the appropriate comment syntax for the file format. We also recommend that a file or class name and description of purpose be included on the same "printed page" as the copyright notice for easier identification within third-party archives.

Copyright [yyyy] [name of copyright owner]

Licensed under the Apache License, Version 2.0 (the "License");

you may not use this file except in compliance with the License.

You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software

distributed under the License is distributed on an "AS IS" BASIS,

WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

See the License for the specific language governing permissions and

limitations under the License.

---------------------------------------------------------

---------------------------------------------------------

com.fasterxml.jackson.core/jackson-databind ******** - Apache-2.0



Apache License

Version 2.0, January 2004

http://www.apache.org/licenses/ TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION

   1. Definitions.

      

      "License" shall mean the terms and conditions for use, reproduction, and distribution as defined by Sections 1 through 9 of this document.

      

      "Licensor" shall mean the copyright owner or entity authorized by the copyright owner that is granting the License.

      

      "Legal Entity" shall mean the union of the acting entity and all other entities that control, are controlled by, or are under common control with that entity. For the purposes of this definition, "control" means (i) the power, direct or indirect, to cause the direction or management of such entity, whether by contract or otherwise, or (ii) ownership of fifty percent (50%) or more of the outstanding shares, or (iii) beneficial ownership of such entity.

      

      "You" (or "Your") shall mean an individual or Legal Entity exercising permissions granted by this License.

      

      "Source" form shall mean the preferred form for making modifications, including but not limited to software source code, documentation source, and configuration files.

      

      "Object" form shall mean any form resulting from mechanical transformation or translation of a Source form, including but not limited to compiled object code, generated documentation, and conversions to other media types.

      

      "Work" shall mean the work of authorship, whether in Source or Object form, made available under the License, as indicated by a copyright notice that is included in or attached to the work (an example is provided in the Appendix below).

      

      "Derivative Works" shall mean any work, whether in Source or Object form, that is based on (or derived from) the Work and for which the editorial revisions, annotations, elaborations, or other modifications represent, as a whole, an original work of authorship. For the purposes of this License, Derivative Works shall not include works that remain separable from, or merely link (or bind by name) to the interfaces of, the Work and Derivative Works thereof.

      

      "Contribution" shall mean any work of authorship, including the original version of the Work and any modifications or additions to that Work or Derivative Works thereof, that is intentionally submitted to Licensor for inclusion in the Work by the copyright owner or by an individual or Legal Entity authorized to submit on behalf of the copyright owner. For the purposes of this definition, "submitted" means any form of electronic, verbal, or written communication sent to the Licensor or its representatives, including but not limited to communication on electronic mailing lists, source code control systems, and issue tracking systems that are managed by, or on behalf of, the Licensor for the purpose of discussing and improving the Work, but excluding communication that is conspicuously marked or otherwise designated in writing by the copyright owner as "Not a Contribution."

      

      "Contributor" shall mean Licensor and any individual or Legal Entity on behalf of whom a Contribution has been received by Licensor and subsequently incorporated within the Work.

   2. Grant of Copyright License. Subject to the terms and conditions of this License, each Contributor hereby grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable copyright license to reproduce, prepare Derivative Works of, publicly display, publicly perform, sublicense, and distribute the Work and such Derivative Works in Source or Object form.

   3. Grant of Patent License. Subject to the terms and conditions of this License, each Contributor hereby grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable (except as stated in this section) patent license to make, have made, use, offer to sell, sell, import, and otherwise transfer the Work, where such license applies only to those patent claims licensable by such Contributor that are necessarily infringed by their Contribution(s) alone or by combination of their Contribution(s) with the Work to which such Contribution(s) was submitted. If You institute patent litigation against any entity (including a cross-claim or counterclaim in a lawsuit) alleging that the Work or a Contribution incorporated within the Work constitutes direct or contributory patent infringement, then any patent licenses granted to You under this License for that Work shall terminate as of the date such litigation is filed.

   4. Redistribution. You may reproduce and distribute copies of the Work or Derivative Works thereof in any medium, with or without modifications, and in Source or Object form, provided that You meet the following conditions:

      (a) You must give any other recipients of the Work or Derivative Works a copy of this License; and

      (b) You must cause any modified files to carry prominent notices stating that You changed the files; and

      (c) You must retain, in the Source form of any Derivative Works that You distribute, all copyright, patent, trademark, and attribution notices from the Source form of the Work, excluding those notices that do not pertain to any part of the Derivative Works; and

      (d) If the Work includes a "NOTICE" text file as part of its distribution, then any Derivative Works that You distribute must include a readable copy of the attribution notices contained within such NOTICE file, excluding those notices that do not pertain to any part of the Derivative Works, in at least one of the following places: within a NOTICE text file distributed as part of the Derivative Works; within the Source form or documentation, if provided along with the Derivative Works; or, within a display generated by the Derivative Works, if and wherever such third-party notices normally appear. The contents of the NOTICE file are for informational purposes only and do not modify the License. You may add Your own attribution notices within Derivative Works that You distribute, alongside or as an addendum to the NOTICE text from the Work, provided that such additional attribution notices cannot be construed as modifying the License.

      You may add Your own copyright statement to Your modifications and may provide additional or different license terms and conditions for use, reproduction, or distribution of Your modifications, or for any such Derivative Works as a whole, provided Your use, reproduction, and distribution of the Work otherwise complies with the conditions stated in this License.

   5. Submission of Contributions. Unless You explicitly state otherwise, any Contribution intentionally submitted for inclusion in the Work by You to the Licensor shall be under the terms and conditions of this License, without any additional terms or conditions. Notwithstanding the above, nothing herein shall supersede or modify the terms of any separate license agreement you may have executed with Licensor regarding such Contributions.

   6. Trademarks. This License does not grant permission to use the trade names, trademarks, service marks, or product names of the Licensor, except as required for reasonable and customary use in describing the origin of the Work and reproducing the content of the NOTICE file.

   7. Disclaimer of Warranty. Unless required by applicable law or agreed to in writing, Licensor provides the Work (and each Contributor provides its Contributions) on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied, including, without limitation, any warranties or conditions of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A PARTICULAR PURPOSE. You are solely responsible for determining the appropriateness of using or redistributing the Work and assume any risks associated with Your exercise of permissions under this License.

   8. Limitation of Liability. In no event and under no legal theory, whether in tort (including negligence), contract, or otherwise, unless required by applicable law (such as deliberate and grossly negligent acts) or agreed to in writing, shall any Contributor be liable to You for damages, including any direct, indirect, special, incidental, or consequential damages of any character arising as a result of this License or out of the use or inability to use the Work (including but not limited to damages for loss of goodwill, work stoppage, computer failure or malfunction, or any and all other commercial damages or losses), even if such Contributor has been advised of the possibility of such damages.

   9. Accepting Warranty or Additional Liability. While redistributing the Work or Derivative Works thereof, You may choose to offer, and charge a fee for, acceptance of support, warranty, indemnity, or other liability obligations and/or rights consistent with this License. However, in accepting such obligations, You may act only on Your own behalf and on Your sole responsibility, not on behalf of any other Contributor, and only if You agree to indemnify, defend, and hold each Contributor harmless for any liability incurred by, or claims asserted against, such Contributor by reason of your accepting any such warranty or additional liability. END OF TERMS AND CONDITIONS

APPENDIX: How to apply the Apache License to your work.

To apply the Apache License to your work, attach the following boilerplate notice, with the fields enclosed by brackets "[]" replaced with your own identifying information. (Don't include the brackets!) The text should be enclosed in the appropriate comment syntax for the file format. We also recommend that a file or class name and description of purpose be included on the same "printed page" as the copyright notice for easier identification within third-party archives.

Copyright [yyyy] [name of copyright owner]

Licensed under the Apache License, Version 2.0 (the "License");

you may not use this file except in compliance with the License.

You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software

distributed under the License is distributed on an "AS IS" BASIS,

WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

See the License for the specific language governing permissions and

limitations under the License.

---------------------------------------------------------

---------------------------------------------------------

com.fasterxml.jackson.datatype/jackson-datatype-joda 2.13.4 - Apache-2.0



Apache License

Version 2.0, January 2004

http://www.apache.org/licenses/ TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION

   1. Definitions.

      

      "License" shall mean the terms and conditions for use, reproduction, and distribution as defined by Sections 1 through 9 of this document.

      

      "Licensor" shall mean the copyright owner or entity authorized by the copyright owner that is granting the License.

      

      "Legal Entity" shall mean the union of the acting entity and all other entities that control, are controlled by, or are under common control with that entity. For the purposes of this definition, "control" means (i) the power, direct or indirect, to cause the direction or management of such entity, whether by contract or otherwise, or (ii) ownership of fifty percent (50%) or more of the outstanding shares, or (iii) beneficial ownership of such entity.

      

      "You" (or "Your") shall mean an individual or Legal Entity exercising permissions granted by this License.

      

      "Source" form shall mean the preferred form for making modifications, including but not limited to software source code, documentation source, and configuration files.

      

      "Object" form shall mean any form resulting from mechanical transformation or translation of a Source form, including but not limited to compiled object code, generated documentation, and conversions to other media types.

      

      "Work" shall mean the work of authorship, whether in Source or Object form, made available under the License, as indicated by a copyright notice that is included in or attached to the work (an example is provided in the Appendix below).

      

      "Derivative Works" shall mean any work, whether in Source or Object form, that is based on (or derived from) the Work and for which the editorial revisions, annotations, elaborations, or other modifications represent, as a whole, an original work of authorship. For the purposes of this License, Derivative Works shall not include works that remain separable from, or merely link (or bind by name) to the interfaces of, the Work and Derivative Works thereof.

      

      "Contribution" shall mean any work of authorship, including the original version of the Work and any modifications or additions to that Work or Derivative Works thereof, that is intentionally submitted to Licensor for inclusion in the Work by the copyright owner or by an individual or Legal Entity authorized to submit on behalf of the copyright owner. For the purposes of this definition, "submitted" means any form of electronic, verbal, or written communication sent to the Licensor or its representatives, including but not limited to communication on electronic mailing lists, source code control systems, and issue tracking systems that are managed by, or on behalf of, the Licensor for the purpose of discussing and improving the Work, but excluding communication that is conspicuously marked or otherwise designated in writing by the copyright owner as "Not a Contribution."

      

      "Contributor" shall mean Licensor and any individual or Legal Entity on behalf of whom a Contribution has been received by Licensor and subsequently incorporated within the Work.

   2. Grant of Copyright License. Subject to the terms and conditions of this License, each Contributor hereby grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable copyright license to reproduce, prepare Derivative Works of, publicly display, publicly perform, sublicense, and distribute the Work and such Derivative Works in Source or Object form.

   3. Grant of Patent License. Subject to the terms and conditions of this License, each Contributor hereby grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable (except as stated in this section) patent license to make, have made, use, offer to sell, sell, import, and otherwise transfer the Work, where such license applies only to those patent claims licensable by such Contributor that are necessarily infringed by their Contribution(s) alone or by combination of their Contribution(s) with the Work to which such Contribution(s) was submitted. If You institute patent litigation against any entity (including a cross-claim or counterclaim in a lawsuit) alleging that the Work or a Contribution incorporated within the Work constitutes direct or contributory patent infringement, then any patent licenses granted to You under this License for that Work shall terminate as of the date such litigation is filed.

   4. Redistribution. You may reproduce and distribute copies of the Work or Derivative Works thereof in any medium, with or without modifications, and in Source or Object form, provided that You meet the following conditions:

      (a) You must give any other recipients of the Work or Derivative Works a copy of this License; and

      (b) You must cause any modified files to carry prominent notices stating that You changed the files; and

      (c) You must retain, in the Source form of any Derivative Works that You distribute, all copyright, patent, trademark, and attribution notices from the Source form of the Work, excluding those notices that do not pertain to any part of the Derivative Works; and

      (d) If the Work includes a "NOTICE" text file as part of its distribution, then any Derivative Works that You distribute must include a readable copy of the attribution notices contained within such NOTICE file, excluding those notices that do not pertain to any part of the Derivative Works, in at least one of the following places: within a NOTICE text file distributed as part of the Derivative Works; within the Source form or documentation, if provided along with the Derivative Works; or, within a display generated by the Derivative Works, if and wherever such third-party notices normally appear. The contents of the NOTICE file are for informational purposes only and do not modify the License. You may add Your own attribution notices within Derivative Works that You distribute, alongside or as an addendum to the NOTICE text from the Work, provided that such additional attribution notices cannot be construed as modifying the License.

      You may add Your own copyright statement to Your modifications and may provide additional or different license terms and conditions for use, reproduction, or distribution of Your modifications, or for any such Derivative Works as a whole, provided Your use, reproduction, and distribution of the Work otherwise complies with the conditions stated in this License.

   5. Submission of Contributions. Unless You explicitly state otherwise, any Contribution intentionally submitted for inclusion in the Work by You to the Licensor shall be under the terms and conditions of this License, without any additional terms or conditions. Notwithstanding the above, nothing herein shall supersede or modify the terms of any separate license agreement you may have executed with Licensor regarding such Contributions.

   6. Trademarks. This License does not grant permission to use the trade names, trademarks, service marks, or product names of the Licensor, except as required for reasonable and customary use in describing the origin of the Work and reproducing the content of the NOTICE file.

   7. Disclaimer of Warranty. Unless required by applicable law or agreed to in writing, Licensor provides the Work (and each Contributor provides its Contributions) on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied, including, without limitation, any warranties or conditions of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A PARTICULAR PURPOSE. You are solely responsible for determining the appropriateness of using or redistributing the Work and assume any risks associated with Your exercise of permissions under this License.

   8. Limitation of Liability. In no event and under no legal theory, whether in tort (including negligence), contract, or otherwise, unless required by applicable law (such as deliberate and grossly negligent acts) or agreed to in writing, shall any Contributor be liable to You for damages, including any direct, indirect, special, incidental, or consequential damages of any character arising as a result of this License or out of the use or inability to use the Work (including but not limited to damages for loss of goodwill, work stoppage, computer failure or malfunction, or any and all other commercial damages or losses), even if such Contributor has been advised of the possibility of such damages.

   9. Accepting Warranty or Additional Liability. While redistributing the Work or Derivative Works thereof, You may choose to offer, and charge a fee for, acceptance of support, warranty, indemnity, or other liability obligations and/or rights consistent with this License. However, in accepting such obligations, You may act only on Your own behalf and on Your sole responsibility, not on behalf of any other Contributor, and only if You agree to indemnify, defend, and hold each Contributor harmless for any liability incurred by, or claims asserted against, such Contributor by reason of your accepting any such warranty or additional liability. END OF TERMS AND CONDITIONS

APPENDIX: How to apply the Apache License to your work.

To apply the Apache License to your work, attach the following boilerplate notice, with the fields enclosed by brackets "[]" replaced with your own identifying information. (Don't include the brackets!) The text should be enclosed in the appropriate comment syntax for the file format. We also recommend that a file or class name and description of purpose be included on the same "printed page" as the copyright notice for easier identification within third-party archives.

Copyright [yyyy] [name of copyright owner]

Licensed under the Apache License, Version 2.0 (the "License");

you may not use this file except in compliance with the License.

You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software

distributed under the License is distributed on an "AS IS" BASIS,

WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

See the License for the specific language governing permissions and

limitations under the License.

---------------------------------------------------------

---------------------------------------------------------

com.github.stephenc.jcip/jcip-annotations 1.0-1 - Apache-2.0


Copyright 2013 Stephen Connolly

Apache License

Version 2.0, January 2004

http://www.apache.org/licenses/ TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION

   1. Definitions.

      

      "License" shall mean the terms and conditions for use, reproduction, and distribution as defined by Sections 1 through 9 of this document.

      

      "Licensor" shall mean the copyright owner or entity authorized by the copyright owner that is granting the License.

      

      "Legal Entity" shall mean the union of the acting entity and all other entities that control, are controlled by, or are under common control with that entity. For the purposes of this definition, "control" means (i) the power, direct or indirect, to cause the direction or management of such entity, whether by contract or otherwise, or (ii) ownership of fifty percent (50%) or more of the outstanding shares, or (iii) beneficial ownership of such entity.

      

      "You" (or "Your") shall mean an individual or Legal Entity exercising permissions granted by this License.

      

      "Source" form shall mean the preferred form for making modifications, including but not limited to software source code, documentation source, and configuration files.

      

      "Object" form shall mean any form resulting from mechanical transformation or translation of a Source form, including but not limited to compiled object code, generated documentation, and conversions to other media types.

      

      "Work" shall mean the work of authorship, whether in Source or Object form, made available under the License, as indicated by a copyright notice that is included in or attached to the work (an example is provided in the Appendix below).

      

      "Derivative Works" shall mean any work, whether in Source or Object form, that is based on (or derived from) the Work and for which the editorial revisions, annotations, elaborations, or other modifications represent, as a whole, an original work of authorship. For the purposes of this License, Derivative Works shall not include works that remain separable from, or merely link (or bind by name) to the interfaces of, the Work and Derivative Works thereof.

      

      "Contribution" shall mean any work of authorship, including the original version of the Work and any modifications or additions to that Work or Derivative Works thereof, that is intentionally submitted to Licensor for inclusion in the Work by the copyright owner or by an individual or Legal Entity authorized to submit on behalf of the copyright owner. For the purposes of this definition, "submitted" means any form of electronic, verbal, or written communication sent to the Licensor or its representatives, including but not limited to communication on electronic mailing lists, source code control systems, and issue tracking systems that are managed by, or on behalf of, the Licensor for the purpose of discussing and improving the Work, but excluding communication that is conspicuously marked or otherwise designated in writing by the copyright owner as "Not a Contribution."

      

      "Contributor" shall mean Licensor and any individual or Legal Entity on behalf of whom a Contribution has been received by Licensor and subsequently incorporated within the Work.

   2. Grant of Copyright License. Subject to the terms and conditions of this License, each Contributor hereby grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable copyright license to reproduce, prepare Derivative Works of, publicly display, publicly perform, sublicense, and distribute the Work and such Derivative Works in Source or Object form.

   3. Grant of Patent License. Subject to the terms and conditions of this License, each Contributor hereby grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable (except as stated in this section) patent license to make, have made, use, offer to sell, sell, import, and otherwise transfer the Work, where such license applies only to those patent claims licensable by such Contributor that are necessarily infringed by their Contribution(s) alone or by combination of their Contribution(s) with the Work to which such Contribution(s) was submitted. If You institute patent litigation against any entity (including a cross-claim or counterclaim in a lawsuit) alleging that the Work or a Contribution incorporated within the Work constitutes direct or contributory patent infringement, then any patent licenses granted to You under this License for that Work shall terminate as of the date such litigation is filed.

   4. Redistribution. You may reproduce and distribute copies of the Work or Derivative Works thereof in any medium, with or without modifications, and in Source or Object form, provided that You meet the following conditions:

      (a) You must give any other recipients of the Work or Derivative Works a copy of this License; and

      (b) You must cause any modified files to carry prominent notices stating that You changed the files; and

      (c) You must retain, in the Source form of any Derivative Works that You distribute, all copyright, patent, trademark, and attribution notices from the Source form of the Work, excluding those notices that do not pertain to any part of the Derivative Works; and

      (d) If the Work includes a "NOTICE" text file as part of its distribution, then any Derivative Works that You distribute must include a readable copy of the attribution notices contained within such NOTICE file, excluding those notices that do not pertain to any part of the Derivative Works, in at least one of the following places: within a NOTICE text file distributed as part of the Derivative Works; within the Source form or documentation, if provided along with the Derivative Works; or, within a display generated by the Derivative Works, if and wherever such third-party notices normally appear. The contents of the NOTICE file are for informational purposes only and do not modify the License. You may add Your own attribution notices within Derivative Works that You distribute, alongside or as an addendum to the NOTICE text from the Work, provided that such additional attribution notices cannot be construed as modifying the License.

      You may add Your own copyright statement to Your modifications and may provide additional or different license terms and conditions for use, reproduction, or distribution of Your modifications, or for any such Derivative Works as a whole, provided Your use, reproduction, and distribution of the Work otherwise complies with the conditions stated in this License.

   5. Submission of Contributions. Unless You explicitly state otherwise, any Contribution intentionally submitted for inclusion in the Work by You to the Licensor shall be under the terms and conditions of this License, without any additional terms or conditions. Notwithstanding the above, nothing herein shall supersede or modify the terms of any separate license agreement you may have executed with Licensor regarding such Contributions.

   6. Trademarks. This License does not grant permission to use the trade names, trademarks, service marks, or product names of the Licensor, except as required for reasonable and customary use in describing the origin of the Work and reproducing the content of the NOTICE file.

   7. Disclaimer of Warranty. Unless required by applicable law or agreed to in writing, Licensor provides the Work (and each Contributor provides its Contributions) on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied, including, without limitation, any warranties or conditions of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A PARTICULAR PURPOSE. You are solely responsible for determining the appropriateness of using or redistributing the Work and assume any risks associated with Your exercise of permissions under this License.

   8. Limitation of Liability. In no event and under no legal theory, whether in tort (including negligence), contract, or otherwise, unless required by applicable law (such as deliberate and grossly negligent acts) or agreed to in writing, shall any Contributor be liable to You for damages, including any direct, indirect, special, incidental, or consequential damages of any character arising as a result of this License or out of the use or inability to use the Work (including but not limited to damages for loss of goodwill, work stoppage, computer failure or malfunction, or any and all other commercial damages or losses), even if such Contributor has been advised of the possibility of such damages.

   9. Accepting Warranty or Additional Liability. While redistributing the Work or Derivative Works thereof, You may choose to offer, and charge a fee for, acceptance of support, warranty, indemnity, or other liability obligations and/or rights consistent with this License. However, in accepting such obligations, You may act only on Your own behalf and on Your sole responsibility, not on behalf of any other Contributor, and only if You agree to indemnify, defend, and hold each Contributor harmless for any liability incurred by, or claims asserted against, such Contributor by reason of your accepting any such warranty or additional liability. END OF TERMS AND CONDITIONS

APPENDIX: How to apply the Apache License to your work.

To apply the Apache License to your work, attach the following boilerplate notice, with the fields enclosed by brackets "[]" replaced with your own identifying information. (Don't include the brackets!) The text should be enclosed in the appropriate comment syntax for the file format. We also recommend that a file or class name and description of purpose be included on the same "printed page" as the copyright notice for easier identification within third-party archives.

Copyright [yyyy] [name of copyright owner]

Licensed under the Apache License, Version 2.0 (the "License");

you may not use this file except in compliance with the License.

You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software

distributed under the License is distributed on an "AS IS" BASIS,

WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

See the License for the specific language governing permissions and

limitations under the License.

---------------------------------------------------------

---------------------------------------------------------

com.google.code.gson/gson 2.8.9 - Apache-2.0



Apache License

Version 2.0, January 2004

http://www.apache.org/licenses/ TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION

   1. Definitions.

      

      "License" shall mean the terms and conditions for use, reproduction, and distribution as defined by Sections 1 through 9 of this document.

      

      "Licensor" shall mean the copyright owner or entity authorized by the copyright owner that is granting the License.

      

      "Legal Entity" shall mean the union of the acting entity and all other entities that control, are controlled by, or are under common control with that entity. For the purposes of this definition, "control" means (i) the power, direct or indirect, to cause the direction or management of such entity, whether by contract or otherwise, or (ii) ownership of fifty percent (50%) or more of the outstanding shares, or (iii) beneficial ownership of such entity.

      

      "You" (or "Your") shall mean an individual or Legal Entity exercising permissions granted by this License.

      

      "Source" form shall mean the preferred form for making modifications, including but not limited to software source code, documentation source, and configuration files.

      

      "Object" form shall mean any form resulting from mechanical transformation or translation of a Source form, including but not limited to compiled object code, generated documentation, and conversions to other media types.

      

      "Work" shall mean the work of authorship, whether in Source or Object form, made available under the License, as indicated by a copyright notice that is included in or attached to the work (an example is provided in the Appendix below).

      

      "Derivative Works" shall mean any work, whether in Source or Object form, that is based on (or derived from) the Work and for which the editorial revisions, annotations, elaborations, or other modifications represent, as a whole, an original work of authorship. For the purposes of this License, Derivative Works shall not include works that remain separable from, or merely link (or bind by name) to the interfaces of, the Work and Derivative Works thereof.

      

      "Contribution" shall mean any work of authorship, including the original version of the Work and any modifications or additions to that Work or Derivative Works thereof, that is intentionally submitted to Licensor for inclusion in the Work by the copyright owner or by an individual or Legal Entity authorized to submit on behalf of the copyright owner. For the purposes of this definition, "submitted" means any form of electronic, verbal, or written communication sent to the Licensor or its representatives, including but not limited to communication on electronic mailing lists, source code control systems, and issue tracking systems that are managed by, or on behalf of, the Licensor for the purpose of discussing and improving the Work, but excluding communication that is conspicuously marked or otherwise designated in writing by the copyright owner as "Not a Contribution."

      

      "Contributor" shall mean Licensor and any individual or Legal Entity on behalf of whom a Contribution has been received by Licensor and subsequently incorporated within the Work.

   2. Grant of Copyright License. Subject to the terms and conditions of this License, each Contributor hereby grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable copyright license to reproduce, prepare Derivative Works of, publicly display, publicly perform, sublicense, and distribute the Work and such Derivative Works in Source or Object form.

   3. Grant of Patent License. Subject to the terms and conditions of this License, each Contributor hereby grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable (except as stated in this section) patent license to make, have made, use, offer to sell, sell, import, and otherwise transfer the Work, where such license applies only to those patent claims licensable by such Contributor that are necessarily infringed by their Contribution(s) alone or by combination of their Contribution(s) with the Work to which such Contribution(s) was submitted. If You institute patent litigation against any entity (including a cross-claim or counterclaim in a lawsuit) alleging that the Work or a Contribution incorporated within the Work constitutes direct or contributory patent infringement, then any patent licenses granted to You under this License for that Work shall terminate as of the date such litigation is filed.

   4. Redistribution. You may reproduce and distribute copies of the Work or Derivative Works thereof in any medium, with or without modifications, and in Source or Object form, provided that You meet the following conditions:

      (a) You must give any other recipients of the Work or Derivative Works a copy of this License; and

      (b) You must cause any modified files to carry prominent notices stating that You changed the files; and

      (c) You must retain, in the Source form of any Derivative Works that You distribute, all copyright, patent, trademark, and attribution notices from the Source form of the Work, excluding those notices that do not pertain to any part of the Derivative Works; and

      (d) If the Work includes a "NOTICE" text file as part of its distribution, then any Derivative Works that You distribute must include a readable copy of the attribution notices contained within such NOTICE file, excluding those notices that do not pertain to any part of the Derivative Works, in at least one of the following places: within a NOTICE text file distributed as part of the Derivative Works; within the Source form or documentation, if provided along with the Derivative Works; or, within a display generated by the Derivative Works, if and wherever such third-party notices normally appear. The contents of the NOTICE file are for informational purposes only and do not modify the License. You may add Your own attribution notices within Derivative Works that You distribute, alongside or as an addendum to the NOTICE text from the Work, provided that such additional attribution notices cannot be construed as modifying the License.

      You may add Your own copyright statement to Your modifications and may provide additional or different license terms and conditions for use, reproduction, or distribution of Your modifications, or for any such Derivative Works as a whole, provided Your use, reproduction, and distribution of the Work otherwise complies with the conditions stated in this License.

   5. Submission of Contributions. Unless You explicitly state otherwise, any Contribution intentionally submitted for inclusion in the Work by You to the Licensor shall be under the terms and conditions of this License, without any additional terms or conditions. Notwithstanding the above, nothing herein shall supersede or modify the terms of any separate license agreement you may have executed with Licensor regarding such Contributions.

   6. Trademarks. This License does not grant permission to use the trade names, trademarks, service marks, or product names of the Licensor, except as required for reasonable and customary use in describing the origin of the Work and reproducing the content of the NOTICE file.

   7. Disclaimer of Warranty. Unless required by applicable law or agreed to in writing, Licensor provides the Work (and each Contributor provides its Contributions) on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied, including, without limitation, any warranties or conditions of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A PARTICULAR PURPOSE. You are solely responsible for determining the appropriateness of using or redistributing the Work and assume any risks associated with Your exercise of permissions under this License.

   8. Limitation of Liability. In no event and under no legal theory, whether in tort (including negligence), contract, or otherwise, unless required by applicable law (such as deliberate and grossly negligent acts) or agreed to in writing, shall any Contributor be liable to You for damages, including any direct, indirect, special, incidental, or consequential damages of any character arising as a result of this License or out of the use or inability to use the Work (including but not limited to damages for loss of goodwill, work stoppage, computer failure or malfunction, or any and all other commercial damages or losses), even if such Contributor has been advised of the possibility of such damages.

   9. Accepting Warranty or Additional Liability. While redistributing the Work or Derivative Works thereof, You may choose to offer, and charge a fee for, acceptance of support, warranty, indemnity, or other liability obligations and/or rights consistent with this License. However, in accepting such obligations, You may act only on Your own behalf and on Your sole responsibility, not on behalf of any other Contributor, and only if You agree to indemnify, defend, and hold each Contributor harmless for any liability incurred by, or claims asserted against, such Contributor by reason of your accepting any such warranty or additional liability. END OF TERMS AND CONDITIONS

APPENDIX: How to apply the Apache License to your work.

To apply the Apache License to your work, attach the following boilerplate notice, with the fields enclosed by brackets "[]" replaced with your own identifying information. (Don't include the brackets!) The text should be enclosed in the appropriate comment syntax for the file format. We also recommend that a file or class name and description of purpose be included on the same "printed page" as the copyright notice for easier identification within third-party archives.

Copyright [yyyy] [name of copyright owner]

Licensed under the Apache License, Version 2.0 (the "License");

you may not use this file except in compliance with the License.

You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software

distributed under the License is distributed on an "AS IS" BASIS,

WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

See the License for the specific language governing permissions and

limitations under the License.

---------------------------------------------------------

---------------------------------------------------------

com.google.code.gson/gson 2.9.0 - Apache-2.0



Apache License

Version 2.0, January 2004

http://www.apache.org/licenses/ TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION

   1. Definitions.

      

      "License" shall mean the terms and conditions for use, reproduction, and distribution as defined by Sections 1 through 9 of this document.

      

      "Licensor" shall mean the copyright owner or entity authorized by the copyright owner that is granting the License.

      

      "Legal Entity" shall mean the union of the acting entity and all other entities that control, are controlled by, or are under common control with that entity. For the purposes of this definition, "control" means (i) the power, direct or indirect, to cause the direction or management of such entity, whether by contract or otherwise, or (ii) ownership of fifty percent (50%) or more of the outstanding shares, or (iii) beneficial ownership of such entity.

      

      "You" (or "Your") shall mean an individual or Legal Entity exercising permissions granted by this License.

      

      "Source" form shall mean the preferred form for making modifications, including but not limited to software source code, documentation source, and configuration files.

      

      "Object" form shall mean any form resulting from mechanical transformation or translation of a Source form, including but not limited to compiled object code, generated documentation, and conversions to other media types.

      

      "Work" shall mean the work of authorship, whether in Source or Object form, made available under the License, as indicated by a copyright notice that is included in or attached to the work (an example is provided in the Appendix below).

      

      "Derivative Works" shall mean any work, whether in Source or Object form, that is based on (or derived from) the Work and for which the editorial revisions, annotations, elaborations, or other modifications represent, as a whole, an original work of authorship. For the purposes of this License, Derivative Works shall not include works that remain separable from, or merely link (or bind by name) to the interfaces of, the Work and Derivative Works thereof.

      

      "Contribution" shall mean any work of authorship, including the original version of the Work and any modifications or additions to that Work or Derivative Works thereof, that is intentionally submitted to Licensor for inclusion in the Work by the copyright owner or by an individual or Legal Entity authorized to submit on behalf of the copyright owner. For the purposes of this definition, "submitted" means any form of electronic, verbal, or written communication sent to the Licensor or its representatives, including but not limited to communication on electronic mailing lists, source code control systems, and issue tracking systems that are managed by, or on behalf of, the Licensor for the purpose of discussing and improving the Work, but excluding communication that is conspicuously marked or otherwise designated in writing by the copyright owner as "Not a Contribution."

      

      "Contributor" shall mean Licensor and any individual or Legal Entity on behalf of whom a Contribution has been received by Licensor and subsequently incorporated within the Work.

   2. Grant of Copyright License. Subject to the terms and conditions of this License, each Contributor hereby grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable copyright license to reproduce, prepare Derivative Works of, publicly display, publicly perform, sublicense, and distribute the Work and such Derivative Works in Source or Object form.

   3. Grant of Patent License. Subject to the terms and conditions of this License, each Contributor hereby grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable (except as stated in this section) patent license to make, have made, use, offer to sell, sell, import, and otherwise transfer the Work, where such license applies only to those patent claims licensable by such Contributor that are necessarily infringed by their Contribution(s) alone or by combination of their Contribution(s) with the Work to which such Contribution(s) was submitted. If You institute patent litigation against any entity (including a cross-claim or counterclaim in a lawsuit) alleging that the Work or a Contribution incorporated within the Work constitutes direct or contributory patent infringement, then any patent licenses granted to You under this License for that Work shall terminate as of the date such litigation is filed.

   4. Redistribution. You may reproduce and distribute copies of the Work or Derivative Works thereof in any medium, with or without modifications, and in Source or Object form, provided that You meet the following conditions:

      (a) You must give any other recipients of the Work or Derivative Works a copy of this License; and

      (b) You must cause any modified files to carry prominent notices stating that You changed the files; and

      (c) You must retain, in the Source form of any Derivative Works that You distribute, all copyright, patent, trademark, and attribution notices from the Source form of the Work, excluding those notices that do not pertain to any part of the Derivative Works; and

      (d) If the Work includes a "NOTICE" text file as part of its distribution, then any Derivative Works that You distribute must include a readable copy of the attribution notices contained within such NOTICE file, excluding those notices that do not pertain to any part of the Derivative Works, in at least one of the following places: within a NOTICE text file distributed as part of the Derivative Works; within the Source form or documentation, if provided along with the Derivative Works; or, within a display generated by the Derivative Works, if and wherever such third-party notices normally appear. The contents of the NOTICE file are for informational purposes only and do not modify the License. You may add Your own attribution notices within Derivative Works that You distribute, alongside or as an addendum to the NOTICE text from the Work, provided that such additional attribution notices cannot be construed as modifying the License.

      You may add Your own copyright statement to Your modifications and may provide additional or different license terms and conditions for use, reproduction, or distribution of Your modifications, or for any such Derivative Works as a whole, provided Your use, reproduction, and distribution of the Work otherwise complies with the conditions stated in this License.

   5. Submission of Contributions. Unless You explicitly state otherwise, any Contribution intentionally submitted for inclusion in the Work by You to the Licensor shall be under the terms and conditions of this License, without any additional terms or conditions. Notwithstanding the above, nothing herein shall supersede or modify the terms of any separate license agreement you may have executed with Licensor regarding such Contributions.

   6. Trademarks. This License does not grant permission to use the trade names, trademarks, service marks, or product names of the Licensor, except as required for reasonable and customary use in describing the origin of the Work and reproducing the content of the NOTICE file.

   7. Disclaimer of Warranty. Unless required by applicable law or agreed to in writing, Licensor provides the Work (and each Contributor provides its Contributions) on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied, including, without limitation, any warranties or conditions of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A PARTICULAR PURPOSE. You are solely responsible for determining the appropriateness of using or redistributing the Work and assume any risks associated with Your exercise of permissions under this License.

   8. Limitation of Liability. In no event and under no legal theory, whether in tort (including negligence), contract, or otherwise, unless required by applicable law (such as deliberate and grossly negligent acts) or agreed to in writing, shall any Contributor be liable to You for damages, including any direct, indirect, special, incidental, or consequential damages of any character arising as a result of this License or out of the use or inability to use the Work (including but not limited to damages for loss of goodwill, work stoppage, computer failure or malfunction, or any and all other commercial damages or losses), even if such Contributor has been advised of the possibility of such damages.

   9. Accepting Warranty or Additional Liability. While redistributing the Work or Derivative Works thereof, You may choose to offer, and charge a fee for, acceptance of support, warranty, indemnity, or other liability obligations and/or rights consistent with this License. However, in accepting such obligations, You may act only on Your own behalf and on Your sole responsibility, not on behalf of any other Contributor, and only if You agree to indemnify, defend, and hold each Contributor harmless for any liability incurred by, or claims asserted against, such Contributor by reason of your accepting any such warranty or additional liability. END OF TERMS AND CONDITIONS

APPENDIX: How to apply the Apache License to your work.

To apply the Apache License to your work, attach the following boilerplate notice, with the fields enclosed by brackets "[]" replaced with your own identifying information. (Don't include the brackets!) The text should be enclosed in the appropriate comment syntax for the file format. We also recommend that a file or class name and description of purpose be included on the same "printed page" as the copyright notice for easier identification within third-party archives.

Copyright [yyyy] [name of copyright owner]

Licensed under the Apache License, Version 2.0 (the "License");

you may not use this file except in compliance with the License.

You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software

distributed under the License is distributed on an "AS IS" BASIS,

WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

See the License for the specific language governing permissions and

limitations under the License.

---------------------------------------------------------

---------------------------------------------------------

com.google.errorprone/error_prone_annotations 2.7.1 - Apache-2.0


Copyright 2015 The Error Prone

Apache License

Version 2.0, January 2004

http://www.apache.org/licenses/ TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION

   1. Definitions.

      

      "License" shall mean the terms and conditions for use, reproduction, and distribution as defined by Sections 1 through 9 of this document.

      

      "Licensor" shall mean the copyright owner or entity authorized by the copyright owner that is granting the License.

      

      "Legal Entity" shall mean the union of the acting entity and all other entities that control, are controlled by, or are under common control with that entity. For the purposes of this definition, "control" means (i) the power, direct or indirect, to cause the direction or management of such entity, whether by contract or otherwise, or (ii) ownership of fifty percent (50%) or more of the outstanding shares, or (iii) beneficial ownership of such entity.

      

      "You" (or "Your") shall mean an individual or Legal Entity exercising permissions granted by this License.

      

      "Source" form shall mean the preferred form for making modifications, including but not limited to software source code, documentation source, and configuration files.

      

      "Object" form shall mean any form resulting from mechanical transformation or translation of a Source form, including but not limited to compiled object code, generated documentation, and conversions to other media types.

      

      "Work" shall mean the work of authorship, whether in Source or Object form, made available under the License, as indicated by a copyright notice that is included in or attached to the work (an example is provided in the Appendix below).

      

      "Derivative Works" shall mean any work, whether in Source or Object form, that is based on (or derived from) the Work and for which the editorial revisions, annotations, elaborations, or other modifications represent, as a whole, an original work of authorship. For the purposes of this License, Derivative Works shall not include works that remain separable from, or merely link (or bind by name) to the interfaces of, the Work and Derivative Works thereof.

      

      "Contribution" shall mean any work of authorship, including the original version of the Work and any modifications or additions to that Work or Derivative Works thereof, that is intentionally submitted to Licensor for inclusion in the Work by the copyright owner or by an individual or Legal Entity authorized to submit on behalf of the copyright owner. For the purposes of this definition, "submitted" means any form of electronic, verbal, or written communication sent to the Licensor or its representatives, including but not limited to communication on electronic mailing lists, source code control systems, and issue tracking systems that are managed by, or on behalf of, the Licensor for the purpose of discussing and improving the Work, but excluding communication that is conspicuously marked or otherwise designated in writing by the copyright owner as "Not a Contribution."

      

      "Contributor" shall mean Licensor and any individual or Legal Entity on behalf of whom a Contribution has been received by Licensor and subsequently incorporated within the Work.

   2. Grant of Copyright License. Subject to the terms and conditions of this License, each Contributor hereby grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable copyright license to reproduce, prepare Derivative Works of, publicly display, publicly perform, sublicense, and distribute the Work and such Derivative Works in Source or Object form.

   3. Grant of Patent License. Subject to the terms and conditions of this License, each Contributor hereby grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable (except as stated in this section) patent license to make, have made, use, offer to sell, sell, import, and otherwise transfer the Work, where such license applies only to those patent claims licensable by such Contributor that are necessarily infringed by their Contribution(s) alone or by combination of their Contribution(s) with the Work to which such Contribution(s) was submitted. If You institute patent litigation against any entity (including a cross-claim or counterclaim in a lawsuit) alleging that the Work or a Contribution incorporated within the Work constitutes direct or contributory patent infringement, then any patent licenses granted to You under this License for that Work shall terminate as of the date such litigation is filed.

   4. Redistribution. You may reproduce and distribute copies of the Work or Derivative Works thereof in any medium, with or without modifications, and in Source or Object form, provided that You meet the following conditions:

      (a) You must give any other recipients of the Work or Derivative Works a copy of this License; and

      (b) You must cause any modified files to carry prominent notices stating that You changed the files; and

      (c) You must retain, in the Source form of any Derivative Works that You distribute, all copyright, patent, trademark, and attribution notices from the Source form of the Work, excluding those notices that do not pertain to any part of the Derivative Works; and

      (d) If the Work includes a "NOTICE" text file as part of its distribution, then any Derivative Works that You distribute must include a readable copy of the attribution notices contained within such NOTICE file, excluding those notices that do not pertain to any part of the Derivative Works, in at least one of the following places: within a NOTICE text file distributed as part of the Derivative Works; within the Source form or documentation, if provided along with the Derivative Works; or, within a display generated by the Derivative Works, if and wherever such third-party notices normally appear. The contents of the NOTICE file are for informational purposes only and do not modify the License. You may add Your own attribution notices within Derivative Works that You distribute, alongside or as an addendum to the NOTICE text from the Work, provided that such additional attribution notices cannot be construed as modifying the License.

      You may add Your own copyright statement to Your modifications and may provide additional or different license terms and conditions for use, reproduction, or distribution of Your modifications, or for any such Derivative Works as a whole, provided Your use, reproduction, and distribution of the Work otherwise complies with the conditions stated in this License.

   5. Submission of Contributions. Unless You explicitly state otherwise, any Contribution intentionally submitted for inclusion in the Work by You to the Licensor shall be under the terms and conditions of this License, without any additional terms or conditions. Notwithstanding the above, nothing herein shall supersede or modify the terms of any separate license agreement you may have executed with Licensor regarding such Contributions.

   6. Trademarks. This License does not grant permission to use the trade names, trademarks, service marks, or product names of the Licensor, except as required for reasonable and customary use in describing the origin of the Work and reproducing the content of the NOTICE file.

   7. Disclaimer of Warranty. Unless required by applicable law or agreed to in writing, Licensor provides the Work (and each Contributor provides its Contributions) on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied, including, without limitation, any warranties or conditions of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A PARTICULAR PURPOSE. You are solely responsible for determining the appropriateness of using or redistributing the Work and assume any risks associated with Your exercise of permissions under this License.

   8. Limitation of Liability. In no event and under no legal theory, whether in tort (including negligence), contract, or otherwise, unless required by applicable law (such as deliberate and grossly negligent acts) or agreed to in writing, shall any Contributor be liable to You for damages, including any direct, indirect, special, incidental, or consequential damages of any character arising as a result of this License or out of the use or inability to use the Work (including but not limited to damages for loss of goodwill, work stoppage, computer failure or malfunction, or any and all other commercial damages or losses), even if such Contributor has been advised of the possibility of such damages.

   9. Accepting Warranty or Additional Liability. While redistributing the Work or Derivative Works thereof, You may choose to offer, and charge a fee for, acceptance of support, warranty, indemnity, or other liability obligations and/or rights consistent with this License. However, in accepting such obligations, You may act only on Your own behalf and on Your sole responsibility, not on behalf of any other Contributor, and only if You agree to indemnify, defend, and hold each Contributor harmless for any liability incurred by, or claims asserted against, such Contributor by reason of your accepting any such warranty or additional liability. END OF TERMS AND CONDITIONS

APPENDIX: How to apply the Apache License to your work.

To apply the Apache License to your work, attach the following boilerplate notice, with the fields enclosed by brackets "[]" replaced with your own identifying information. (Don't include the brackets!) The text should be enclosed in the appropriate comment syntax for the file format. We also recommend that a file or class name and description of purpose be included on the same "printed page" as the copyright notice for easier identification within third-party archives.

Copyright [yyyy] [name of copyright owner]

Licensed under the Apache License, Version 2.0 (the "License");

you may not use this file except in compliance with the License.

You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software

distributed under the License is distributed on an "AS IS" BASIS,

WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

See the License for the specific language governing permissions and

limitations under the License.

---------------------------------------------------------

---------------------------------------------------------

com.google.guava/failureaccess 1.0.1 - Apache-2.0



Apache License

Version 2.0, January 2004

http://www.apache.org/licenses/ TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION

   1. Definitions.

      

      "License" shall mean the terms and conditions for use, reproduction, and distribution as defined by Sections 1 through 9 of this document.

      

      "Licensor" shall mean the copyright owner or entity authorized by the copyright owner that is granting the License.

      

      "Legal Entity" shall mean the union of the acting entity and all other entities that control, are controlled by, or are under common control with that entity. For the purposes of this definition, "control" means (i) the power, direct or indirect, to cause the direction or management of such entity, whether by contract or otherwise, or (ii) ownership of fifty percent (50%) or more of the outstanding shares, or (iii) beneficial ownership of such entity.

      

      "You" (or "Your") shall mean an individual or Legal Entity exercising permissions granted by this License.

      

      "Source" form shall mean the preferred form for making modifications, including but not limited to software source code, documentation source, and configuration files.

      

      "Object" form shall mean any form resulting from mechanical transformation or translation of a Source form, including but not limited to compiled object code, generated documentation, and conversions to other media types.

      

      "Work" shall mean the work of authorship, whether in Source or Object form, made available under the License, as indicated by a copyright notice that is included in or attached to the work (an example is provided in the Appendix below).

      

      "Derivative Works" shall mean any work, whether in Source or Object form, that is based on (or derived from) the Work and for which the editorial revisions, annotations, elaborations, or other modifications represent, as a whole, an original work of authorship. For the purposes of this License, Derivative Works shall not include works that remain separable from, or merely link (or bind by name) to the interfaces of, the Work and Derivative Works thereof.

      

      "Contribution" shall mean any work of authorship, including the original version of the Work and any modifications or additions to that Work or Derivative Works thereof, that is intentionally submitted to Licensor for inclusion in the Work by the copyright owner or by an individual or Legal Entity authorized to submit on behalf of the copyright owner. For the purposes of this definition, "submitted" means any form of electronic, verbal, or written communication sent to the Licensor or its representatives, including but not limited to communication on electronic mailing lists, source code control systems, and issue tracking systems that are managed by, or on behalf of, the Licensor for the purpose of discussing and improving the Work, but excluding communication that is conspicuously marked or otherwise designated in writing by the copyright owner as "Not a Contribution."

      

      "Contributor" shall mean Licensor and any individual or Legal Entity on behalf of whom a Contribution has been received by Licensor and subsequently incorporated within the Work.

   2. Grant of Copyright License. Subject to the terms and conditions of this License, each Contributor hereby grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable copyright license to reproduce, prepare Derivative Works of, publicly display, publicly perform, sublicense, and distribute the Work and such Derivative Works in Source or Object form.

   3. Grant of Patent License. Subject to the terms and conditions of this License, each Contributor hereby grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable (except as stated in this section) patent license to make, have made, use, offer to sell, sell, import, and otherwise transfer the Work, where such license applies only to those patent claims licensable by such Contributor that are necessarily infringed by their Contribution(s) alone or by combination of their Contribution(s) with the Work to which such Contribution(s) was submitted. If You institute patent litigation against any entity (including a cross-claim or counterclaim in a lawsuit) alleging that the Work or a Contribution incorporated within the Work constitutes direct or contributory patent infringement, then any patent licenses granted to You under this License for that Work shall terminate as of the date such litigation is filed.

   4. Redistribution. You may reproduce and distribute copies of the Work or Derivative Works thereof in any medium, with or without modifications, and in Source or Object form, provided that You meet the following conditions:

      (a) You must give any other recipients of the Work or Derivative Works a copy of this License; and

      (b) You must cause any modified files to carry prominent notices stating that You changed the files; and

      (c) You must retain, in the Source form of any Derivative Works that You distribute, all copyright, patent, trademark, and attribution notices from the Source form of the Work, excluding those notices that do not pertain to any part of the Derivative Works; and

      (d) If the Work includes a "NOTICE" text file as part of its distribution, then any Derivative Works that You distribute must include a readable copy of the attribution notices contained within such NOTICE file, excluding those notices that do not pertain to any part of the Derivative Works, in at least one of the following places: within a NOTICE text file distributed as part of the Derivative Works; within the Source form or documentation, if provided along with the Derivative Works; or, within a display generated by the Derivative Works, if and wherever such third-party notices normally appear. The contents of the NOTICE file are for informational purposes only and do not modify the License. You may add Your own attribution notices within Derivative Works that You distribute, alongside or as an addendum to the NOTICE text from the Work, provided that such additional attribution notices cannot be construed as modifying the License.

      You may add Your own copyright statement to Your modifications and may provide additional or different license terms and conditions for use, reproduction, or distribution of Your modifications, or for any such Derivative Works as a whole, provided Your use, reproduction, and distribution of the Work otherwise complies with the conditions stated in this License.

   5. Submission of Contributions. Unless You explicitly state otherwise, any Contribution intentionally submitted for inclusion in the Work by You to the Licensor shall be under the terms and conditions of this License, without any additional terms or conditions. Notwithstanding the above, nothing herein shall supersede or modify the terms of any separate license agreement you may have executed with Licensor regarding such Contributions.

   6. Trademarks. This License does not grant permission to use the trade names, trademarks, service marks, or product names of the Licensor, except as required for reasonable and customary use in describing the origin of the Work and reproducing the content of the NOTICE file.

   7. Disclaimer of Warranty. Unless required by applicable law or agreed to in writing, Licensor provides the Work (and each Contributor provides its Contributions) on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied, including, without limitation, any warranties or conditions of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A PARTICULAR PURPOSE. You are solely responsible for determining the appropriateness of using or redistributing the Work and assume any risks associated with Your exercise of permissions under this License.

   8. Limitation of Liability. In no event and under no legal theory, whether in tort (including negligence), contract, or otherwise, unless required by applicable law (such as deliberate and grossly negligent acts) or agreed to in writing, shall any Contributor be liable to You for damages, including any direct, indirect, special, incidental, or consequential damages of any character arising as a result of this License or out of the use or inability to use the Work (including but not limited to damages for loss of goodwill, work stoppage, computer failure or malfunction, or any and all other commercial damages or losses), even if such Contributor has been advised of the possibility of such damages.

   9. Accepting Warranty or Additional Liability. While redistributing the Work or Derivative Works thereof, You may choose to offer, and charge a fee for, acceptance of support, warranty, indemnity, or other liability obligations and/or rights consistent with this License. However, in accepting such obligations, You may act only on Your own behalf and on Your sole responsibility, not on behalf of any other Contributor, and only if You agree to indemnify, defend, and hold each Contributor harmless for any liability incurred by, or claims asserted against, such Contributor by reason of your accepting any such warranty or additional liability. END OF TERMS AND CONDITIONS

APPENDIX: How to apply the Apache License to your work.

To apply the Apache License to your work, attach the following boilerplate notice, with the fields enclosed by brackets "[]" replaced with your own identifying information. (Don't include the brackets!) The text should be enclosed in the appropriate comment syntax for the file format. We also recommend that a file or class name and description of purpose be included on the same "printed page" as the copyright notice for easier identification within third-party archives.

Copyright [yyyy] [name of copyright owner]

Licensed under the Apache License, Version 2.0 (the "License");

you may not use this file except in compliance with the License.

You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software

distributed under the License is distributed on an "AS IS" BASIS,

WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

See the License for the specific language governing permissions and

limitations under the License.

---------------------------------------------------------

---------------------------------------------------------

com.google.guava/guava 31.0.1-jre - Apache-2.0



Apache License

Version 2.0, January 2004

http://www.apache.org/licenses/ TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION

   1. Definitions.

      

      "License" shall mean the terms and conditions for use, reproduction, and distribution as defined by Sections 1 through 9 of this document.

      

      "Licensor" shall mean the copyright owner or entity authorized by the copyright owner that is granting the License.

      

      "Legal Entity" shall mean the union of the acting entity and all other entities that control, are controlled by, or are under common control with that entity. For the purposes of this definition, "control" means (i) the power, direct or indirect, to cause the direction or management of such entity, whether by contract or otherwise, or (ii) ownership of fifty percent (50%) or more of the outstanding shares, or (iii) beneficial ownership of such entity.

      

      "You" (or "Your") shall mean an individual or Legal Entity exercising permissions granted by this License.

      

      "Source" form shall mean the preferred form for making modifications, including but not limited to software source code, documentation source, and configuration files.

      

      "Object" form shall mean any form resulting from mechanical transformation or translation of a Source form, including but not limited to compiled object code, generated documentation, and conversions to other media types.

      

      "Work" shall mean the work of authorship, whether in Source or Object form, made available under the License, as indicated by a copyright notice that is included in or attached to the work (an example is provided in the Appendix below).

      

      "Derivative Works" shall mean any work, whether in Source or Object form, that is based on (or derived from) the Work and for which the editorial revisions, annotations, elaborations, or other modifications represent, as a whole, an original work of authorship. For the purposes of this License, Derivative Works shall not include works that remain separable from, or merely link (or bind by name) to the interfaces of, the Work and Derivative Works thereof.

      

      "Contribution" shall mean any work of authorship, including the original version of the Work and any modifications or additions to that Work or Derivative Works thereof, that is intentionally submitted to Licensor for inclusion in the Work by the copyright owner or by an individual or Legal Entity authorized to submit on behalf of the copyright owner. For the purposes of this definition, "submitted" means any form of electronic, verbal, or written communication sent to the Licensor or its representatives, including but not limited to communication on electronic mailing lists, source code control systems, and issue tracking systems that are managed by, or on behalf of, the Licensor for the purpose of discussing and improving the Work, but excluding communication that is conspicuously marked or otherwise designated in writing by the copyright owner as "Not a Contribution."

      

      "Contributor" shall mean Licensor and any individual or Legal Entity on behalf of whom a Contribution has been received by Licensor and subsequently incorporated within the Work.

   2. Grant of Copyright License. Subject to the terms and conditions of this License, each Contributor hereby grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable copyright license to reproduce, prepare Derivative Works of, publicly display, publicly perform, sublicense, and distribute the Work and such Derivative Works in Source or Object form.

   3. Grant of Patent License. Subject to the terms and conditions of this License, each Contributor hereby grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable (except as stated in this section) patent license to make, have made, use, offer to sell, sell, import, and otherwise transfer the Work, where such license applies only to those patent claims licensable by such Contributor that are necessarily infringed by their Contribution(s) alone or by combination of their Contribution(s) with the Work to which such Contribution(s) was submitted. If You institute patent litigation against any entity (including a cross-claim or counterclaim in a lawsuit) alleging that the Work or a Contribution incorporated within the Work constitutes direct or contributory patent infringement, then any patent licenses granted to You under this License for that Work shall terminate as of the date such litigation is filed.

   4. Redistribution. You may reproduce and distribute copies of the Work or Derivative Works thereof in any medium, with or without modifications, and in Source or Object form, provided that You meet the following conditions:

      (a) You must give any other recipients of the Work or Derivative Works a copy of this License; and

      (b) You must cause any modified files to carry prominent notices stating that You changed the files; and

      (c) You must retain, in the Source form of any Derivative Works that You distribute, all copyright, patent, trademark, and attribution notices from the Source form of the Work, excluding those notices that do not pertain to any part of the Derivative Works; and

      (d) If the Work includes a "NOTICE" text file as part of its distribution, then any Derivative Works that You distribute must include a readable copy of the attribution notices contained within such NOTICE file, excluding those notices that do not pertain to any part of the Derivative Works, in at least one of the following places: within a NOTICE text file distributed as part of the Derivative Works; within the Source form or documentation, if provided along with the Derivative Works; or, within a display generated by the Derivative Works, if and wherever such third-party notices normally appear. The contents of the NOTICE file are for informational purposes only and do not modify the License. You may add Your own attribution notices within Derivative Works that You distribute, alongside or as an addendum to the NOTICE text from the Work, provided that such additional attribution notices cannot be construed as modifying the License.

      You may add Your own copyright statement to Your modifications and may provide additional or different license terms and conditions for use, reproduction, or distribution of Your modifications, or for any such Derivative Works as a whole, provided Your use, reproduction, and distribution of the Work otherwise complies with the conditions stated in this License.

   5. Submission of Contributions. Unless You explicitly state otherwise, any Contribution intentionally submitted for inclusion in the Work by You to the Licensor shall be under the terms and conditions of this License, without any additional terms or conditions. Notwithstanding the above, nothing herein shall supersede or modify the terms of any separate license agreement you may have executed with Licensor regarding such Contributions.

   6. Trademarks. This License does not grant permission to use the trade names, trademarks, service marks, or product names of the Licensor, except as required for reasonable and customary use in describing the origin of the Work and reproducing the content of the NOTICE file.

   7. Disclaimer of Warranty. Unless required by applicable law or agreed to in writing, Licensor provides the Work (and each Contributor provides its Contributions) on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied, including, without limitation, any warranties or conditions of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A PARTICULAR PURPOSE. You are solely responsible for determining the appropriateness of using or redistributing the Work and assume any risks associated with Your exercise of permissions under this License.

   8. Limitation of Liability. In no event and under no legal theory, whether in tort (including negligence), contract, or otherwise, unless required by applicable law (such as deliberate and grossly negligent acts) or agreed to in writing, shall any Contributor be liable to You for damages, including any direct, indirect, special, incidental, or consequential damages of any character arising as a result of this License or out of the use or inability to use the Work (including but not limited to damages for loss of goodwill, work stoppage, computer failure or malfunction, or any and all other commercial damages or losses), even if such Contributor has been advised of the possibility of such damages.

   9. Accepting Warranty or Additional Liability. While redistributing the Work or Derivative Works thereof, You may choose to offer, and charge a fee for, acceptance of support, warranty, indemnity, or other liability obligations and/or rights consistent with this License. However, in accepting such obligations, You may act only on Your own behalf and on Your sole responsibility, not on behalf of any other Contributor, and only if You agree to indemnify, defend, and hold each Contributor harmless for any liability incurred by, or claims asserted against, such Contributor by reason of your accepting any such warranty or additional liability. END OF TERMS AND CONDITIONS

APPENDIX: How to apply the Apache License to your work.

To apply the Apache License to your work, attach the following boilerplate notice, with the fields enclosed by brackets "[]" replaced with your own identifying information. (Don't include the brackets!) The text should be enclosed in the appropriate comment syntax for the file format. We also recommend that a file or class name and description of purpose be included on the same "printed page" as the copyright notice for easier identification within third-party archives.

Copyright [yyyy] [name of copyright owner]

Licensed under the Apache License, Version 2.0 (the "License");

you may not use this file except in compliance with the License.

You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software

distributed under the License is distributed on an "AS IS" BASIS,

WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

See the License for the specific language governing permissions and

limitations under the License.

---------------------------------------------------------

---------------------------------------------------------

com.google.guava/listenablefuture 9999.0-empty-to-avoid-conflict-with-guava - Apache-2.0



Apache License

Version 2.0, January 2004

http://www.apache.org/licenses/ TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION

   1. Definitions.

      

      "License" shall mean the terms and conditions for use, reproduction, and distribution as defined by Sections 1 through 9 of this document.

      

      "Licensor" shall mean the copyright owner or entity authorized by the copyright owner that is granting the License.

      

      "Legal Entity" shall mean the union of the acting entity and all other entities that control, are controlled by, or are under common control with that entity. For the purposes of this definition, "control" means (i) the power, direct or indirect, to cause the direction or management of such entity, whether by contract or otherwise, or (ii) ownership of fifty percent (50%) or more of the outstanding shares, or (iii) beneficial ownership of such entity.

      

      "You" (or "Your") shall mean an individual or Legal Entity exercising permissions granted by this License.

      

      "Source" form shall mean the preferred form for making modifications, including but not limited to software source code, documentation source, and configuration files.

      

      "Object" form shall mean any form resulting from mechanical transformation or translation of a Source form, including but not limited to compiled object code, generated documentation, and conversions to other media types.

      

      "Work" shall mean the work of authorship, whether in Source or Object form, made available under the License, as indicated by a copyright notice that is included in or attached to the work (an example is provided in the Appendix below).

      

      "Derivative Works" shall mean any work, whether in Source or Object form, that is based on (or derived from) the Work and for which the editorial revisions, annotations, elaborations, or other modifications represent, as a whole, an original work of authorship. For the purposes of this License, Derivative Works shall not include works that remain separable from, or merely link (or bind by name) to the interfaces of, the Work and Derivative Works thereof.

      

      "Contribution" shall mean any work of authorship, including the original version of the Work and any modifications or additions to that Work or Derivative Works thereof, that is intentionally submitted to Licensor for inclusion in the Work by the copyright owner or by an individual or Legal Entity authorized to submit on behalf of the copyright owner. For the purposes of this definition, "submitted" means any form of electronic, verbal, or written communication sent to the Licensor or its representatives, including but not limited to communication on electronic mailing lists, source code control systems, and issue tracking systems that are managed by, or on behalf of, the Licensor for the purpose of discussing and improving the Work, but excluding communication that is conspicuously marked or otherwise designated in writing by the copyright owner as "Not a Contribution."

      

      "Contributor" shall mean Licensor and any individual or Legal Entity on behalf of whom a Contribution has been received by Licensor and subsequently incorporated within the Work.

   2. Grant of Copyright License. Subject to the terms and conditions of this License, each Contributor hereby grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable copyright license to reproduce, prepare Derivative Works of, publicly display, publicly perform, sublicense, and distribute the Work and such Derivative Works in Source or Object form.

   3. Grant of Patent License. Subject to the terms and conditions of this License, each Contributor hereby grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable (except as stated in this section) patent license to make, have made, use, offer to sell, sell, import, and otherwise transfer the Work, where such license applies only to those patent claims licensable by such Contributor that are necessarily infringed by their Contribution(s) alone or by combination of their Contribution(s) with the Work to which such Contribution(s) was submitted. If You institute patent litigation against any entity (including a cross-claim or counterclaim in a lawsuit) alleging that the Work or a Contribution incorporated within the Work constitutes direct or contributory patent infringement, then any patent licenses granted to You under this License for that Work shall terminate as of the date such litigation is filed.

   4. Redistribution. You may reproduce and distribute copies of the Work or Derivative Works thereof in any medium, with or without modifications, and in Source or Object form, provided that You meet the following conditions:

      (a) You must give any other recipients of the Work or Derivative Works a copy of this License; and

      (b) You must cause any modified files to carry prominent notices stating that You changed the files; and

      (c) You must retain, in the Source form of any Derivative Works that You distribute, all copyright, patent, trademark, and attribution notices from the Source form of the Work, excluding those notices that do not pertain to any part of the Derivative Works; and

      (d) If the Work includes a "NOTICE" text file as part of its distribution, then any Derivative Works that You distribute must include a readable copy of the attribution notices contained within such NOTICE file, excluding those notices that do not pertain to any part of the Derivative Works, in at least one of the following places: within a NOTICE text file distributed as part of the Derivative Works; within the Source form or documentation, if provided along with the Derivative Works; or, within a display generated by the Derivative Works, if and wherever such third-party notices normally appear. The contents of the NOTICE file are for informational purposes only and do not modify the License. You may add Your own attribution notices within Derivative Works that You distribute, alongside or as an addendum to the NOTICE text from the Work, provided that such additional attribution notices cannot be construed as modifying the License.

      You may add Your own copyright statement to Your modifications and may provide additional or different license terms and conditions for use, reproduction, or distribution of Your modifications, or for any such Derivative Works as a whole, provided Your use, reproduction, and distribution of the Work otherwise complies with the conditions stated in this License.

   5. Submission of Contributions. Unless You explicitly state otherwise, any Contribution intentionally submitted for inclusion in the Work by You to the Licensor shall be under the terms and conditions of this License, without any additional terms or conditions. Notwithstanding the above, nothing herein shall supersede or modify the terms of any separate license agreement you may have executed with Licensor regarding such Contributions.

   6. Trademarks. This License does not grant permission to use the trade names, trademarks, service marks, or product names of the Licensor, except as required for reasonable and customary use in describing the origin of the Work and reproducing the content of the NOTICE file.

   7. Disclaimer of Warranty. Unless required by applicable law or agreed to in writing, Licensor provides the Work (and each Contributor provides its Contributions) on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied, including, without limitation, any warranties or conditions of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A PARTICULAR PURPOSE. You are solely responsible for determining the appropriateness of using or redistributing the Work and assume any risks associated with Your exercise of permissions under this License.

   8. Limitation of Liability. In no event and under no legal theory, whether in tort (including negligence), contract, or otherwise, unless required by applicable law (such as deliberate and grossly negligent acts) or agreed to in writing, shall any Contributor be liable to You for damages, including any direct, indirect, special, incidental, or consequential damages of any character arising as a result of this License or out of the use or inability to use the Work (including but not limited to damages for loss of goodwill, work stoppage, computer failure or malfunction, or any and all other commercial damages or losses), even if such Contributor has been advised of the possibility of such damages.

   9. Accepting Warranty or Additional Liability. While redistributing the Work or Derivative Works thereof, You may choose to offer, and charge a fee for, acceptance of support, warranty, indemnity, or other liability obligations and/or rights consistent with this License. However, in accepting such obligations, You may act only on Your own behalf and on Your sole responsibility, not on behalf of any other Contributor, and only if You agree to indemnify, defend, and hold each Contributor harmless for any liability incurred by, or claims asserted against, such Contributor by reason of your accepting any such warranty or additional liability. END OF TERMS AND CONDITIONS

APPENDIX: How to apply the Apache License to your work.

To apply the Apache License to your work, attach the following boilerplate notice, with the fields enclosed by brackets "[]" replaced with your own identifying information. (Don't include the brackets!) The text should be enclosed in the appropriate comment syntax for the file format. We also recommend that a file or class name and description of purpose be included on the same "printed page" as the copyright notice for easier identification within third-party archives.

Copyright [yyyy] [name of copyright owner]

Licensed under the Apache License, Version 2.0 (the "License");

you may not use this file except in compliance with the License.

You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software

distributed under the License is distributed on an "AS IS" BASIS,

WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

See the License for the specific language governing permissions and

limitations under the License.

---------------------------------------------------------

---------------------------------------------------------

com.google.j2objc/j2objc-annotations 1.3 - Apache-2.0



Apache License

Version 2.0, January 2004

http://www.apache.org/licenses/ TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION

   1. Definitions.

      

      "License" shall mean the terms and conditions for use, reproduction, and distribution as defined by Sections 1 through 9 of this document.

      

      "Licensor" shall mean the copyright owner or entity authorized by the copyright owner that is granting the License.

      

      "Legal Entity" shall mean the union of the acting entity and all other entities that control, are controlled by, or are under common control with that entity. For the purposes of this definition, "control" means (i) the power, direct or indirect, to cause the direction or management of such entity, whether by contract or otherwise, or (ii) ownership of fifty percent (50%) or more of the outstanding shares, or (iii) beneficial ownership of such entity.

      

      "You" (or "Your") shall mean an individual or Legal Entity exercising permissions granted by this License.

      

      "Source" form shall mean the preferred form for making modifications, including but not limited to software source code, documentation source, and configuration files.

      

      "Object" form shall mean any form resulting from mechanical transformation or translation of a Source form, including but not limited to compiled object code, generated documentation, and conversions to other media types.

      

      "Work" shall mean the work of authorship, whether in Source or Object form, made available under the License, as indicated by a copyright notice that is included in or attached to the work (an example is provided in the Appendix below).

      

      "Derivative Works" shall mean any work, whether in Source or Object form, that is based on (or derived from) the Work and for which the editorial revisions, annotations, elaborations, or other modifications represent, as a whole, an original work of authorship. For the purposes of this License, Derivative Works shall not include works that remain separable from, or merely link (or bind by name) to the interfaces of, the Work and Derivative Works thereof.

      

      "Contribution" shall mean any work of authorship, including the original version of the Work and any modifications or additions to that Work or Derivative Works thereof, that is intentionally submitted to Licensor for inclusion in the Work by the copyright owner or by an individual or Legal Entity authorized to submit on behalf of the copyright owner. For the purposes of this definition, "submitted" means any form of electronic, verbal, or written communication sent to the Licensor or its representatives, including but not limited to communication on electronic mailing lists, source code control systems, and issue tracking systems that are managed by, or on behalf of, the Licensor for the purpose of discussing and improving the Work, but excluding communication that is conspicuously marked or otherwise designated in writing by the copyright owner as "Not a Contribution."

      

      "Contributor" shall mean Licensor and any individual or Legal Entity on behalf of whom a Contribution has been received by Licensor and subsequently incorporated within the Work.

   2. Grant of Copyright License. Subject to the terms and conditions of this License, each Contributor hereby grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable copyright license to reproduce, prepare Derivative Works of, publicly display, publicly perform, sublicense, and distribute the Work and such Derivative Works in Source or Object form.

   3. Grant of Patent License. Subject to the terms and conditions of this License, each Contributor hereby grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable (except as stated in this section) patent license to make, have made, use, offer to sell, sell, import, and otherwise transfer the Work, where such license applies only to those patent claims licensable by such Contributor that are necessarily infringed by their Contribution(s) alone or by combination of their Contribution(s) with the Work to which such Contribution(s) was submitted. If You institute patent litigation against any entity (including a cross-claim or counterclaim in a lawsuit) alleging that the Work or a Contribution incorporated within the Work constitutes direct or contributory patent infringement, then any patent licenses granted to You under this License for that Work shall terminate as of the date such litigation is filed.

   4. Redistribution. You may reproduce and distribute copies of the Work or Derivative Works thereof in any medium, with or without modifications, and in Source or Object form, provided that You meet the following conditions:

      (a) You must give any other recipients of the Work or Derivative Works a copy of this License; and

      (b) You must cause any modified files to carry prominent notices stating that You changed the files; and

      (c) You must retain, in the Source form of any Derivative Works that You distribute, all copyright, patent, trademark, and attribution notices from the Source form of the Work, excluding those notices that do not pertain to any part of the Derivative Works; and

      (d) If the Work includes a "NOTICE" text file as part of its distribution, then any Derivative Works that You distribute must include a readable copy of the attribution notices contained within such NOTICE file, excluding those notices that do not pertain to any part of the Derivative Works, in at least one of the following places: within a NOTICE text file distributed as part of the Derivative Works; within the Source form or documentation, if provided along with the Derivative Works; or, within a display generated by the Derivative Works, if and wherever such third-party notices normally appear. The contents of the NOTICE file are for informational purposes only and do not modify the License. You may add Your own attribution notices within Derivative Works that You distribute, alongside or as an addendum to the NOTICE text from the Work, provided that such additional attribution notices cannot be construed as modifying the License.

      You may add Your own copyright statement to Your modifications and may provide additional or different license terms and conditions for use, reproduction, or distribution of Your modifications, or for any such Derivative Works as a whole, provided Your use, reproduction, and distribution of the Work otherwise complies with the conditions stated in this License.

   5. Submission of Contributions. Unless You explicitly state otherwise, any Contribution intentionally submitted for inclusion in the Work by You to the Licensor shall be under the terms and conditions of this License, without any additional terms or conditions. Notwithstanding the above, nothing herein shall supersede or modify the terms of any separate license agreement you may have executed with Licensor regarding such Contributions.

   6. Trademarks. This License does not grant permission to use the trade names, trademarks, service marks, or product names of the Licensor, except as required for reasonable and customary use in describing the origin of the Work and reproducing the content of the NOTICE file.

   7. Disclaimer of Warranty. Unless required by applicable law or agreed to in writing, Licensor provides the Work (and each Contributor provides its Contributions) on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied, including, without limitation, any warranties or conditions of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A PARTICULAR PURPOSE. You are solely responsible for determining the appropriateness of using or redistributing the Work and assume any risks associated with Your exercise of permissions under this License.

   8. Limitation of Liability. In no event and under no legal theory, whether in tort (including negligence), contract, or otherwise, unless required by applicable law (such as deliberate and grossly negligent acts) or agreed to in writing, shall any Contributor be liable to You for damages, including any direct, indirect, special, incidental, or consequential damages of any character arising as a result of this License or out of the use or inability to use the Work (including but not limited to damages for loss of goodwill, work stoppage, computer failure or malfunction, or any and all other commercial damages or losses), even if such Contributor has been advised of the possibility of such damages.

   9. Accepting Warranty or Additional Liability. While redistributing the Work or Derivative Works thereof, You may choose to offer, and charge a fee for, acceptance of support, warranty, indemnity, or other liability obligations and/or rights consistent with this License. However, in accepting such obligations, You may act only on Your own behalf and on Your sole responsibility, not on behalf of any other Contributor, and only if You agree to indemnify, defend, and hold each Contributor harmless for any liability incurred by, or claims asserted against, such Contributor by reason of your accepting any such warranty or additional liability. END OF TERMS AND CONDITIONS

APPENDIX: How to apply the Apache License to your work.

To apply the Apache License to your work, attach the following boilerplate notice, with the fields enclosed by brackets "[]" replaced with your own identifying information. (Don't include the brackets!) The text should be enclosed in the appropriate comment syntax for the file format. We also recommend that a file or class name and description of purpose be included on the same "printed page" as the copyright notice for easier identification within third-party archives.

Copyright [yyyy] [name of copyright owner]

Licensed under the Apache License, Version 2.0 (the "License");

you may not use this file except in compliance with the License.

You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software

distributed under the License is distributed on an "AS IS" BASIS,

WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

See the License for the specific language governing permissions and

limitations under the License.

---------------------------------------------------------

---------------------------------------------------------

com.nimbusds/nimbus-jose-jwt 9.20 - Apache-2.0



Apache License

Version 2.0, January 2004

http://www.apache.org/licenses/ TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION

   1. Definitions.

      

      "License" shall mean the terms and conditions for use, reproduction, and distribution as defined by Sections 1 through 9 of this document.

      

      "Licensor" shall mean the copyright owner or entity authorized by the copyright owner that is granting the License.

      

      "Legal Entity" shall mean the union of the acting entity and all other entities that control, are controlled by, or are under common control with that entity. For the purposes of this definition, "control" means (i) the power, direct or indirect, to cause the direction or management of such entity, whether by contract or otherwise, or (ii) ownership of fifty percent (50%) or more of the outstanding shares, or (iii) beneficial ownership of such entity.

      

      "You" (or "Your") shall mean an individual or Legal Entity exercising permissions granted by this License.

      

      "Source" form shall mean the preferred form for making modifications, including but not limited to software source code, documentation source, and configuration files.

      

      "Object" form shall mean any form resulting from mechanical transformation or translation of a Source form, including but not limited to compiled object code, generated documentation, and conversions to other media types.

      

      "Work" shall mean the work of authorship, whether in Source or Object form, made available under the License, as indicated by a copyright notice that is included in or attached to the work (an example is provided in the Appendix below).

      

      "Derivative Works" shall mean any work, whether in Source or Object form, that is based on (or derived from) the Work and for which the editorial revisions, annotations, elaborations, or other modifications represent, as a whole, an original work of authorship. For the purposes of this License, Derivative Works shall not include works that remain separable from, or merely link (or bind by name) to the interfaces of, the Work and Derivative Works thereof.

      

      "Contribution" shall mean any work of authorship, including the original version of the Work and any modifications or additions to that Work or Derivative Works thereof, that is intentionally submitted to Licensor for inclusion in the Work by the copyright owner or by an individual or Legal Entity authorized to submit on behalf of the copyright owner. For the purposes of this definition, "submitted" means any form of electronic, verbal, or written communication sent to the Licensor or its representatives, including but not limited to communication on electronic mailing lists, source code control systems, and issue tracking systems that are managed by, or on behalf of, the Licensor for the purpose of discussing and improving the Work, but excluding communication that is conspicuously marked or otherwise designated in writing by the copyright owner as "Not a Contribution."

      

      "Contributor" shall mean Licensor and any individual or Legal Entity on behalf of whom a Contribution has been received by Licensor and subsequently incorporated within the Work.

   2. Grant of Copyright License. Subject to the terms and conditions of this License, each Contributor hereby grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable copyright license to reproduce, prepare Derivative Works of, publicly display, publicly perform, sublicense, and distribute the Work and such Derivative Works in Source or Object form.

   3. Grant of Patent License. Subject to the terms and conditions of this License, each Contributor hereby grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable (except as stated in this section) patent license to make, have made, use, offer to sell, sell, import, and otherwise transfer the Work, where such license applies only to those patent claims licensable by such Contributor that are necessarily infringed by their Contribution(s) alone or by combination of their Contribution(s) with the Work to which such Contribution(s) was submitted. If You institute patent litigation against any entity (including a cross-claim or counterclaim in a lawsuit) alleging that the Work or a Contribution incorporated within the Work constitutes direct or contributory patent infringement, then any patent licenses granted to You under this License for that Work shall terminate as of the date such litigation is filed.

   4. Redistribution. You may reproduce and distribute copies of the Work or Derivative Works thereof in any medium, with or without modifications, and in Source or Object form, provided that You meet the following conditions:

      (a) You must give any other recipients of the Work or Derivative Works a copy of this License; and

      (b) You must cause any modified files to carry prominent notices stating that You changed the files; and

      (c) You must retain, in the Source form of any Derivative Works that You distribute, all copyright, patent, trademark, and attribution notices from the Source form of the Work, excluding those notices that do not pertain to any part of the Derivative Works; and

      (d) If the Work includes a "NOTICE" text file as part of its distribution, then any Derivative Works that You distribute must include a readable copy of the attribution notices contained within such NOTICE file, excluding those notices that do not pertain to any part of the Derivative Works, in at least one of the following places: within a NOTICE text file distributed as part of the Derivative Works; within the Source form or documentation, if provided along with the Derivative Works; or, within a display generated by the Derivative Works, if and wherever such third-party notices normally appear. The contents of the NOTICE file are for informational purposes only and do not modify the License. You may add Your own attribution notices within Derivative Works that You distribute, alongside or as an addendum to the NOTICE text from the Work, provided that such additional attribution notices cannot be construed as modifying the License.

      You may add Your own copyright statement to Your modifications and may provide additional or different license terms and conditions for use, reproduction, or distribution of Your modifications, or for any such Derivative Works as a whole, provided Your use, reproduction, and distribution of the Work otherwise complies with the conditions stated in this License.

   5. Submission of Contributions. Unless You explicitly state otherwise, any Contribution intentionally submitted for inclusion in the Work by You to the Licensor shall be under the terms and conditions of this License, without any additional terms or conditions. Notwithstanding the above, nothing herein shall supersede or modify the terms of any separate license agreement you may have executed with Licensor regarding such Contributions.

   6. Trademarks. This License does not grant permission to use the trade names, trademarks, service marks, or product names of the Licensor, except as required for reasonable and customary use in describing the origin of the Work and reproducing the content of the NOTICE file.

   7. Disclaimer of Warranty. Unless required by applicable law or agreed to in writing, Licensor provides the Work (and each Contributor provides its Contributions) on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied, including, without limitation, any warranties or conditions of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A PARTICULAR PURPOSE. You are solely responsible for determining the appropriateness of using or redistributing the Work and assume any risks associated with Your exercise of permissions under this License.

   8. Limitation of Liability. In no event and under no legal theory, whether in tort (including negligence), contract, or otherwise, unless required by applicable law (such as deliberate and grossly negligent acts) or agreed to in writing, shall any Contributor be liable to You for damages, including any direct, indirect, special, incidental, or consequential damages of any character arising as a result of this License or out of the use or inability to use the Work (including but not limited to damages for loss of goodwill, work stoppage, computer failure or malfunction, or any and all other commercial damages or losses), even if such Contributor has been advised of the possibility of such damages.

   9. Accepting Warranty or Additional Liability. While redistributing the Work or Derivative Works thereof, You may choose to offer, and charge a fee for, acceptance of support, warranty, indemnity, or other liability obligations and/or rights consistent with this License. However, in accepting such obligations, You may act only on Your own behalf and on Your sole responsibility, not on behalf of any other Contributor, and only if You agree to indemnify, defend, and hold each Contributor harmless for any liability incurred by, or claims asserted against, such Contributor by reason of your accepting any such warranty or additional liability. END OF TERMS AND CONDITIONS

APPENDIX: How to apply the Apache License to your work.

To apply the Apache License to your work, attach the following boilerplate notice, with the fields enclosed by brackets "[]" replaced with your own identifying information. (Don't include the brackets!) The text should be enclosed in the appropriate comment syntax for the file format. We also recommend that a file or class name and description of purpose be included on the same "printed page" as the copyright notice for easier identification within third-party archives.

Copyright [yyyy] [name of copyright owner]

Licensed under the Apache License, Version 2.0 (the "License");

you may not use this file except in compliance with the License.

You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software

distributed under the License is distributed on an "AS IS" BASIS,

WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

See the License for the specific language governing permissions and

limitations under the License.

---------------------------------------------------------

---------------------------------------------------------

google/sentencepiece 8420f2179007c398c8b70f63cb12d8aec827397c - Apache-2.0


(c)o co
(c)ob cob
(c)oc coc
(c)od cod
(c)oe coe
(c)og cog
(c)oh coh
(c)oj coj
(c)ok cok
(c)ol col
(c)om com
(c)on con
(c)op cop
(c)os cos
(c)ot cot
(c) 2016 Unicode(r), Inc.
Copyright 2008 Google Inc.
Copyright 2012 Google Inc.
Copyright 2014 Google Inc.
Copyright 2016 Google Inc.
Copyright 2016 Google LLC.
Copyright 2018 Google Inc.
copyright Archive Literary
(c) (3) educational corporation
Copyright (c) 2006, Google Inc.
Copyright (c) 2008-2009 Yuta Mori
Copyright 2017 The Abseil Authors.
Copyright (c) 2008-2011, Susumu Yata
Copyright (c) 2010 Daisuke Okanohara


                                 Apache License
                           Version 2.0, January 2004
                        http://www.apache.org/licenses/

   TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION

   1. Definitions.

      "License" shall mean the terms and conditions for use, reproduction,
      and distribution as defined by Sections 1 through 9 of this document.

      "Licensor" shall mean the copyright owner or entity authorized by
      the copyright owner that is granting the License.

      "Legal Entity" shall mean the union of the acting entity and all
      other entities that control, are controlled by, or are under common
      control with that entity. For the purposes of this definition,
      "control" means (i) the power, direct or indirect, to cause the
      direction or management of such entity, whether by contract or
      otherwise, or (ii) ownership of fifty percent (50%) or more of the
      outstanding shares, or (iii) beneficial ownership of such entity.

      "You" (or "Your") shall mean an individual or Legal Entity
      exercising permissions granted by this License.

      "Source" form shall mean the preferred form for making modifications,
      including but not limited to software source code, documentation
      source, and configuration files.

      "Object" form shall mean any form resulting from mechanical
      transformation or translation of a Source form, including but
      not limited to compiled object code, generated documentation,
      and conversions to other media types.

      "Work" shall mean the work of authorship, whether in Source or
      Object form, made available under the License, as indicated by a
      copyright notice that is included in or attached to the work
      (an example is provided in the Appendix below).

      "Derivative Works" shall mean any work, whether in Source or Object
      form, that is based on (or derived from) the Work and for which the
      editorial revisions, annotations, elaborations, or other modifications
      represent, as a whole, an original work of authorship. For the purposes
      of this License, Derivative Works shall not include works that remain
      separable from, or merely link (or bind by name) to the interfaces of,
      the Work and Derivative Works thereof.

      "Contribution" shall mean any work of authorship, including
      the original version of the Work and any modifications or additions
      to that Work or Derivative Works thereof, that is intentionally
      submitted to Licensor for inclusion in the Work by the copyright owner
      or by an individual or Legal Entity authorized to submit on behalf of
      the copyright owner. For the purposes of this definition, "submitted"
      means any form of electronic, verbal, or written communication sent
      to the Licensor or its representatives, including but not limited to
      communication on electronic mailing lists, source code control systems,
      and issue tracking systems that are managed by, or on behalf of, the
      Licensor for the purpose of discussing and improving the Work, but
      excluding communication that is conspicuously marked or otherwise
      designated in writing by the copyright owner as "Not a Contribution."

      "Contributor" shall mean Licensor and any individual or Legal Entity
      on behalf of whom a Contribution has been received by Licensor and
      subsequently incorporated within the Work.

   2. Grant of Copyright License. Subject to the terms and conditions of
      this License, each Contributor hereby grants to You a perpetual,
      worldwide, non-exclusive, no-charge, royalty-free, irrevocable
      copyright license to reproduce, prepare Derivative Works of,
      publicly display, publicly perform, sublicense, and distribute the
      Work and such Derivative Works in Source or Object form.

   3. Grant of Patent License. Subject to the terms and conditions of
      this License, each Contributor hereby grants to You a perpetual,
      worldwide, non-exclusive, no-charge, royalty-free, irrevocable
      (except as stated in this section) patent license to make, have made,
      use, offer to sell, sell, import, and otherwise transfer the Work,
      where such license applies only to those patent claims licensable
      by such Contributor that are necessarily infringed by their
      Contribution(s) alone or by combination of their Contribution(s)
      with the Work to which such Contribution(s) was submitted. If You
      institute patent litigation against any entity (including a
      cross-claim or counterclaim in a lawsuit) alleging that the Work
      or a Contribution incorporated within the Work constitutes direct
      or contributory patent infringement, then any patent licenses
      granted to You under this License for that Work shall terminate
      as of the date such litigation is filed.

   4. Redistribution. You may reproduce and distribute copies of the
      Work or Derivative Works thereof in any medium, with or without
      modifications, and in Source or Object form, provided that You
      meet the following conditions:

      (a) You must give any other recipients of the Work or
          Derivative Works a copy of this License; and

      (b) You must cause any modified files to carry prominent notices
          stating that You changed the files; and

      (c) You must retain, in the Source form of any Derivative Works
          that You distribute, all copyright, patent, trademark, and
          attribution notices from the Source form of the Work,
          excluding those notices that do not pertain to any part of
          the Derivative Works; and

      (d) If the Work includes a "NOTICE" text file as part of its
          distribution, then any Derivative Works that You distribute must
          include a readable copy of the attribution notices contained
          within such NOTICE file, excluding those notices that do not
          pertain to any part of the Derivative Works, in at least one
          of the following places: within a NOTICE text file distributed
          as part of the Derivative Works; within the Source form or
          documentation, if provided along with the Derivative Works; or,
          within a display generated by the Derivative Works, if and
          wherever such third-party notices normally appear. The contents
          of the NOTICE file are for informational purposes only and
          do not modify the License. You may add Your own attribution
          notices within Derivative Works that You distribute, alongside
          or as an addendum to the NOTICE text from the Work, provided
          that such additional attribution notices cannot be construed
          as modifying the License.

      You may add Your own copyright statement to Your modifications and
      may provide additional or different license terms and conditions
      for use, reproduction, or distribution of Your modifications, or
      for any such Derivative Works as a whole, provided Your use,
      reproduction, and distribution of the Work otherwise complies with
      the conditions stated in this License.

   5. Submission of Contributions. Unless You explicitly state otherwise,
      any Contribution intentionally submitted for inclusion in the Work
      by You to the Licensor shall be under the terms and conditions of
      this License, without any additional terms or conditions.
      Notwithstanding the above, nothing herein shall supersede or modify
      the terms of any separate license agreement you may have executed
      with Licensor regarding such Contributions.

   6. Trademarks. This License does not grant permission to use the trade
      names, trademarks, service marks, or product names of the Licensor,
      except as required for reasonable and customary use in describing the
      origin of the Work and reproducing the content of the NOTICE file.

   7. Disclaimer of Warranty. Unless required by applicable law or
      agreed to in writing, Licensor provides the Work (and each
      Contributor provides its Contributions) on an "AS IS" BASIS,
      WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
      implied, including, without limitation, any warranties or conditions
      of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A
      PARTICULAR PURPOSE. You are solely responsible for determining the
      appropriateness of using or redistributing the Work and assume any
      risks associated with Your exercise of permissions under this License.

   8. Limitation of Liability. In no event and under no legal theory,
      whether in tort (including negligence), contract, or otherwise,
      unless required by applicable law (such as deliberate and grossly
      negligent acts) or agreed to in writing, shall any Contributor be
      liable to You for damages, including any direct, indirect, special,
      incidental, or consequential damages of any character arising as a
      result of this License or out of the use or inability to use the
      Work (including but not limited to damages for loss of goodwill,
      work stoppage, computer failure or malfunction, or any and all
      other commercial damages or losses), even if such Contributor
      has been advised of the possibility of such damages.

   9. Accepting Warranty or Additional Liability. While redistributing
      the Work or Derivative Works thereof, You may choose to offer,
      and charge a fee for, acceptance of support, warranty, indemnity,
      or other liability obligations and/or rights consistent with this
      License. However, in accepting such obligations, You may act only
      on Your own behalf and on Your sole responsibility, not on behalf
      of any other Contributor, and only if You agree to indemnify,
      defend, and hold each Contributor harmless for any liability
      incurred by, or claims asserted against, such Contributor by reason
      of your accepting any such warranty or additional liability.

   END OF TERMS AND CONDITIONS

   APPENDIX: How to apply the Apache License to your work.

      To apply the Apache License to your work, attach the following
      boilerplate notice, with the fields enclosed by brackets "[]"
      replaced with your own identifying information. (Don't include
      the brackets!)  The text should be enclosed in the appropriate
      comment syntax for the file format. We also recommend that a
      file or class name and description of purpose be included on the
      same "printed page" as the copyright notice for easier
      identification within third-party archives.

   Copyright [yyyy] [name of copyright owner]

   Licensed under the Apache License, Version 2.0 (the "License");
   you may not use this file except in compliance with the License.
   You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.


---------------------------------------------------------

---------------------------------------------------------

googlesamples/android-ndk 33789f3a04bf6dea13a50506aff2d96fd0bf396f - Apache-2.0


Copyright (c) Google Inc.
Copyright 2015 Google, Inc.
Copyright 2016 Google, Inc.
Copyright 2017 Google, Inc.
Copyright 2018 Google, Inc.
Copyright (c) 2013 Google Inc.
Copyright (c) 2015 Google Inc.
copyright 2008 by Glenn Kasten
Copyright (c) 2003 David Blythe
Copyright 2004-2005 Jetro Lauha
(c) The Android Open Source Project
Copyright (c) 2004-2005, Jetro Lauha
Copyright The Android Open Source Project
Copyright (c) 2012-2016 Intel Corporation.
copyrighted by the Free Software Foundation
Copyright (c) 1993 by Sun Microsystems, Inc.
Copyright (c) 2002, Industrial Light & Magic
Copyright (c) The Android Open Source Project
Copyright (c) 2007-2013 The Khronos Group Inc.
Copyright 2009 The Android Open Source Project
Copyright 2013 The Android Open Source Project
Copyright 2015 The Android Open Source Project
Copyright 2016 The Android Open Source Project
Copyright 2017 The Android Open Source Project
Copyright 2018 The Android Open Source Project
Copyright 2006, The Android Open Source Project
Copyright 2009, The Android Open Source Project
Copyright (c) 2007 The Android Open Source Project
Copyright (c) 2008 The Android Open Source Project
Copyright (c) 2009 The Android Open Source Project
Copyright (c) 2010 The Android Open Source Project
Copyright (c) 2011 The Android Open Source Project
Copyright (c) 2014 The Android Open Source Project
Copyright (c) 2015 The Android Open Source Project
Copyright (c) 2016 The Android Open Source Project
Copyright (c) 2017 The Android Open Source Project
Copyright (c) 2018 The Android Open Source Project
Copyright (c) 2006 G-Truc Creation (www.g-truc.net)
Copyright 2015 The Android Open Source Project, Inc.
Copyright (c) 1991, 1999 Free Software Foundation, Inc.
Copyright 2004-2005 Jetro Lauha Web http://iki.fi/jetro
Copyright (c) 2005 - 2012 G-Truc Creation (www.g-truc.net)
Copyright (c) 2005 - 2013 G-Truc Creation (www.g-truc.net)
Copyright (c) 2005 - 2014 G-Truc Creation (www.g-truc.net)

Apache License
Version 2.0, January 2004
http://www.apache.org/licenses/

TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION

1. Definitions.

"License" shall mean the terms and conditions for use, reproduction, and
distribution as defined by Sections 1 through 9 of this document.

"Licensor" shall mean the copyright owner or entity authorized by the copyright
owner that is granting the License.

"Legal Entity" shall mean the union of the acting entity and all other entities
that control, are controlled by, or are under common control with that entity.
For the purposes of this definition, "control" means (i) the power, direct or
indirect, to cause the direction or management of such entity, whether by
contract or otherwise, or (ii) ownership of fifty percent (50%) or more of the
outstanding shares, or (iii) beneficial ownership of such entity.

"You" (or "Your") shall mean an individual or Legal Entity exercising
permissions granted by this License.

"Source" form shall mean the preferred form for making modifications, including
but not limited to software source code, documentation source, and configuration
files.

"Object" form shall mean any form resulting from mechanical transformation or
translation of a Source form, including but not limited to compiled object code,
generated documentation, and conversions to other media types.

"Work" shall mean the work of authorship, whether in Source or Object form, made
available under the License, as indicated by a copyright notice that is included
in or attached to the work (an example is provided in the Appendix below).

"Derivative Works" shall mean any work, whether in Source or Object form, that
is based on (or derived from) the Work and for which the editorial revisions,
annotations, elaborations, or other modifications represent, as a whole, an
original work of authorship. For the purposes of this License, Derivative Works
shall not include works that remain separable from, or merely link (or bind by
name) to the interfaces of, the Work and Derivative Works thereof.

"Contribution" shall mean any work of authorship, including the original version
of the Work and any modifications or additions to that Work or Derivative Works
thereof, that is intentionally submitted to Licensor for inclusion in the Work
by the copyright owner or by an individual or Legal Entity authorized to submit
on behalf of the copyright owner. For the purposes of this definition,
"submitted" means any form of electronic, verbal, or written communication sent
to the Licensor or its representatives, including but not limited to
communication on electronic mailing lists, source code control systems, and
issue tracking systems that are managed by, or on behalf of, the Licensor for
the purpose of discussing and improving the Work, but excluding communication
that is conspicuously marked or otherwise designated in writing by the copyright
owner as "Not a Contribution."

"Contributor" shall mean Licensor and any individual or Legal Entity on behalf
of whom a Contribution has been received by Licensor and subsequently
incorporated within the Work.

2. Grant of Copyright License.

Subject to the terms and conditions of this License, each Contributor hereby
grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free,
irrevocable copyright license to reproduce, prepare Derivative Works of,
publicly display, publicly perform, sublicense, and distribute the Work and such
Derivative Works in Source or Object form.

3. Grant of Patent License.

Subject to the terms and conditions of this License, each Contributor hereby
grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free,
irrevocable (except as stated in this section) patent license to make, have
made, use, offer to sell, sell, import, and otherwise transfer the Work, where
such license applies only to those patent claims licensable by such Contributor
that are necessarily infringed by their Contribution(s) alone or by combination
of their Contribution(s) with the Work to which such Contribution(s) was
submitted. If You institute patent litigation against any entity (including a
cross-claim or counterclaim in a lawsuit) alleging that the Work or a
Contribution incorporated within the Work constitutes direct or contributory
patent infringement, then any patent licenses granted to You under this License
for that Work shall terminate as of the date such litigation is filed.

4. Redistribution.

You may reproduce and distribute copies of the Work or Derivative Works thereof
in any medium, with or without modifications, and in Source or Object form,
provided that You meet the following conditions:

You must give any other recipients of the Work or Derivative Works a copy of
this License; and
You must cause any modified files to carry prominent notices stating that You
changed the files; and
You must retain, in the Source form of any Derivative Works that You distribute,
all copyright, patent, trademark, and attribution notices from the Source form
of the Work, excluding those notices that do not pertain to any part of the
Derivative Works; and
If the Work includes a "NOTICE" text file as part of its distribution, then any
Derivative Works that You distribute must include a readable copy of the
attribution notices contained within such NOTICE file, excluding those notices
that do not pertain to any part of the Derivative Works, in at least one of the
following places: within a NOTICE text file distributed as part of the
Derivative Works; within the Source form or documentation, if provided along
with the Derivative Works; or, within a display generated by the Derivative
Works, if and wherever such third-party notices normally appear. The contents of
the NOTICE file are for informational purposes only and do not modify the
License. You may add Your own attribution notices within Derivative Works that
You distribute, alongside or as an addendum to the NOTICE text from the Work,
provided that such additional attribution notices cannot be construed as
modifying the License.
You may add Your own copyright statement to Your modifications and may provide
additional or different license terms and conditions for use, reproduction, or
distribution of Your modifications, or for any such Derivative Works as a whole,
provided Your use, reproduction, and distribution of the Work otherwise complies
with the conditions stated in this License.

5. Submission of Contributions.

Unless You explicitly state otherwise, any Contribution intentionally submitted
for inclusion in the Work by You to the Licensor shall be under the terms and
conditions of this License, without any additional terms or conditions.
Notwithstanding the above, nothing herein shall supersede or modify the terms of
any separate license agreement you may have executed with Licensor regarding
such Contributions.

6. Trademarks.

This License does not grant permission to use the trade names, trademarks,
service marks, or product names of the Licensor, except as required for
reasonable and customary use in describing the origin of the Work and
reproducing the content of the NOTICE file.

7. Disclaimer of Warranty.

Unless required by applicable law or agreed to in writing, Licensor provides the
Work (and each Contributor provides its Contributions) on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied,
including, without limitation, any warranties or conditions of TITLE,
NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A PARTICULAR PURPOSE. You are
solely responsible for determining the appropriateness of using or
redistributing the Work and assume any risks associated with Your exercise of
permissions under this License.

8. Limitation of Liability.

In no event and under no legal theory, whether in tort (including negligence),
contract, or otherwise, unless required by applicable law (such as deliberate
and grossly negligent acts) or agreed to in writing, shall any Contributor be
liable to You for damages, including any direct, indirect, special, incidental,
or consequential damages of any character arising as a result of this License or
out of the use or inability to use the Work (including but not limited to
damages for loss of goodwill, work stoppage, computer failure or malfunction, or
any and all other commercial damages or losses), even if such Contributor has
been advised of the possibility of such damages.

9. Accepting Warranty or Additional Liability.

While redistributing the Work or Derivative Works thereof, You may choose to
offer, and charge a fee for, acceptance of support, warranty, indemnity, or
other liability obligations and/or rights consistent with this License. However,
in accepting such obligations, You may act only on Your own behalf and on Your
sole responsibility, not on behalf of any other Contributor, and only if You
agree to indemnify, defend, and hold each Contributor harmless for any liability
incurred by, or claims asserted against, such Contributor by reason of your
accepting any such warranty or additional liability.

END OF TERMS AND CONDITIONS

APPENDIX: How to apply the Apache License to your work

To apply the Apache License to your work, attach the following boilerplate
notice, with the fields enclosed by brackets "[]" replaced with your own
identifying information. (Don't include the brackets!) The text should be
enclosed in the appropriate comment syntax for the file format. We also
recommend that a file or class name and description of purpose be included on
the same "printed page" as the copyright notice for easier identification within
third-party archives.

   Copyright [yyyy] [name of copyright owner]

   Licensed under the Apache License, Version 2.0 (the "License");
   you may not use this file except in compliance with the License.
   You may obtain a copy of the License at

     http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.

---------------------------------------------------------

---------------------------------------------------------

importlib-metadata 6.0.0 - Apache-2.0



Apache License

Version 2.0, January 2004

http://www.apache.org/licenses/ TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION

   1. Definitions.

      

      "License" shall mean the terms and conditions for use, reproduction, and distribution as defined by Sections 1 through 9 of this document.

      

      "Licensor" shall mean the copyright owner or entity authorized by the copyright owner that is granting the License.

      

      "Legal Entity" shall mean the union of the acting entity and all other entities that control, are controlled by, or are under common control with that entity. For the purposes of this definition, "control" means (i) the power, direct or indirect, to cause the direction or management of such entity, whether by contract or otherwise, or (ii) ownership of fifty percent (50%) or more of the outstanding shares, or (iii) beneficial ownership of such entity.

      

      "You" (or "Your") shall mean an individual or Legal Entity exercising permissions granted by this License.

      

      "Source" form shall mean the preferred form for making modifications, including but not limited to software source code, documentation source, and configuration files.

      

      "Object" form shall mean any form resulting from mechanical transformation or translation of a Source form, including but not limited to compiled object code, generated documentation, and conversions to other media types.

      

      "Work" shall mean the work of authorship, whether in Source or Object form, made available under the License, as indicated by a copyright notice that is included in or attached to the work (an example is provided in the Appendix below).

      

      "Derivative Works" shall mean any work, whether in Source or Object form, that is based on (or derived from) the Work and for which the editorial revisions, annotations, elaborations, or other modifications represent, as a whole, an original work of authorship. For the purposes of this License, Derivative Works shall not include works that remain separable from, or merely link (or bind by name) to the interfaces of, the Work and Derivative Works thereof.

      

      "Contribution" shall mean any work of authorship, including the original version of the Work and any modifications or additions to that Work or Derivative Works thereof, that is intentionally submitted to Licensor for inclusion in the Work by the copyright owner or by an individual or Legal Entity authorized to submit on behalf of the copyright owner. For the purposes of this definition, "submitted" means any form of electronic, verbal, or written communication sent to the Licensor or its representatives, including but not limited to communication on electronic mailing lists, source code control systems, and issue tracking systems that are managed by, or on behalf of, the Licensor for the purpose of discussing and improving the Work, but excluding communication that is conspicuously marked or otherwise designated in writing by the copyright owner as "Not a Contribution."

      

      "Contributor" shall mean Licensor and any individual or Legal Entity on behalf of whom a Contribution has been received by Licensor and subsequently incorporated within the Work.

   2. Grant of Copyright License. Subject to the terms and conditions of this License, each Contributor hereby grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable copyright license to reproduce, prepare Derivative Works of, publicly display, publicly perform, sublicense, and distribute the Work and such Derivative Works in Source or Object form.

   3. Grant of Patent License. Subject to the terms and conditions of this License, each Contributor hereby grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable (except as stated in this section) patent license to make, have made, use, offer to sell, sell, import, and otherwise transfer the Work, where such license applies only to those patent claims licensable by such Contributor that are necessarily infringed by their Contribution(s) alone or by combination of their Contribution(s) with the Work to which such Contribution(s) was submitted. If You institute patent litigation against any entity (including a cross-claim or counterclaim in a lawsuit) alleging that the Work or a Contribution incorporated within the Work constitutes direct or contributory patent infringement, then any patent licenses granted to You under this License for that Work shall terminate as of the date such litigation is filed.

   4. Redistribution. You may reproduce and distribute copies of the Work or Derivative Works thereof in any medium, with or without modifications, and in Source or Object form, provided that You meet the following conditions:

      (a) You must give any other recipients of the Work or Derivative Works a copy of this License; and

      (b) You must cause any modified files to carry prominent notices stating that You changed the files; and

      (c) You must retain, in the Source form of any Derivative Works that You distribute, all copyright, patent, trademark, and attribution notices from the Source form of the Work, excluding those notices that do not pertain to any part of the Derivative Works; and

      (d) If the Work includes a "NOTICE" text file as part of its distribution, then any Derivative Works that You distribute must include a readable copy of the attribution notices contained within such NOTICE file, excluding those notices that do not pertain to any part of the Derivative Works, in at least one of the following places: within a NOTICE text file distributed as part of the Derivative Works; within the Source form or documentation, if provided along with the Derivative Works; or, within a display generated by the Derivative Works, if and wherever such third-party notices normally appear. The contents of the NOTICE file are for informational purposes only and do not modify the License. You may add Your own attribution notices within Derivative Works that You distribute, alongside or as an addendum to the NOTICE text from the Work, provided that such additional attribution notices cannot be construed as modifying the License.

      You may add Your own copyright statement to Your modifications and may provide additional or different license terms and conditions for use, reproduction, or distribution of Your modifications, or for any such Derivative Works as a whole, provided Your use, reproduction, and distribution of the Work otherwise complies with the conditions stated in this License.

   5. Submission of Contributions. Unless You explicitly state otherwise, any Contribution intentionally submitted for inclusion in the Work by You to the Licensor shall be under the terms and conditions of this License, without any additional terms or conditions. Notwithstanding the above, nothing herein shall supersede or modify the terms of any separate license agreement you may have executed with Licensor regarding such Contributions.

   6. Trademarks. This License does not grant permission to use the trade names, trademarks, service marks, or product names of the Licensor, except as required for reasonable and customary use in describing the origin of the Work and reproducing the content of the NOTICE file.

   7. Disclaimer of Warranty. Unless required by applicable law or agreed to in writing, Licensor provides the Work (and each Contributor provides its Contributions) on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied, including, without limitation, any warranties or conditions of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A PARTICULAR PURPOSE. You are solely responsible for determining the appropriateness of using or redistributing the Work and assume any risks associated with Your exercise of permissions under this License.

   8. Limitation of Liability. In no event and under no legal theory, whether in tort (including negligence), contract, or otherwise, unless required by applicable law (such as deliberate and grossly negligent acts) or agreed to in writing, shall any Contributor be liable to You for damages, including any direct, indirect, special, incidental, or consequential damages of any character arising as a result of this License or out of the use or inability to use the Work (including but not limited to damages for loss of goodwill, work stoppage, computer failure or malfunction, or any and all other commercial damages or losses), even if such Contributor has been advised of the possibility of such damages.

   9. Accepting Warranty or Additional Liability. While redistributing the Work or Derivative Works thereof, You may choose to offer, and charge a fee for, acceptance of support, warranty, indemnity, or other liability obligations and/or rights consistent with this License. However, in accepting such obligations, You may act only on Your own behalf and on Your sole responsibility, not on behalf of any other Contributor, and only if You agree to indemnify, defend, and hold each Contributor harmless for any liability incurred by, or claims asserted against, such Contributor by reason of your accepting any such warranty or additional liability. END OF TERMS AND CONDITIONS

APPENDIX: How to apply the Apache License to your work.

To apply the Apache License to your work, attach the following boilerplate notice, with the fields enclosed by brackets "[]" replaced with your own identifying information. (Don't include the brackets!) The text should be enclosed in the appropriate comment syntax for the file format. We also recommend that a file or class name and description of purpose be included on the same "printed page" as the copyright notice for easier identification within third-party archives.

Copyright [yyyy] [name of copyright owner]

Licensed under the Apache License, Version 2.0 (the "License");

you may not use this file except in compliance with the License.

You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software

distributed under the License is distributed on an "AS IS" BASIS,

WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

See the License for the specific language governing permissions and

limitations under the License.

---------------------------------------------------------

---------------------------------------------------------

importlib-resources 5.10.2 - Apache-2.0



Apache License

Version 2.0, January 2004

http://www.apache.org/licenses/ TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION

   1. Definitions.

      

      "License" shall mean the terms and conditions for use, reproduction, and distribution as defined by Sections 1 through 9 of this document.

      

      "Licensor" shall mean the copyright owner or entity authorized by the copyright owner that is granting the License.

      

      "Legal Entity" shall mean the union of the acting entity and all other entities that control, are controlled by, or are under common control with that entity. For the purposes of this definition, "control" means (i) the power, direct or indirect, to cause the direction or management of such entity, whether by contract or otherwise, or (ii) ownership of fifty percent (50%) or more of the outstanding shares, or (iii) beneficial ownership of such entity.

      

      "You" (or "Your") shall mean an individual or Legal Entity exercising permissions granted by this License.

      

      "Source" form shall mean the preferred form for making modifications, including but not limited to software source code, documentation source, and configuration files.

      

      "Object" form shall mean any form resulting from mechanical transformation or translation of a Source form, including but not limited to compiled object code, generated documentation, and conversions to other media types.

      

      "Work" shall mean the work of authorship, whether in Source or Object form, made available under the License, as indicated by a copyright notice that is included in or attached to the work (an example is provided in the Appendix below).

      

      "Derivative Works" shall mean any work, whether in Source or Object form, that is based on (or derived from) the Work and for which the editorial revisions, annotations, elaborations, or other modifications represent, as a whole, an original work of authorship. For the purposes of this License, Derivative Works shall not include works that remain separable from, or merely link (or bind by name) to the interfaces of, the Work and Derivative Works thereof.

      

      "Contribution" shall mean any work of authorship, including the original version of the Work and any modifications or additions to that Work or Derivative Works thereof, that is intentionally submitted to Licensor for inclusion in the Work by the copyright owner or by an individual or Legal Entity authorized to submit on behalf of the copyright owner. For the purposes of this definition, "submitted" means any form of electronic, verbal, or written communication sent to the Licensor or its representatives, including but not limited to communication on electronic mailing lists, source code control systems, and issue tracking systems that are managed by, or on behalf of, the Licensor for the purpose of discussing and improving the Work, but excluding communication that is conspicuously marked or otherwise designated in writing by the copyright owner as "Not a Contribution."

      

      "Contributor" shall mean Licensor and any individual or Legal Entity on behalf of whom a Contribution has been received by Licensor and subsequently incorporated within the Work.

   2. Grant of Copyright License. Subject to the terms and conditions of this License, each Contributor hereby grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable copyright license to reproduce, prepare Derivative Works of, publicly display, publicly perform, sublicense, and distribute the Work and such Derivative Works in Source or Object form.

   3. Grant of Patent License. Subject to the terms and conditions of this License, each Contributor hereby grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable (except as stated in this section) patent license to make, have made, use, offer to sell, sell, import, and otherwise transfer the Work, where such license applies only to those patent claims licensable by such Contributor that are necessarily infringed by their Contribution(s) alone or by combination of their Contribution(s) with the Work to which such Contribution(s) was submitted. If You institute patent litigation against any entity (including a cross-claim or counterclaim in a lawsuit) alleging that the Work or a Contribution incorporated within the Work constitutes direct or contributory patent infringement, then any patent licenses granted to You under this License for that Work shall terminate as of the date such litigation is filed.

   4. Redistribution. You may reproduce and distribute copies of the Work or Derivative Works thereof in any medium, with or without modifications, and in Source or Object form, provided that You meet the following conditions:

      (a) You must give any other recipients of the Work or Derivative Works a copy of this License; and

      (b) You must cause any modified files to carry prominent notices stating that You changed the files; and

      (c) You must retain, in the Source form of any Derivative Works that You distribute, all copyright, patent, trademark, and attribution notices from the Source form of the Work, excluding those notices that do not pertain to any part of the Derivative Works; and

      (d) If the Work includes a "NOTICE" text file as part of its distribution, then any Derivative Works that You distribute must include a readable copy of the attribution notices contained within such NOTICE file, excluding those notices that do not pertain to any part of the Derivative Works, in at least one of the following places: within a NOTICE text file distributed as part of the Derivative Works; within the Source form or documentation, if provided along with the Derivative Works; or, within a display generated by the Derivative Works, if and wherever such third-party notices normally appear. The contents of the NOTICE file are for informational purposes only and do not modify the License. You may add Your own attribution notices within Derivative Works that You distribute, alongside or as an addendum to the NOTICE text from the Work, provided that such additional attribution notices cannot be construed as modifying the License.

      You may add Your own copyright statement to Your modifications and may provide additional or different license terms and conditions for use, reproduction, or distribution of Your modifications, or for any such Derivative Works as a whole, provided Your use, reproduction, and distribution of the Work otherwise complies with the conditions stated in this License.

   5. Submission of Contributions. Unless You explicitly state otherwise, any Contribution intentionally submitted for inclusion in the Work by You to the Licensor shall be under the terms and conditions of this License, without any additional terms or conditions. Notwithstanding the above, nothing herein shall supersede or modify the terms of any separate license agreement you may have executed with Licensor regarding such Contributions.

   6. Trademarks. This License does not grant permission to use the trade names, trademarks, service marks, or product names of the Licensor, except as required for reasonable and customary use in describing the origin of the Work and reproducing the content of the NOTICE file.

   7. Disclaimer of Warranty. Unless required by applicable law or agreed to in writing, Licensor provides the Work (and each Contributor provides its Contributions) on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied, including, without limitation, any warranties or conditions of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A PARTICULAR PURPOSE. You are solely responsible for determining the appropriateness of using or redistributing the Work and assume any risks associated with Your exercise of permissions under this License.

   8. Limitation of Liability. In no event and under no legal theory, whether in tort (including negligence), contract, or otherwise, unless required by applicable law (such as deliberate and grossly negligent acts) or agreed to in writing, shall any Contributor be liable to You for damages, including any direct, indirect, special, incidental, or consequential damages of any character arising as a result of this License or out of the use or inability to use the Work (including but not limited to damages for loss of goodwill, work stoppage, computer failure or malfunction, or any and all other commercial damages or losses), even if such Contributor has been advised of the possibility of such damages.

   9. Accepting Warranty or Additional Liability. While redistributing the Work or Derivative Works thereof, You may choose to offer, and charge a fee for, acceptance of support, warranty, indemnity, or other liability obligations and/or rights consistent with this License. However, in accepting such obligations, You may act only on Your own behalf and on Your sole responsibility, not on behalf of any other Contributor, and only if You agree to indemnify, defend, and hold each Contributor harmless for any liability incurred by, or claims asserted against, such Contributor by reason of your accepting any such warranty or additional liability. END OF TERMS AND CONDITIONS

APPENDIX: How to apply the Apache License to your work.

To apply the Apache License to your work, attach the following boilerplate notice, with the fields enclosed by brackets "[]" replaced with your own identifying information. (Don't include the brackets!) The text should be enclosed in the appropriate comment syntax for the file format. We also recommend that a file or class name and description of purpose be included on the same "printed page" as the copyright notice for easier identification within third-party archives.

Copyright [yyyy] [name of copyright owner]

Licensed under the Apache License, Version 2.0 (the "License");

you may not use this file except in compliance with the License.

You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software

distributed under the License is distributed on an "AS IS" BASIS,

WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

See the License for the specific language governing permissions and

limitations under the License.

---------------------------------------------------------

---------------------------------------------------------

java-diff-utils/java-diff-utils ab1e38c57b39ec6f9bd48bcbf7f696321616218a - Apache-2.0


Copyright 2017 java-diff-utils
Copyright 2018 java-diff-utils
Copyright 2019 java-diff-utils
Copyright 2020 java-diff-utils
Copyright 2021 java-diff-utils
Copyright 2009-2017 java-diff-utils
Copyright 2009-2017 java-diff-utills
Copyright 2010-2018 Amazon.com, Inc.
Copyright 2015-2018 Amazon.com, Inc.
Portions copyright 2006-2009 James Murty
Copyright (c) 2006 Johannes E. Schindelin
Copyright (c) 2009 - 2017 java-diff-utils
Copyright 2011-2018 Amazon Technologies, Inc.
Copyright 2012-2018 Amazon Technologies, Inc.
Copyright 2013-2018 Amazon Technologies, Inc.
Copyright 2014-2018 Amazon Technologies, Inc.
Copyright 2015-2018 Amazon Technologies, Inc.
Copyright (c) 2016 Amazon.com, Inc. or its affiliates
Copyright (c) 2016. Amazon.com, Inc. or its affiliates
Copyright 2010-2018 Amazon.com, Inc. or its affiliates
Copyright 2011-2018 Amazon.com, Inc. or its affiliates
Copyright 2012-2018 Amazon.com, Inc. or its affiliates
Copyright 2013-2018 Amazon.com, Inc. or its affiliates
Copyright 2014-2018 Amazon.com, Inc. or its affiliates
Copyright 2015-2018 Amazon.com, Inc. or its affiliates
Copyright 2016-2018 Amazon.com, Inc. or its affiliates
Copyright (c) 2004, PostgreSQL Global Development Group
Copyright (c) 2017, PostgreSQL Global Development Group
Copyright (c) 2018, PostgreSQL Global Development Group

                                 Apache License
                           Version 2.0, January 2004
                        http://www.apache.org/licenses/

   TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION

   1. Definitions.

      "License" shall mean the terms and conditions for use, reproduction,
      and distribution as defined by Sections 1 through 9 of this document.

      "Licensor" shall mean the copyright owner or entity authorized by
      the copyright owner that is granting the License.

      "Legal Entity" shall mean the union of the acting entity and all
      other entities that control, are controlled by, or are under common
      control with that entity. For the purposes of this definition,
      "control" means (i) the power, direct or indirect, to cause the
      direction or management of such entity, whether by contract or
      otherwise, or (ii) ownership of fifty percent (50%) or more of the
      outstanding shares, or (iii) beneficial ownership of such entity.

      "You" (or "Your") shall mean an individual or Legal Entity
      exercising permissions granted by this License.

      "Source" form shall mean the preferred form for making modifications,
      including but not limited to software source code, documentation
      source, and configuration files.

      "Object" form shall mean any form resulting from mechanical
      transformation or translation of a Source form, including but
      not limited to compiled object code, generated documentation,
      and conversions to other media types.

      "Work" shall mean the work of authorship, whether in Source or
      Object form, made available under the License, as indicated by a
      copyright notice that is included in or attached to the work
      (an example is provided in the Appendix below).

      "Derivative Works" shall mean any work, whether in Source or Object
      form, that is based on (or derived from) the Work and for which the
      editorial revisions, annotations, elaborations, or other modifications
      represent, as a whole, an original work of authorship. For the purposes
      of this License, Derivative Works shall not include works that remain
      separable from, or merely link (or bind by name) to the interfaces of,
      the Work and Derivative Works thereof.

      "Contribution" shall mean any work of authorship, including
      the original version of the Work and any modifications or additions
      to that Work or Derivative Works thereof, that is intentionally
      submitted to Licensor for inclusion in the Work by the copyright owner
      or by an individual or Legal Entity authorized to submit on behalf of
      the copyright owner. For the purposes of this definition, "submitted"
      means any form of electronic, verbal, or written communication sent
      to the Licensor or its representatives, including but not limited to
      communication on electronic mailing lists, source code control systems,
      and issue tracking systems that are managed by, or on behalf of, the
      Licensor for the purpose of discussing and improving the Work, but
      excluding communication that is conspicuously marked or otherwise
      designated in writing by the copyright owner as "Not a Contribution."

      "Contributor" shall mean Licensor and any individual or Legal Entity
      on behalf of whom a Contribution has been received by Licensor and
      subsequently incorporated within the Work.

   2. Grant of Copyright License. Subject to the terms and conditions of
      this License, each Contributor hereby grants to You a perpetual,
      worldwide, non-exclusive, no-charge, royalty-free, irrevocable
      copyright license to reproduce, prepare Derivative Works of,
      publicly display, publicly perform, sublicense, and distribute the
      Work and such Derivative Works in Source or Object form.

   3. Grant of Patent License. Subject to the terms and conditions of
      this License, each Contributor hereby grants to You a perpetual,
      worldwide, non-exclusive, no-charge, royalty-free, irrevocable
      (except as stated in this section) patent license to make, have made,
      use, offer to sell, sell, import, and otherwise transfer the Work,
      where such license applies only to those patent claims licensable
      by such Contributor that are necessarily infringed by their
      Contribution(s) alone or by combination of their Contribution(s)
      with the Work to which such Contribution(s) was submitted. If You
      institute patent litigation against any entity (including a
      cross-claim or counterclaim in a lawsuit) alleging that the Work
      or a Contribution incorporated within the Work constitutes direct
      or contributory patent infringement, then any patent licenses
      granted to You under this License for that Work shall terminate
      as of the date such litigation is filed.

   4. Redistribution. You may reproduce and distribute copies of the
      Work or Derivative Works thereof in any medium, with or without
      modifications, and in Source or Object form, provided that You
      meet the following conditions:

      (a) You must give any other recipients of the Work or
          Derivative Works a copy of this License; and

      (b) You must cause any modified files to carry prominent notices
          stating that You changed the files; and

      (c) You must retain, in the Source form of any Derivative Works
          that You distribute, all copyright, patent, trademark, and
          attribution notices from the Source form of the Work,
          excluding those notices that do not pertain to any part of
          the Derivative Works; and

      (d) If the Work includes a "NOTICE" text file as part of its
          distribution, then any Derivative Works that You distribute must
          include a readable copy of the attribution notices contained
          within such NOTICE file, excluding those notices that do not
          pertain to any part of the Derivative Works, in at least one
          of the following places: within a NOTICE text file distributed
          as part of the Derivative Works; within the Source form or
          documentation, if provided along with the Derivative Works; or,
          within a display generated by the Derivative Works, if and
          wherever such third-party notices normally appear. The contents
          of the NOTICE file are for informational purposes only and
          do not modify the License. You may add Your own attribution
          notices within Derivative Works that You distribute, alongside
          or as an addendum to the NOTICE text from the Work, provided
          that such additional attribution notices cannot be construed
          as modifying the License.

      You may add Your own copyright statement to Your modifications and
      may provide additional or different license terms and conditions
      for use, reproduction, or distribution of Your modifications, or
      for any such Derivative Works as a whole, provided Your use,
      reproduction, and distribution of the Work otherwise complies with
      the conditions stated in this License.

   5. Submission of Contributions. Unless You explicitly state otherwise,
      any Contribution intentionally submitted for inclusion in the Work
      by You to the Licensor shall be under the terms and conditions of
      this License, without any additional terms or conditions.
      Notwithstanding the above, nothing herein shall supersede or modify
      the terms of any separate license agreement you may have executed
      with Licensor regarding such Contributions.

   6. Trademarks. This License does not grant permission to use the trade
      names, trademarks, service marks, or product names of the Licensor,
      except as required for reasonable and customary use in describing the
      origin of the Work and reproducing the content of the NOTICE file.

   7. Disclaimer of Warranty. Unless required by applicable law or
      agreed to in writing, Licensor provides the Work (and each
      Contributor provides its Contributions) on an "AS IS" BASIS,
      WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
      implied, including, without limitation, any warranties or conditions
      of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A
      PARTICULAR PURPOSE. You are solely responsible for determining the
      appropriateness of using or redistributing the Work and assume any
      risks associated with Your exercise of permissions under this License.

   8. Limitation of Liability. In no event and under no legal theory,
      whether in tort (including negligence), contract, or otherwise,
      unless required by applicable law (such as deliberate and grossly
      negligent acts) or agreed to in writing, shall any Contributor be
      liable to You for damages, including any direct, indirect, special,
      incidental, or consequential damages of any character arising as a
      result of this License or out of the use or inability to use the
      Work (including but not limited to damages for loss of goodwill,
      work stoppage, computer failure or malfunction, or any and all
      other commercial damages or losses), even if such Contributor
      has been advised of the possibility of such damages.

   9. Accepting Warranty or Additional Liability. While redistributing
      the Work or Derivative Works thereof, You may choose to offer,
      and charge a fee for, acceptance of support, warranty, indemnity,
      or other liability obligations and/or rights consistent with this
      License. However, in accepting such obligations, You may act only
      on Your own behalf and on Your sole responsibility, not on behalf
      of any other Contributor, and only if You agree to indemnify,
      defend, and hold each Contributor harmless for any liability
      incurred by, or claims asserted against, such Contributor by reason
      of your accepting any such warranty or additional liability.

   END OF TERMS AND CONDITIONS

   APPENDIX: How to apply the Apache License to your work.

      To apply the Apache License to your work, attach the following
      boilerplate notice, with the fields enclosed by brackets "[]"
      replaced with your own identifying information. (Don't include
      the brackets!)  The text should be enclosed in the appropriate
      comment syntax for the file format. We also recommend that a
      file or class name and description of purpose be included on the
      same "printed page" as the copyright notice for easier
      identification within third-party archives.

   Copyright [yyyy] [name of copyright owner]

   Licensed under the Apache License, Version 2.0 (the "License");
   you may not use this file except in compliance with the License.
   You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.


---------------------------------------------------------

---------------------------------------------------------

joda-time/joda-time 2.10.8 - Apache-2.0



Apache License

Version 2.0, January 2004

http://www.apache.org/licenses/ TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION

   1. Definitions.

      

      "License" shall mean the terms and conditions for use, reproduction, and distribution as defined by Sections 1 through 9 of this document.

      

      "Licensor" shall mean the copyright owner or entity authorized by the copyright owner that is granting the License.

      

      "Legal Entity" shall mean the union of the acting entity and all other entities that control, are controlled by, or are under common control with that entity. For the purposes of this definition, "control" means (i) the power, direct or indirect, to cause the direction or management of such entity, whether by contract or otherwise, or (ii) ownership of fifty percent (50%) or more of the outstanding shares, or (iii) beneficial ownership of such entity.

      

      "You" (or "Your") shall mean an individual or Legal Entity exercising permissions granted by this License.

      

      "Source" form shall mean the preferred form for making modifications, including but not limited to software source code, documentation source, and configuration files.

      

      "Object" form shall mean any form resulting from mechanical transformation or translation of a Source form, including but not limited to compiled object code, generated documentation, and conversions to other media types.

      

      "Work" shall mean the work of authorship, whether in Source or Object form, made available under the License, as indicated by a copyright notice that is included in or attached to the work (an example is provided in the Appendix below).

      

      "Derivative Works" shall mean any work, whether in Source or Object form, that is based on (or derived from) the Work and for which the editorial revisions, annotations, elaborations, or other modifications represent, as a whole, an original work of authorship. For the purposes of this License, Derivative Works shall not include works that remain separable from, or merely link (or bind by name) to the interfaces of, the Work and Derivative Works thereof.

      

      "Contribution" shall mean any work of authorship, including the original version of the Work and any modifications or additions to that Work or Derivative Works thereof, that is intentionally submitted to Licensor for inclusion in the Work by the copyright owner or by an individual or Legal Entity authorized to submit on behalf of the copyright owner. For the purposes of this definition, "submitted" means any form of electronic, verbal, or written communication sent to the Licensor or its representatives, including but not limited to communication on electronic mailing lists, source code control systems, and issue tracking systems that are managed by, or on behalf of, the Licensor for the purpose of discussing and improving the Work, but excluding communication that is conspicuously marked or otherwise designated in writing by the copyright owner as "Not a Contribution."

      

      "Contributor" shall mean Licensor and any individual or Legal Entity on behalf of whom a Contribution has been received by Licensor and subsequently incorporated within the Work.

   2. Grant of Copyright License. Subject to the terms and conditions of this License, each Contributor hereby grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable copyright license to reproduce, prepare Derivative Works of, publicly display, publicly perform, sublicense, and distribute the Work and such Derivative Works in Source or Object form.

   3. Grant of Patent License. Subject to the terms and conditions of this License, each Contributor hereby grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable (except as stated in this section) patent license to make, have made, use, offer to sell, sell, import, and otherwise transfer the Work, where such license applies only to those patent claims licensable by such Contributor that are necessarily infringed by their Contribution(s) alone or by combination of their Contribution(s) with the Work to which such Contribution(s) was submitted. If You institute patent litigation against any entity (including a cross-claim or counterclaim in a lawsuit) alleging that the Work or a Contribution incorporated within the Work constitutes direct or contributory patent infringement, then any patent licenses granted to You under this License for that Work shall terminate as of the date such litigation is filed.

   4. Redistribution. You may reproduce and distribute copies of the Work or Derivative Works thereof in any medium, with or without modifications, and in Source or Object form, provided that You meet the following conditions:

      (a) You must give any other recipients of the Work or Derivative Works a copy of this License; and

      (b) You must cause any modified files to carry prominent notices stating that You changed the files; and

      (c) You must retain, in the Source form of any Derivative Works that You distribute, all copyright, patent, trademark, and attribution notices from the Source form of the Work, excluding those notices that do not pertain to any part of the Derivative Works; and

      (d) If the Work includes a "NOTICE" text file as part of its distribution, then any Derivative Works that You distribute must include a readable copy of the attribution notices contained within such NOTICE file, excluding those notices that do not pertain to any part of the Derivative Works, in at least one of the following places: within a NOTICE text file distributed as part of the Derivative Works; within the Source form or documentation, if provided along with the Derivative Works; or, within a display generated by the Derivative Works, if and wherever such third-party notices normally appear. The contents of the NOTICE file are for informational purposes only and do not modify the License. You may add Your own attribution notices within Derivative Works that You distribute, alongside or as an addendum to the NOTICE text from the Work, provided that such additional attribution notices cannot be construed as modifying the License.

      You may add Your own copyright statement to Your modifications and may provide additional or different license terms and conditions for use, reproduction, or distribution of Your modifications, or for any such Derivative Works as a whole, provided Your use, reproduction, and distribution of the Work otherwise complies with the conditions stated in this License.

   5. Submission of Contributions. Unless You explicitly state otherwise, any Contribution intentionally submitted for inclusion in the Work by You to the Licensor shall be under the terms and conditions of this License, without any additional terms or conditions. Notwithstanding the above, nothing herein shall supersede or modify the terms of any separate license agreement you may have executed with Licensor regarding such Contributions.

   6. Trademarks. This License does not grant permission to use the trade names, trademarks, service marks, or product names of the Licensor, except as required for reasonable and customary use in describing the origin of the Work and reproducing the content of the NOTICE file.

   7. Disclaimer of Warranty. Unless required by applicable law or agreed to in writing, Licensor provides the Work (and each Contributor provides its Contributions) on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied, including, without limitation, any warranties or conditions of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A PARTICULAR PURPOSE. You are solely responsible for determining the appropriateness of using or redistributing the Work and assume any risks associated with Your exercise of permissions under this License.

   8. Limitation of Liability. In no event and under no legal theory, whether in tort (including negligence), contract, or otherwise, unless required by applicable law (such as deliberate and grossly negligent acts) or agreed to in writing, shall any Contributor be liable to You for damages, including any direct, indirect, special, incidental, or consequential damages of any character arising as a result of this License or out of the use or inability to use the Work (including but not limited to damages for loss of goodwill, work stoppage, computer failure or malfunction, or any and all other commercial damages or losses), even if such Contributor has been advised of the possibility of such damages.

   9. Accepting Warranty or Additional Liability. While redistributing the Work or Derivative Works thereof, You may choose to offer, and charge a fee for, acceptance of support, warranty, indemnity, or other liability obligations and/or rights consistent with this License. However, in accepting such obligations, You may act only on Your own behalf and on Your sole responsibility, not on behalf of any other Contributor, and only if You agree to indemnify, defend, and hold each Contributor harmless for any liability incurred by, or claims asserted against, such Contributor by reason of your accepting any such warranty or additional liability. END OF TERMS AND CONDITIONS

APPENDIX: How to apply the Apache License to your work.

To apply the Apache License to your work, attach the following boilerplate notice, with the fields enclosed by brackets "[]" replaced with your own identifying information. (Don't include the brackets!) The text should be enclosed in the appropriate comment syntax for the file format. We also recommend that a file or class name and description of purpose be included on the same "printed page" as the copyright notice for easier identification within third-party archives.

Copyright [yyyy] [name of copyright owner]

Licensed under the Apache License, Version 2.0 (the "License");

you may not use this file except in compliance with the License.

You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software

distributed under the License is distributed on an "AS IS" BASIS,

WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

See the License for the specific language governing permissions and

limitations under the License.

---------------------------------------------------------

---------------------------------------------------------

microsoft/cpp_client_telemetry 0fa60bb679be6ec0e8eb50d73b1862126693b44b - Apache-2.0


Copyright (c) 2019
(c) 2008 Google Inc.
CoprocPair IntPair I64Regs
Copyright 2003 Google Inc.
Copyright 2005 Google Inc.
Copyright 2007 Google Inc.
Copyright 2008 Google Inc.
Copyright 2009 Google Inc.
Copyright 2010 Google Inc.
Copyright 2013 Google Inc.
Copyright 2003, Google Inc.
Copyright 2005, Google Inc.
Copyright 2006, Google Inc.
Copyright 2007 Neal Norwitz
Copyright 2007, Google Inc.
Copyright 2008, Google Inc.
Copyright 2009 Neal Norwitz
Copyright 2009, Google Inc.
Copyright 2010, Google Inc.
Copyright 2013, Google Inc.
Copyright 2015, Google Inc.
Copyright (c) 2003 Mark Adler
Copyright (c) 2011 Mark Adler
Copyright (c) by P.J. Plauger
(c) Copyright Henrik Ravn 2004
Copyright (c) Henrik Ravn 2004
Copyright 1995-2013 Mark Adler
Copyright 1995-2017 Mark Adler
Copyright (c) 2003 Cosmin Truta.
Copyright (c) 1990-2000 Info-ZIP.
Copyright (c) 2004 by Henrik Ravn
Copyright (c) 2011, Tony Million.
Copyright (c) 1995-2003 Mark Adler
Copyright (c) 1995-2008 Mark Adler
Copyright (c) 1995-2013 Mark Adler
Copyright (c) 1995-2016 Mark Adler
Copyright (c) 1995-2017 Mark Adler
Copyright (c) 1998 by Bob Dellaca.
Copyright (c) 2002-2013 Mark Adler
Copyright (c) 2003-2010 Mark Adler
Copyright (c) 2004-2017 Mark Adler
Copyright 1998-2004 Gilles Vollant
Copyright (c) 1996 L. Peter Deutsch
Copyright (c) 1997,99 Borland Corp.
Copyright (c) 2003 by Cosmin Truta.
Copyright (c) 2003, 2012 Mark Adler
Copyright (c) 2004, 2010 Mark Adler
Copyright (c) 2005, 2012 Mark Adler
Copyright (c) 2009 Florian Loitsch.
Portions Copyright 2007 Google Inc.
Portions Copyright 2009 Google Inc.
Copyright (c) 2004, 2005 Mark Adler.
Copyright (c) 2007-2008 Even Rouault
Copyright (c) 2014 Intel Corporation
Copyright (c) Microsoft Corporation.
Copyright (c) 2013 Intel Corporation.
Copyright (c) 1998-2005 Gilles Vollant
Copyright (c) 2004, 2005 by Mark Adler
Copyright (c) 2011-2013, Tony Million.
Copyright (c) 1995-2003, 2010 Mark Adler
Copyright (c) 1995-2005, 2010 Mark Adler
Copyright (c) 1995-2011, 2016 Mark Adler
Copyright (c) 1995-2016 Jean-loup Gailly
Copyright (c) 1995-2017 Jean-loup Gailly
Copyright (c) 1995-1998 Jean-loup Gailly.
Copyright (c) 1995-2003 Jean-loup Gailly.
Copyright (c) 1995-2013 Jean-loup Gailly.
Copyright (c) 1997,99 Borland Corporation
Copyright (c) 1998 by Andreas R. Kleinert
Copyright (c) 2002-2003 Dmitriy Anisimkov
Copyright (c) 2002-2004 Dmitriy Anisimkov
Copyright (c) 2004, 2005, 2012 Mark Adler
Copyright (c) 2004, 2008, 2012 Mark Adler
Copyright (c) 2007, 2008, 2012 Mark Adler
(c) 1995-2013 Jean-loup Gailly & Mark Adler
Copyright (c) 1998 by Jacques Nomssi Nzali.
Copyright (c) 1998-2010 - by Gilles Vollant
Copyright (c) 1995-2003 by Jean-loup Gailly.
(c) 1995-2012 Jean-loup Gailly and Mark Adler
Copyright (c) 1995-2006, 2011 Jean-loup Gailly.
Copyright (c) 2013 Intel Corporation Jim Kukunas
Copyright (c) 2015 Free Software Foundation, Inc.
Copyright 1995-2017 Jean-loup Gailly and Mark Adler
Copyright (c) 1995-2013 Jean-loup Gailly, Mark Adler
Copyright (c) 1995-2016 Jean-loup Gailly, Mark Adler
Copyright (c) 1995-2006, 2010, 2011 Jean-loup Gailly.
Copyright (c) 1998,1999,2000 by Jacques Nomssi Nzali.
Copyright (c) 2003, 2005, 2008, 2010, 2012 Mark Adler
Copyright (c) 2003 Chris Anderson <<EMAIL>>
Copyright (c) 1995-2017 Jean-loup Gailly and Mark Adler
copyright (c) 1995-2006 Jean-loup Gailly and Mark Adler
Copyright (c) 1995-2003 Jean-loup Gailly and Mark Adler.
Copyright (c) 1996 L. Peter Deutsch and Jean-Loup Gailly
Copyright (c) 1998 Brian Raiter <<EMAIL>>
Copyright (c) 1995-2006, 2010, 2011, 2012, 2016 Mark Adler
Copyright (c) 2013-2019 Niels Lohmann <http://nlohmann.me>
Copyright (c) 2009-2010 Mathias Svensson http://result42.com
(c) 1995-2013 Jean-loup Gailly (<EMAIL>) and Mark Adler
Copyright (c) 1998, 2007 Brian Raiter <<EMAIL>>
Copyright (c) 1995-2005, 2014, 2016 Jean-loup Gailly, Mark Adler
Copyright (c) 2004, 2005, 2010, 2011, 2012, 2013, 2016 Mark Adler
Copyright Jean-loup Gailly Osma Ahvenlampi <<EMAIL>>
Copyright (c) 1997 Christian Michelsen Research AS Advanced Computing
Copyright (c) 1995-2003, 2010, 2014, 2016 Jean-loup Gailly, Mark Adler
Copyright (c) 1998 - 2010 Gilles Vollant, Even Rouault, Mathias Svensson
Copyright (c) 1995-1996 Jean-loup Gailly, Brian Raiter and Gilles Vollant.
Copyright (c) 1995-2010 Jean-loup Gailly, Brian Raiter and Gilles Vollant.
Copyright (c) 1998-2010 Gilles Vollant (minizip) http://www.winimage.com/zLibDll/minizip.html
Copyright (c) 1995-2013 Jean-loup Gailly (<EMAIL>) and Mark Adler (<EMAIL>).
Copyright (c) 2008-2009 Bjoern Hoehrmann <<EMAIL>> sa http://bjoern.hoehrmann.de/utf-8/decoder/dfa


                                 Apache License
                           Version 2.0, January 2004
                        http://www.apache.org/licenses/

   TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION

   1. Definitions.

      "License" shall mean the terms and conditions for use, reproduction,
      and distribution as defined by Sections 1 through 9 of this document.

      "Licensor" shall mean the copyright owner or entity authorized by
      the copyright owner that is granting the License.

      "Legal Entity" shall mean the union of the acting entity and all
      other entities that control, are controlled by, or are under common
      control with that entity. For the purposes of this definition,
      "control" means (i) the power, direct or indirect, to cause the
      direction or management of such entity, whether by contract or
      otherwise, or (ii) ownership of fifty percent (50%) or more of the
      outstanding shares, or (iii) beneficial ownership of such entity.

      "You" (or "Your") shall mean an individual or Legal Entity
      exercising permissions granted by this License.

      "Source" form shall mean the preferred form for making modifications,
      including but not limited to software source code, documentation
      source, and configuration files.

      "Object" form shall mean any form resulting from mechanical
      transformation or translation of a Source form, including but
      not limited to compiled object code, generated documentation,
      and conversions to other media types.

      "Work" shall mean the work of authorship, whether in Source or
      Object form, made available under the License, as indicated by a
      copyright notice that is included in or attached to the work
      (an example is provided in the Appendix below).

      "Derivative Works" shall mean any work, whether in Source or Object
      form, that is based on (or derived from) the Work and for which the
      editorial revisions, annotations, elaborations, or other modifications
      represent, as a whole, an original work of authorship. For the purposes
      of this License, Derivative Works shall not include works that remain
      separable from, or merely link (or bind by name) to the interfaces of,
      the Work and Derivative Works thereof.

      "Contribution" shall mean any work of authorship, including
      the original version of the Work and any modifications or additions
      to that Work or Derivative Works thereof, that is intentionally
      submitted to Licensor for inclusion in the Work by the copyright owner
      or by an individual or Legal Entity authorized to submit on behalf of
      the copyright owner. For the purposes of this definition, "submitted"
      means any form of electronic, verbal, or written communication sent
      to the Licensor or its representatives, including but not limited to
      communication on electronic mailing lists, source code control systems,
      and issue tracking systems that are managed by, or on behalf of, the
      Licensor for the purpose of discussing and improving the Work, but
      excluding communication that is conspicuously marked or otherwise
      designated in writing by the copyright owner as "Not a Contribution."

      "Contributor" shall mean Licensor and any individual or Legal Entity
      on behalf of whom a Contribution has been received by Licensor and
      subsequently incorporated within the Work.

   2. Grant of Copyright License. Subject to the terms and conditions of
      this License, each Contributor hereby grants to You a perpetual,
      worldwide, non-exclusive, no-charge, royalty-free, irrevocable
      copyright license to reproduce, prepare Derivative Works of,
      publicly display, publicly perform, sublicense, and distribute the
      Work and such Derivative Works in Source or Object form.

   3. Grant of Patent License. Subject to the terms and conditions of
      this License, each Contributor hereby grants to You a perpetual,
      worldwide, non-exclusive, no-charge, royalty-free, irrevocable
      (except as stated in this section) patent license to make, have made,
      use, offer to sell, sell, import, and otherwise transfer the Work,
      where such license applies only to those patent claims licensable
      by such Contributor that are necessarily infringed by their
      Contribution(s) alone or by combination of their Contribution(s)
      with the Work to which such Contribution(s) was submitted. If You
      institute patent litigation against any entity (including a
      cross-claim or counterclaim in a lawsuit) alleging that the Work
      or a Contribution incorporated within the Work constitutes direct
      or contributory patent infringement, then any patent licenses
      granted to You under this License for that Work shall terminate
      as of the date such litigation is filed.

   4. Redistribution. You may reproduce and distribute copies of the
      Work or Derivative Works thereof in any medium, with or without
      modifications, and in Source or Object form, provided that You
      meet the following conditions:

      (a) You must give any other recipients of the Work or
          Derivative Works a copy of this License; and

      (b) You must cause any modified files to carry prominent notices
          stating that You changed the files; and

      (c) You must retain, in the Source form of any Derivative Works
          that You distribute, all copyright, patent, trademark, and
          attribution notices from the Source form of the Work,
          excluding those notices that do not pertain to any part of
          the Derivative Works; and

      (d) If the Work includes a "NOTICE" text file as part of its
          distribution, then any Derivative Works that You distribute must
          include a readable copy of the attribution notices contained
          within such NOTICE file, excluding those notices that do not
          pertain to any part of the Derivative Works, in at least one
          of the following places: within a NOTICE text file distributed
          as part of the Derivative Works; within the Source form or
          documentation, if provided along with the Derivative Works; or,
          within a display generated by the Derivative Works, if and
          wherever such third-party notices normally appear. The contents
          of the NOTICE file are for informational purposes only and
          do not modify the License. You may add Your own attribution
          notices within Derivative Works that You distribute, alongside
          or as an addendum to the NOTICE text from the Work, provided
          that such additional attribution notices cannot be construed
          as modifying the License.

      You may add Your own copyright statement to Your modifications and
      may provide additional or different license terms and conditions
      for use, reproduction, or distribution of Your modifications, or
      for any such Derivative Works as a whole, provided Your use,
      reproduction, and distribution of the Work otherwise complies with
      the conditions stated in this License.

   5. Submission of Contributions. Unless You explicitly state otherwise,
      any Contribution intentionally submitted for inclusion in the Work
      by You to the Licensor shall be under the terms and conditions of
      this License, without any additional terms or conditions.
      Notwithstanding the above, nothing herein shall supersede or modify
      the terms of any separate license agreement you may have executed
      with Licensor regarding such Contributions.

   6. Trademarks. This License does not grant permission to use the trade
      names, trademarks, service marks, or product names of the Licensor,
      except as required for reasonable and customary use in describing the
      origin of the Work and reproducing the content of the NOTICE file.

   7. Disclaimer of Warranty. Unless required by applicable law or
      agreed to in writing, Licensor provides the Work (and each
      Contributor provides its Contributions) on an "AS IS" BASIS,
      WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
      implied, including, without limitation, any warranties or conditions
      of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A
      PARTICULAR PURPOSE. You are solely responsible for determining the
      appropriateness of using or redistributing the Work and assume any
      risks associated with Your exercise of permissions under this License.

   8. Limitation of Liability. In no event and under no legal theory,
      whether in tort (including negligence), contract, or otherwise,
      unless required by applicable law (such as deliberate and grossly
      negligent acts) or agreed to in writing, shall any Contributor be
      liable to You for damages, including any direct, indirect, special,
      incidental, or consequential damages of any character arising as a
      result of this License or out of the use or inability to use the
      Work (including but not limited to damages for loss of goodwill,
      work stoppage, computer failure or malfunction, or any and all
      other commercial damages or losses), even if such Contributor
      has been advised of the possibility of such damages.

   9. Accepting Warranty or Additional Liability. While redistributing
      the Work or Derivative Works thereof, You may choose to offer,
      and charge a fee for, acceptance of support, warranty, indemnity,
      or other liability obligations and/or rights consistent with this
      License. However, in accepting such obligations, You may act only
      on Your own behalf and on Your sole responsibility, not on behalf
      of any other Contributor, and only if You agree to indemnify,
      defend, and hold each Contributor harmless for any liability
      incurred by, or claims asserted against, such Contributor by reason
      of your accepting any such warranty or additional liability.

   END OF TERMS AND CONDITIONS

   APPENDIX: How to apply the Apache License to your work.

      To apply the Apache License to your work, attach the following
      boilerplate notice, with the fields enclosed by brackets "[]"
      replaced with your own identifying information. (Don't include
      the brackets!)  The text should be enclosed in the appropriate
      comment syntax for the file format. We also recommend that a
      file or class name and description of purpose be included on the
      same "printed page" as the copyright notice for easier
      identification within third-party archives.

   Copyright [yyyy] [name of copyright owner]

   Licensed under the Apache License, Version 2.0 (the "License");
   you may not use this file except in compliance with the License.
   You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.


---------------------------------------------------------

---------------------------------------------------------

nltk 3.8.1 - Apache-2.0


Copyright Michele Simionato
(c) https://iso639-3.sil.org
copyright 2023, NLTK Project
Copyright (c) 2012 NLTK Project
Copyright 2015 The NLTK Project
Copyright 2002 by Grzegorz Kondrak
Copyright (c) 2001-2007 NLTK Project
Copyright (c) 2001-2013 NLTK Project
Copyright (c) 2001-2015 NLTK Project
Copyright (c) 2001-2016 NLTK Project
Copyright (c) 2001-2023 NLTK Project
Copyright (c) 2022-2023 NLTK Project
Copyright 2013 Matthew Honnibal NLTK
Copyright 1998 Carnegie Mellon University
Copyright 2002 by Grzegorz Kondrak. ALINE
Copyright (c) 1999-2005, Marc-Andre Lemburg
Copyright (c) 2005-2007 Oregon Graduate Institute
Copyright (c) 2001-2023 NLTK Project URL <https://www.nltk.org/>
Copyright (c) 2001-2023 NLTK Project Algorithm Kiss & Strunk (2006)
Copyright (c) 2001-2023 NLTK Project Algorithm Kazem Taghva, Rania Elkhoury, and Jeffrey Coombs (2005)

Apache License

Version 2.0, January 2004

http://www.apache.org/licenses/ TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION

   1. Definitions.

      

      "License" shall mean the terms and conditions for use, reproduction, and distribution as defined by Sections 1 through 9 of this document.

      

      "Licensor" shall mean the copyright owner or entity authorized by the copyright owner that is granting the License.

      

      "Legal Entity" shall mean the union of the acting entity and all other entities that control, are controlled by, or are under common control with that entity. For the purposes of this definition, "control" means (i) the power, direct or indirect, to cause the direction or management of such entity, whether by contract or otherwise, or (ii) ownership of fifty percent (50%) or more of the outstanding shares, or (iii) beneficial ownership of such entity.

      

      "You" (or "Your") shall mean an individual or Legal Entity exercising permissions granted by this License.

      

      "Source" form shall mean the preferred form for making modifications, including but not limited to software source code, documentation source, and configuration files.

      

      "Object" form shall mean any form resulting from mechanical transformation or translation of a Source form, including but not limited to compiled object code, generated documentation, and conversions to other media types.

      

      "Work" shall mean the work of authorship, whether in Source or Object form, made available under the License, as indicated by a copyright notice that is included in or attached to the work (an example is provided in the Appendix below).

      

      "Derivative Works" shall mean any work, whether in Source or Object form, that is based on (or derived from) the Work and for which the editorial revisions, annotations, elaborations, or other modifications represent, as a whole, an original work of authorship. For the purposes of this License, Derivative Works shall not include works that remain separable from, or merely link (or bind by name) to the interfaces of, the Work and Derivative Works thereof.

      

      "Contribution" shall mean any work of authorship, including the original version of the Work and any modifications or additions to that Work or Derivative Works thereof, that is intentionally submitted to Licensor for inclusion in the Work by the copyright owner or by an individual or Legal Entity authorized to submit on behalf of the copyright owner. For the purposes of this definition, "submitted" means any form of electronic, verbal, or written communication sent to the Licensor or its representatives, including but not limited to communication on electronic mailing lists, source code control systems, and issue tracking systems that are managed by, or on behalf of, the Licensor for the purpose of discussing and improving the Work, but excluding communication that is conspicuously marked or otherwise designated in writing by the copyright owner as "Not a Contribution."

      

      "Contributor" shall mean Licensor and any individual or Legal Entity on behalf of whom a Contribution has been received by Licensor and subsequently incorporated within the Work.

   2. Grant of Copyright License. Subject to the terms and conditions of this License, each Contributor hereby grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable copyright license to reproduce, prepare Derivative Works of, publicly display, publicly perform, sublicense, and distribute the Work and such Derivative Works in Source or Object form.

   3. Grant of Patent License. Subject to the terms and conditions of this License, each Contributor hereby grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable (except as stated in this section) patent license to make, have made, use, offer to sell, sell, import, and otherwise transfer the Work, where such license applies only to those patent claims licensable by such Contributor that are necessarily infringed by their Contribution(s) alone or by combination of their Contribution(s) with the Work to which such Contribution(s) was submitted. If You institute patent litigation against any entity (including a cross-claim or counterclaim in a lawsuit) alleging that the Work or a Contribution incorporated within the Work constitutes direct or contributory patent infringement, then any patent licenses granted to You under this License for that Work shall terminate as of the date such litigation is filed.

   4. Redistribution. You may reproduce and distribute copies of the Work or Derivative Works thereof in any medium, with or without modifications, and in Source or Object form, provided that You meet the following conditions:

      (a) You must give any other recipients of the Work or Derivative Works a copy of this License; and

      (b) You must cause any modified files to carry prominent notices stating that You changed the files; and

      (c) You must retain, in the Source form of any Derivative Works that You distribute, all copyright, patent, trademark, and attribution notices from the Source form of the Work, excluding those notices that do not pertain to any part of the Derivative Works; and

      (d) If the Work includes a "NOTICE" text file as part of its distribution, then any Derivative Works that You distribute must include a readable copy of the attribution notices contained within such NOTICE file, excluding those notices that do not pertain to any part of the Derivative Works, in at least one of the following places: within a NOTICE text file distributed as part of the Derivative Works; within the Source form or documentation, if provided along with the Derivative Works; or, within a display generated by the Derivative Works, if and wherever such third-party notices normally appear. The contents of the NOTICE file are for informational purposes only and do not modify the License. You may add Your own attribution notices within Derivative Works that You distribute, alongside or as an addendum to the NOTICE text from the Work, provided that such additional attribution notices cannot be construed as modifying the License.

      You may add Your own copyright statement to Your modifications and may provide additional or different license terms and conditions for use, reproduction, or distribution of Your modifications, or for any such Derivative Works as a whole, provided Your use, reproduction, and distribution of the Work otherwise complies with the conditions stated in this License.

   5. Submission of Contributions. Unless You explicitly state otherwise, any Contribution intentionally submitted for inclusion in the Work by You to the Licensor shall be under the terms and conditions of this License, without any additional terms or conditions. Notwithstanding the above, nothing herein shall supersede or modify the terms of any separate license agreement you may have executed with Licensor regarding such Contributions.

   6. Trademarks. This License does not grant permission to use the trade names, trademarks, service marks, or product names of the Licensor, except as required for reasonable and customary use in describing the origin of the Work and reproducing the content of the NOTICE file.

   7. Disclaimer of Warranty. Unless required by applicable law or agreed to in writing, Licensor provides the Work (and each Contributor provides its Contributions) on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied, including, without limitation, any warranties or conditions of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A PARTICULAR PURPOSE. You are solely responsible for determining the appropriateness of using or redistributing the Work and assume any risks associated with Your exercise of permissions under this License.

   8. Limitation of Liability. In no event and under no legal theory, whether in tort (including negligence), contract, or otherwise, unless required by applicable law (such as deliberate and grossly negligent acts) or agreed to in writing, shall any Contributor be liable to You for damages, including any direct, indirect, special, incidental, or consequential damages of any character arising as a result of this License or out of the use or inability to use the Work (including but not limited to damages for loss of goodwill, work stoppage, computer failure or malfunction, or any and all other commercial damages or losses), even if such Contributor has been advised of the possibility of such damages.

   9. Accepting Warranty or Additional Liability. While redistributing the Work or Derivative Works thereof, You may choose to offer, and charge a fee for, acceptance of support, warranty, indemnity, or other liability obligations and/or rights consistent with this License. However, in accepting such obligations, You may act only on Your own behalf and on Your sole responsibility, not on behalf of any other Contributor, and only if You agree to indemnify, defend, and hold each Contributor harmless for any liability incurred by, or claims asserted against, such Contributor by reason of your accepting any such warranty or additional liability. END OF TERMS AND CONDITIONS

APPENDIX: How to apply the Apache License to your work.

To apply the Apache License to your work, attach the following boilerplate notice, with the fields enclosed by brackets "[]" replaced with your own identifying information. (Don't include the brackets!) The text should be enclosed in the appropriate comment syntax for the file format. We also recommend that a file or class name and description of purpose be included on the same "printed page" as the copyright notice for easier identification within third-party archives.

Copyright [yyyy] [name of copyright owner]

Licensed under the Apache License, Version 2.0 (the "License");

you may not use this file except in compliance with the License.

You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software

distributed under the License is distributed on an "AS IS" BASIS,

WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

See the License for the specific language governing permissions and

limitations under the License.

---------------------------------------------------------

---------------------------------------------------------

open-telemetry/opentelemetry-cpp da2911cf4458c7068f967c17c65d07eeba449a08 - Apache-2.0


Copyright 2017, OpenCensus
Copyright 2020 opentelemetry
Copyright 2019, OpenTelemetry
Copyright 2020, OpenTelemetry
Copyright 2021, OpenTelemetry
copyright 2021, OpenTelemetry
Copyright 2017 The Abseil Authors
Copyright 2018 The Abseil Authors
Copyright 2019 The Abseil Authors
Copyright (c) Microsoft Corporation
Copyright 2020, Open Telemetry Authors

                                 Apache License
                           Version 2.0, January 2004
                        http://www.apache.org/licenses/

   TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION

   1. Definitions.

      "License" shall mean the terms and conditions for use, reproduction,
      and distribution as defined by Sections 1 through 9 of this document.

      "Licensor" shall mean the copyright owner or entity authorized by
      the copyright owner that is granting the License.

      "Legal Entity" shall mean the union of the acting entity and all
      other entities that control, are controlled by, or are under common
      control with that entity. For the purposes of this definition,
      "control" means (i) the power, direct or indirect, to cause the
      direction or management of such entity, whether by contract or
      otherwise, or (ii) ownership of fifty percent (50%) or more of the
      outstanding shares, or (iii) beneficial ownership of such entity.

      "You" (or "Your") shall mean an individual or Legal Entity
      exercising permissions granted by this License.

      "Source" form shall mean the preferred form for making modifications,
      including but not limited to software source code, documentation
      source, and configuration files.

      "Object" form shall mean any form resulting from mechanical
      transformation or translation of a Source form, including but
      not limited to compiled object code, generated documentation,
      and conversions to other media types.

      "Work" shall mean the work of authorship, whether in Source or
      Object form, made available under the License, as indicated by a
      copyright notice that is included in or attached to the work
      (an example is provided in the Appendix below).

      "Derivative Works" shall mean any work, whether in Source or Object
      form, that is based on (or derived from) the Work and for which the
      editorial revisions, annotations, elaborations, or other modifications
      represent, as a whole, an original work of authorship. For the purposes
      of this License, Derivative Works shall not include works that remain
      separable from, or merely link (or bind by name) to the interfaces of,
      the Work and Derivative Works thereof.

      "Contribution" shall mean any work of authorship, including
      the original version of the Work and any modifications or additions
      to that Work or Derivative Works thereof, that is intentionally
      submitted to Licensor for inclusion in the Work by the copyright owner
      or by an individual or Legal Entity authorized to submit on behalf of
      the copyright owner. For the purposes of this definition, "submitted"
      means any form of electronic, verbal, or written communication sent
      to the Licensor or its representatives, including but not limited to
      communication on electronic mailing lists, source code control systems,
      and issue tracking systems that are managed by, or on behalf of, the
      Licensor for the purpose of discussing and improving the Work, but
      excluding communication that is conspicuously marked or otherwise
      designated in writing by the copyright owner as "Not a Contribution."

      "Contributor" shall mean Licensor and any individual or Legal Entity
      on behalf of whom a Contribution has been received by Licensor and
      subsequently incorporated within the Work.

   2. Grant of Copyright License. Subject to the terms and conditions of
      this License, each Contributor hereby grants to You a perpetual,
      worldwide, non-exclusive, no-charge, royalty-free, irrevocable
      copyright license to reproduce, prepare Derivative Works of,
      publicly display, publicly perform, sublicense, and distribute the
      Work and such Derivative Works in Source or Object form.

   3. Grant of Patent License. Subject to the terms and conditions of
      this License, each Contributor hereby grants to You a perpetual,
      worldwide, non-exclusive, no-charge, royalty-free, irrevocable
      (except as stated in this section) patent license to make, have made,
      use, offer to sell, sell, import, and otherwise transfer the Work,
      where such license applies only to those patent claims licensable
      by such Contributor that are necessarily infringed by their
      Contribution(s) alone or by combination of their Contribution(s)
      with the Work to which such Contribution(s) was submitted. If You
      institute patent litigation against any entity (including a
      cross-claim or counterclaim in a lawsuit) alleging that the Work
      or a Contribution incorporated within the Work constitutes direct
      or contributory patent infringement, then any patent licenses
      granted to You under this License for that Work shall terminate
      as of the date such litigation is filed.

   4. Redistribution. You may reproduce and distribute copies of the
      Work or Derivative Works thereof in any medium, with or without
      modifications, and in Source or Object form, provided that You
      meet the following conditions:

      (a) You must give any other recipients of the Work or
          Derivative Works a copy of this License; and

      (b) You must cause any modified files to carry prominent notices
          stating that You changed the files; and

      (c) You must retain, in the Source form of any Derivative Works
          that You distribute, all copyright, patent, trademark, and
          attribution notices from the Source form of the Work,
          excluding those notices that do not pertain to any part of
          the Derivative Works; and

      (d) If the Work includes a "NOTICE" text file as part of its
          distribution, then any Derivative Works that You distribute must
          include a readable copy of the attribution notices contained
          within such NOTICE file, excluding those notices that do not
          pertain to any part of the Derivative Works, in at least one
          of the following places: within a NOTICE text file distributed
          as part of the Derivative Works; within the Source form or
          documentation, if provided along with the Derivative Works; or,
          within a display generated by the Derivative Works, if and
          wherever such third-party notices normally appear. The contents
          of the NOTICE file are for informational purposes only and
          do not modify the License. You may add Your own attribution
          notices within Derivative Works that You distribute, alongside
          or as an addendum to the NOTICE text from the Work, provided
          that such additional attribution notices cannot be construed
          as modifying the License.

      You may add Your own copyright statement to Your modifications and
      may provide additional or different license terms and conditions
      for use, reproduction, or distribution of Your modifications, or
      for any such Derivative Works as a whole, provided Your use,
      reproduction, and distribution of the Work otherwise complies with
      the conditions stated in this License.

   5. Submission of Contributions. Unless You explicitly state otherwise,
      any Contribution intentionally submitted for inclusion in the Work
      by You to the Licensor shall be under the terms and conditions of
      this License, without any additional terms or conditions.
      Notwithstanding the above, nothing herein shall supersede or modify
      the terms of any separate license agreement you may have executed
      with Licensor regarding such Contributions.

   6. Trademarks. This License does not grant permission to use the trade
      names, trademarks, service marks, or product names of the Licensor,
      except as required for reasonable and customary use in describing the
      origin of the Work and reproducing the content of the NOTICE file.

   7. Disclaimer of Warranty. Unless required by applicable law or
      agreed to in writing, Licensor provides the Work (and each
      Contributor provides its Contributions) on an "AS IS" BASIS,
      WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
      implied, including, without limitation, any warranties or conditions
      of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A
      PARTICULAR PURPOSE. You are solely responsible for determining the
      appropriateness of using or redistributing the Work and assume any
      risks associated with Your exercise of permissions under this License.

   8. Limitation of Liability. In no event and under no legal theory,
      whether in tort (including negligence), contract, or otherwise,
      unless required by applicable law (such as deliberate and grossly
      negligent acts) or agreed to in writing, shall any Contributor be
      liable to You for damages, including any direct, indirect, special,
      incidental, or consequential damages of any character arising as a
      result of this License or out of the use or inability to use the
      Work (including but not limited to damages for loss of goodwill,
      work stoppage, computer failure or malfunction, or any and all
      other commercial damages or losses), even if such Contributor
      has been advised of the possibility of such damages.

   9. Accepting Warranty or Additional Liability. While redistributing
      the Work or Derivative Works thereof, You may choose to offer,
      and charge a fee for, acceptance of support, warranty, indemnity,
      or other liability obligations and/or rights consistent with this
      License. However, in accepting such obligations, You may act only
      on Your own behalf and on Your sole responsibility, not on behalf
      of any other Contributor, and only if You agree to indemnify,
      defend, and hold each Contributor harmless for any liability
      incurred by, or claims asserted against, such Contributor by reason
      of your accepting any such warranty or additional liability.

   END OF TERMS AND CONDITIONS

   APPENDIX: How to apply the Apache License to your work.

      To apply the Apache License to your work, attach the following
      boilerplate notice, with the fields enclosed by brackets "[]"
      replaced with your own identifying information. (Don't include
      the brackets!)  The text should be enclosed in the appropriate
      comment syntax for the file format. We also recommend that a
      file or class name and description of purpose be included on the
      same "printed page" as the copyright notice for easier
      identification within third-party archives.

   Copyright [yyyy] [name of copyright owner]

   Licensed under the Apache License, Version 2.0 (the "License");
   you may not use this file except in compliance with the License.
   You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.


---------------------------------------------------------

---------------------------------------------------------

org.apache.commons/commons-lang3 3.12.0 - Apache-2.0


Copyright 2001-2021 The Apache Software Foundation

Apache License

Version 2.0, January 2004

http://www.apache.org/licenses/ TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION

   1. Definitions.

      

      "License" shall mean the terms and conditions for use, reproduction, and distribution as defined by Sections 1 through 9 of this document.

      

      "Licensor" shall mean the copyright owner or entity authorized by the copyright owner that is granting the License.

      

      "Legal Entity" shall mean the union of the acting entity and all other entities that control, are controlled by, or are under common control with that entity. For the purposes of this definition, "control" means (i) the power, direct or indirect, to cause the direction or management of such entity, whether by contract or otherwise, or (ii) ownership of fifty percent (50%) or more of the outstanding shares, or (iii) beneficial ownership of such entity.

      

      "You" (or "Your") shall mean an individual or Legal Entity exercising permissions granted by this License.

      

      "Source" form shall mean the preferred form for making modifications, including but not limited to software source code, documentation source, and configuration files.

      

      "Object" form shall mean any form resulting from mechanical transformation or translation of a Source form, including but not limited to compiled object code, generated documentation, and conversions to other media types.

      

      "Work" shall mean the work of authorship, whether in Source or Object form, made available under the License, as indicated by a copyright notice that is included in or attached to the work (an example is provided in the Appendix below).

      

      "Derivative Works" shall mean any work, whether in Source or Object form, that is based on (or derived from) the Work and for which the editorial revisions, annotations, elaborations, or other modifications represent, as a whole, an original work of authorship. For the purposes of this License, Derivative Works shall not include works that remain separable from, or merely link (or bind by name) to the interfaces of, the Work and Derivative Works thereof.

      

      "Contribution" shall mean any work of authorship, including the original version of the Work and any modifications or additions to that Work or Derivative Works thereof, that is intentionally submitted to Licensor for inclusion in the Work by the copyright owner or by an individual or Legal Entity authorized to submit on behalf of the copyright owner. For the purposes of this definition, "submitted" means any form of electronic, verbal, or written communication sent to the Licensor or its representatives, including but not limited to communication on electronic mailing lists, source code control systems, and issue tracking systems that are managed by, or on behalf of, the Licensor for the purpose of discussing and improving the Work, but excluding communication that is conspicuously marked or otherwise designated in writing by the copyright owner as "Not a Contribution."

      

      "Contributor" shall mean Licensor and any individual or Legal Entity on behalf of whom a Contribution has been received by Licensor and subsequently incorporated within the Work.

   2. Grant of Copyright License. Subject to the terms and conditions of this License, each Contributor hereby grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable copyright license to reproduce, prepare Derivative Works of, publicly display, publicly perform, sublicense, and distribute the Work and such Derivative Works in Source or Object form.

   3. Grant of Patent License. Subject to the terms and conditions of this License, each Contributor hereby grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable (except as stated in this section) patent license to make, have made, use, offer to sell, sell, import, and otherwise transfer the Work, where such license applies only to those patent claims licensable by such Contributor that are necessarily infringed by their Contribution(s) alone or by combination of their Contribution(s) with the Work to which such Contribution(s) was submitted. If You institute patent litigation against any entity (including a cross-claim or counterclaim in a lawsuit) alleging that the Work or a Contribution incorporated within the Work constitutes direct or contributory patent infringement, then any patent licenses granted to You under this License for that Work shall terminate as of the date such litigation is filed.

   4. Redistribution. You may reproduce and distribute copies of the Work or Derivative Works thereof in any medium, with or without modifications, and in Source or Object form, provided that You meet the following conditions:

      (a) You must give any other recipients of the Work or Derivative Works a copy of this License; and

      (b) You must cause any modified files to carry prominent notices stating that You changed the files; and

      (c) You must retain, in the Source form of any Derivative Works that You distribute, all copyright, patent, trademark, and attribution notices from the Source form of the Work, excluding those notices that do not pertain to any part of the Derivative Works; and

      (d) If the Work includes a "NOTICE" text file as part of its distribution, then any Derivative Works that You distribute must include a readable copy of the attribution notices contained within such NOTICE file, excluding those notices that do not pertain to any part of the Derivative Works, in at least one of the following places: within a NOTICE text file distributed as part of the Derivative Works; within the Source form or documentation, if provided along with the Derivative Works; or, within a display generated by the Derivative Works, if and wherever such third-party notices normally appear. The contents of the NOTICE file are for informational purposes only and do not modify the License. You may add Your own attribution notices within Derivative Works that You distribute, alongside or as an addendum to the NOTICE text from the Work, provided that such additional attribution notices cannot be construed as modifying the License.

      You may add Your own copyright statement to Your modifications and may provide additional or different license terms and conditions for use, reproduction, or distribution of Your modifications, or for any such Derivative Works as a whole, provided Your use, reproduction, and distribution of the Work otherwise complies with the conditions stated in this License.

   5. Submission of Contributions. Unless You explicitly state otherwise, any Contribution intentionally submitted for inclusion in the Work by You to the Licensor shall be under the terms and conditions of this License, without any additional terms or conditions. Notwithstanding the above, nothing herein shall supersede or modify the terms of any separate license agreement you may have executed with Licensor regarding such Contributions.

   6. Trademarks. This License does not grant permission to use the trade names, trademarks, service marks, or product names of the Licensor, except as required for reasonable and customary use in describing the origin of the Work and reproducing the content of the NOTICE file.

   7. Disclaimer of Warranty. Unless required by applicable law or agreed to in writing, Licensor provides the Work (and each Contributor provides its Contributions) on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied, including, without limitation, any warranties or conditions of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A PARTICULAR PURPOSE. You are solely responsible for determining the appropriateness of using or redistributing the Work and assume any risks associated with Your exercise of permissions under this License.

   8. Limitation of Liability. In no event and under no legal theory, whether in tort (including negligence), contract, or otherwise, unless required by applicable law (such as deliberate and grossly negligent acts) or agreed to in writing, shall any Contributor be liable to You for damages, including any direct, indirect, special, incidental, or consequential damages of any character arising as a result of this License or out of the use or inability to use the Work (including but not limited to damages for loss of goodwill, work stoppage, computer failure or malfunction, or any and all other commercial damages or losses), even if such Contributor has been advised of the possibility of such damages.

   9. Accepting Warranty or Additional Liability. While redistributing the Work or Derivative Works thereof, You may choose to offer, and charge a fee for, acceptance of support, warranty, indemnity, or other liability obligations and/or rights consistent with this License. However, in accepting such obligations, You may act only on Your own behalf and on Your sole responsibility, not on behalf of any other Contributor, and only if You agree to indemnify, defend, and hold each Contributor harmless for any liability incurred by, or claims asserted against, such Contributor by reason of your accepting any such warranty or additional liability. END OF TERMS AND CONDITIONS

APPENDIX: How to apply the Apache License to your work.

To apply the Apache License to your work, attach the following boilerplate notice, with the fields enclosed by brackets "[]" replaced with your own identifying information. (Don't include the brackets!) The text should be enclosed in the appropriate comment syntax for the file format. We also recommend that a file or class name and description of purpose be included on the same "printed page" as the copyright notice for easier identification within third-party archives.

Copyright [yyyy] [name of copyright owner]

Licensed under the Apache License, Version 2.0 (the "License");

you may not use this file except in compliance with the License.

You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software

distributed under the License is distributed on an "AS IS" BASIS,

WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

See the License for the specific language governing permissions and

limitations under the License.

---------------------------------------------------------

---------------------------------------------------------

org.apache.commons/commons-pool2 2.11.1 - Apache-2.0


Copyright 2001-2021 The Apache Software Foundation

Apache License

Version 2.0, January 2004

http://www.apache.org/licenses/ TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION

   1. Definitions.

      

      "License" shall mean the terms and conditions for use, reproduction, and distribution as defined by Sections 1 through 9 of this document.

      

      "Licensor" shall mean the copyright owner or entity authorized by the copyright owner that is granting the License.

      

      "Legal Entity" shall mean the union of the acting entity and all other entities that control, are controlled by, or are under common control with that entity. For the purposes of this definition, "control" means (i) the power, direct or indirect, to cause the direction or management of such entity, whether by contract or otherwise, or (ii) ownership of fifty percent (50%) or more of the outstanding shares, or (iii) beneficial ownership of such entity.

      

      "You" (or "Your") shall mean an individual or Legal Entity exercising permissions granted by this License.

      

      "Source" form shall mean the preferred form for making modifications, including but not limited to software source code, documentation source, and configuration files.

      

      "Object" form shall mean any form resulting from mechanical transformation or translation of a Source form, including but not limited to compiled object code, generated documentation, and conversions to other media types.

      

      "Work" shall mean the work of authorship, whether in Source or Object form, made available under the License, as indicated by a copyright notice that is included in or attached to the work (an example is provided in the Appendix below).

      

      "Derivative Works" shall mean any work, whether in Source or Object form, that is based on (or derived from) the Work and for which the editorial revisions, annotations, elaborations, or other modifications represent, as a whole, an original work of authorship. For the purposes of this License, Derivative Works shall not include works that remain separable from, or merely link (or bind by name) to the interfaces of, the Work and Derivative Works thereof.

      

      "Contribution" shall mean any work of authorship, including the original version of the Work and any modifications or additions to that Work or Derivative Works thereof, that is intentionally submitted to Licensor for inclusion in the Work by the copyright owner or by an individual or Legal Entity authorized to submit on behalf of the copyright owner. For the purposes of this definition, "submitted" means any form of electronic, verbal, or written communication sent to the Licensor or its representatives, including but not limited to communication on electronic mailing lists, source code control systems, and issue tracking systems that are managed by, or on behalf of, the Licensor for the purpose of discussing and improving the Work, but excluding communication that is conspicuously marked or otherwise designated in writing by the copyright owner as "Not a Contribution."

      

      "Contributor" shall mean Licensor and any individual or Legal Entity on behalf of whom a Contribution has been received by Licensor and subsequently incorporated within the Work.

   2. Grant of Copyright License. Subject to the terms and conditions of this License, each Contributor hereby grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable copyright license to reproduce, prepare Derivative Works of, publicly display, publicly perform, sublicense, and distribute the Work and such Derivative Works in Source or Object form.

   3. Grant of Patent License. Subject to the terms and conditions of this License, each Contributor hereby grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable (except as stated in this section) patent license to make, have made, use, offer to sell, sell, import, and otherwise transfer the Work, where such license applies only to those patent claims licensable by such Contributor that are necessarily infringed by their Contribution(s) alone or by combination of their Contribution(s) with the Work to which such Contribution(s) was submitted. If You institute patent litigation against any entity (including a cross-claim or counterclaim in a lawsuit) alleging that the Work or a Contribution incorporated within the Work constitutes direct or contributory patent infringement, then any patent licenses granted to You under this License for that Work shall terminate as of the date such litigation is filed.

   4. Redistribution. You may reproduce and distribute copies of the Work or Derivative Works thereof in any medium, with or without modifications, and in Source or Object form, provided that You meet the following conditions:

      (a) You must give any other recipients of the Work or Derivative Works a copy of this License; and

      (b) You must cause any modified files to carry prominent notices stating that You changed the files; and

      (c) You must retain, in the Source form of any Derivative Works that You distribute, all copyright, patent, trademark, and attribution notices from the Source form of the Work, excluding those notices that do not pertain to any part of the Derivative Works; and

      (d) If the Work includes a "NOTICE" text file as part of its distribution, then any Derivative Works that You distribute must include a readable copy of the attribution notices contained within such NOTICE file, excluding those notices that do not pertain to any part of the Derivative Works, in at least one of the following places: within a NOTICE text file distributed as part of the Derivative Works; within the Source form or documentation, if provided along with the Derivative Works; or, within a display generated by the Derivative Works, if and wherever such third-party notices normally appear. The contents of the NOTICE file are for informational purposes only and do not modify the License. You may add Your own attribution notices within Derivative Works that You distribute, alongside or as an addendum to the NOTICE text from the Work, provided that such additional attribution notices cannot be construed as modifying the License.

      You may add Your own copyright statement to Your modifications and may provide additional or different license terms and conditions for use, reproduction, or distribution of Your modifications, or for any such Derivative Works as a whole, provided Your use, reproduction, and distribution of the Work otherwise complies with the conditions stated in this License.

   5. Submission of Contributions. Unless You explicitly state otherwise, any Contribution intentionally submitted for inclusion in the Work by You to the Licensor shall be under the terms and conditions of this License, without any additional terms or conditions. Notwithstanding the above, nothing herein shall supersede or modify the terms of any separate license agreement you may have executed with Licensor regarding such Contributions.

   6. Trademarks. This License does not grant permission to use the trade names, trademarks, service marks, or product names of the Licensor, except as required for reasonable and customary use in describing the origin of the Work and reproducing the content of the NOTICE file.

   7. Disclaimer of Warranty. Unless required by applicable law or agreed to in writing, Licensor provides the Work (and each Contributor provides its Contributions) on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied, including, without limitation, any warranties or conditions of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A PARTICULAR PURPOSE. You are solely responsible for determining the appropriateness of using or redistributing the Work and assume any risks associated with Your exercise of permissions under this License.

   8. Limitation of Liability. In no event and under no legal theory, whether in tort (including negligence), contract, or otherwise, unless required by applicable law (such as deliberate and grossly negligent acts) or agreed to in writing, shall any Contributor be liable to You for damages, including any direct, indirect, special, incidental, or consequential damages of any character arising as a result of this License or out of the use or inability to use the Work (including but not limited to damages for loss of goodwill, work stoppage, computer failure or malfunction, or any and all other commercial damages or losses), even if such Contributor has been advised of the possibility of such damages.

   9. Accepting Warranty or Additional Liability. While redistributing the Work or Derivative Works thereof, You may choose to offer, and charge a fee for, acceptance of support, warranty, indemnity, or other liability obligations and/or rights consistent with this License. However, in accepting such obligations, You may act only on Your own behalf and on Your sole responsibility, not on behalf of any other Contributor, and only if You agree to indemnify, defend, and hold each Contributor harmless for any liability incurred by, or claims asserted against, such Contributor by reason of your accepting any such warranty or additional liability. END OF TERMS AND CONDITIONS

APPENDIX: How to apply the Apache License to your work.

To apply the Apache License to your work, attach the following boilerplate notice, with the fields enclosed by brackets "[]" replaced with your own identifying information. (Don't include the brackets!) The text should be enclosed in the appropriate comment syntax for the file format. We also recommend that a file or class name and description of purpose be included on the same "printed page" as the copyright notice for easier identification within third-party archives.

Copyright [yyyy] [name of copyright owner]

Licensed under the Apache License, Version 2.0 (the "License");

you may not use this file except in compliance with the License.

You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software

distributed under the License is distributed on an "AS IS" BASIS,

WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

See the License for the specific language governing permissions and

limitations under the License.

---------------------------------------------------------

---------------------------------------------------------

org.apache.commons/commons-text 1.10.0 - Apache-2.0


Copyright 2014-2022 The Apache Software Foundation

Apache License

Version 2.0, January 2004

http://www.apache.org/licenses/ TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION

   1. Definitions.

      

      "License" shall mean the terms and conditions for use, reproduction, and distribution as defined by Sections 1 through 9 of this document.

      

      "Licensor" shall mean the copyright owner or entity authorized by the copyright owner that is granting the License.

      

      "Legal Entity" shall mean the union of the acting entity and all other entities that control, are controlled by, or are under common control with that entity. For the purposes of this definition, "control" means (i) the power, direct or indirect, to cause the direction or management of such entity, whether by contract or otherwise, or (ii) ownership of fifty percent (50%) or more of the outstanding shares, or (iii) beneficial ownership of such entity.

      

      "You" (or "Your") shall mean an individual or Legal Entity exercising permissions granted by this License.

      

      "Source" form shall mean the preferred form for making modifications, including but not limited to software source code, documentation source, and configuration files.

      

      "Object" form shall mean any form resulting from mechanical transformation or translation of a Source form, including but not limited to compiled object code, generated documentation, and conversions to other media types.

      

      "Work" shall mean the work of authorship, whether in Source or Object form, made available under the License, as indicated by a copyright notice that is included in or attached to the work (an example is provided in the Appendix below).

      

      "Derivative Works" shall mean any work, whether in Source or Object form, that is based on (or derived from) the Work and for which the editorial revisions, annotations, elaborations, or other modifications represent, as a whole, an original work of authorship. For the purposes of this License, Derivative Works shall not include works that remain separable from, or merely link (or bind by name) to the interfaces of, the Work and Derivative Works thereof.

      

      "Contribution" shall mean any work of authorship, including the original version of the Work and any modifications or additions to that Work or Derivative Works thereof, that is intentionally submitted to Licensor for inclusion in the Work by the copyright owner or by an individual or Legal Entity authorized to submit on behalf of the copyright owner. For the purposes of this definition, "submitted" means any form of electronic, verbal, or written communication sent to the Licensor or its representatives, including but not limited to communication on electronic mailing lists, source code control systems, and issue tracking systems that are managed by, or on behalf of, the Licensor for the purpose of discussing and improving the Work, but excluding communication that is conspicuously marked or otherwise designated in writing by the copyright owner as "Not a Contribution."

      

      "Contributor" shall mean Licensor and any individual or Legal Entity on behalf of whom a Contribution has been received by Licensor and subsequently incorporated within the Work.

   2. Grant of Copyright License. Subject to the terms and conditions of this License, each Contributor hereby grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable copyright license to reproduce, prepare Derivative Works of, publicly display, publicly perform, sublicense, and distribute the Work and such Derivative Works in Source or Object form.

   3. Grant of Patent License. Subject to the terms and conditions of this License, each Contributor hereby grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable (except as stated in this section) patent license to make, have made, use, offer to sell, sell, import, and otherwise transfer the Work, where such license applies only to those patent claims licensable by such Contributor that are necessarily infringed by their Contribution(s) alone or by combination of their Contribution(s) with the Work to which such Contribution(s) was submitted. If You institute patent litigation against any entity (including a cross-claim or counterclaim in a lawsuit) alleging that the Work or a Contribution incorporated within the Work constitutes direct or contributory patent infringement, then any patent licenses granted to You under this License for that Work shall terminate as of the date such litigation is filed.

   4. Redistribution. You may reproduce and distribute copies of the Work or Derivative Works thereof in any medium, with or without modifications, and in Source or Object form, provided that You meet the following conditions:

      (a) You must give any other recipients of the Work or Derivative Works a copy of this License; and

      (b) You must cause any modified files to carry prominent notices stating that You changed the files; and

      (c) You must retain, in the Source form of any Derivative Works that You distribute, all copyright, patent, trademark, and attribution notices from the Source form of the Work, excluding those notices that do not pertain to any part of the Derivative Works; and

      (d) If the Work includes a "NOTICE" text file as part of its distribution, then any Derivative Works that You distribute must include a readable copy of the attribution notices contained within such NOTICE file, excluding those notices that do not pertain to any part of the Derivative Works, in at least one of the following places: within a NOTICE text file distributed as part of the Derivative Works; within the Source form or documentation, if provided along with the Derivative Works; or, within a display generated by the Derivative Works, if and wherever such third-party notices normally appear. The contents of the NOTICE file are for informational purposes only and do not modify the License. You may add Your own attribution notices within Derivative Works that You distribute, alongside or as an addendum to the NOTICE text from the Work, provided that such additional attribution notices cannot be construed as modifying the License.

      You may add Your own copyright statement to Your modifications and may provide additional or different license terms and conditions for use, reproduction, or distribution of Your modifications, or for any such Derivative Works as a whole, provided Your use, reproduction, and distribution of the Work otherwise complies with the conditions stated in this License.

   5. Submission of Contributions. Unless You explicitly state otherwise, any Contribution intentionally submitted for inclusion in the Work by You to the Licensor shall be under the terms and conditions of this License, without any additional terms or conditions. Notwithstanding the above, nothing herein shall supersede or modify the terms of any separate license agreement you may have executed with Licensor regarding such Contributions.

   6. Trademarks. This License does not grant permission to use the trade names, trademarks, service marks, or product names of the Licensor, except as required for reasonable and customary use in describing the origin of the Work and reproducing the content of the NOTICE file.

   7. Disclaimer of Warranty. Unless required by applicable law or agreed to in writing, Licensor provides the Work (and each Contributor provides its Contributions) on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied, including, without limitation, any warranties or conditions of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A PARTICULAR PURPOSE. You are solely responsible for determining the appropriateness of using or redistributing the Work and assume any risks associated with Your exercise of permissions under this License.

   8. Limitation of Liability. In no event and under no legal theory, whether in tort (including negligence), contract, or otherwise, unless required by applicable law (such as deliberate and grossly negligent acts) or agreed to in writing, shall any Contributor be liable to You for damages, including any direct, indirect, special, incidental, or consequential damages of any character arising as a result of this License or out of the use or inability to use the Work (including but not limited to damages for loss of goodwill, work stoppage, computer failure or malfunction, or any and all other commercial damages or losses), even if such Contributor has been advised of the possibility of such damages.

   9. Accepting Warranty or Additional Liability. While redistributing the Work or Derivative Works thereof, You may choose to offer, and charge a fee for, acceptance of support, warranty, indemnity, or other liability obligations and/or rights consistent with this License. However, in accepting such obligations, You may act only on Your own behalf and on Your sole responsibility, not on behalf of any other Contributor, and only if You agree to indemnify, defend, and hold each Contributor harmless for any liability incurred by, or claims asserted against, such Contributor by reason of your accepting any such warranty or additional liability. END OF TERMS AND CONDITIONS

APPENDIX: How to apply the Apache License to your work.

To apply the Apache License to your work, attach the following boilerplate notice, with the fields enclosed by brackets "[]" replaced with your own identifying information. (Don't include the brackets!) The text should be enclosed in the appropriate comment syntax for the file format. We also recommend that a file or class name and description of purpose be included on the same "printed page" as the copyright notice for easier identification within third-party archives.

Copyright [yyyy] [name of copyright owner]

Licensed under the Apache License, Version 2.0 (the "License");

you may not use this file except in compliance with the License.

You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software

distributed under the License is distributed on an "AS IS" BASIS,

WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

See the License for the specific language governing permissions and

limitations under the License.

---------------------------------------------------------

---------------------------------------------------------

regex 2022.10.31 - Apache-2.0


Copyright 2020 Matthew Barnett
Copyright (c) 1997-2002 by Secret Labs AB
copyright (c) 1998-2001 by Secret Labs AB
Copyright (c) 1997-2001 by Secret Labs AB.
Copyright (c) 1998-2001 by Secret Labs AB.

Apache License

Version 2.0, January 2004

http://www.apache.org/licenses/ TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION

   1. Definitions.

      

      "License" shall mean the terms and conditions for use, reproduction, and distribution as defined by Sections 1 through 9 of this document.

      

      "Licensor" shall mean the copyright owner or entity authorized by the copyright owner that is granting the License.

      

      "Legal Entity" shall mean the union of the acting entity and all other entities that control, are controlled by, or are under common control with that entity. For the purposes of this definition, "control" means (i) the power, direct or indirect, to cause the direction or management of such entity, whether by contract or otherwise, or (ii) ownership of fifty percent (50%) or more of the outstanding shares, or (iii) beneficial ownership of such entity.

      

      "You" (or "Your") shall mean an individual or Legal Entity exercising permissions granted by this License.

      

      "Source" form shall mean the preferred form for making modifications, including but not limited to software source code, documentation source, and configuration files.

      

      "Object" form shall mean any form resulting from mechanical transformation or translation of a Source form, including but not limited to compiled object code, generated documentation, and conversions to other media types.

      

      "Work" shall mean the work of authorship, whether in Source or Object form, made available under the License, as indicated by a copyright notice that is included in or attached to the work (an example is provided in the Appendix below).

      

      "Derivative Works" shall mean any work, whether in Source or Object form, that is based on (or derived from) the Work and for which the editorial revisions, annotations, elaborations, or other modifications represent, as a whole, an original work of authorship. For the purposes of this License, Derivative Works shall not include works that remain separable from, or merely link (or bind by name) to the interfaces of, the Work and Derivative Works thereof.

      

      "Contribution" shall mean any work of authorship, including the original version of the Work and any modifications or additions to that Work or Derivative Works thereof, that is intentionally submitted to Licensor for inclusion in the Work by the copyright owner or by an individual or Legal Entity authorized to submit on behalf of the copyright owner. For the purposes of this definition, "submitted" means any form of electronic, verbal, or written communication sent to the Licensor or its representatives, including but not limited to communication on electronic mailing lists, source code control systems, and issue tracking systems that are managed by, or on behalf of, the Licensor for the purpose of discussing and improving the Work, but excluding communication that is conspicuously marked or otherwise designated in writing by the copyright owner as "Not a Contribution."

      

      "Contributor" shall mean Licensor and any individual or Legal Entity on behalf of whom a Contribution has been received by Licensor and subsequently incorporated within the Work.

   2. Grant of Copyright License. Subject to the terms and conditions of this License, each Contributor hereby grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable copyright license to reproduce, prepare Derivative Works of, publicly display, publicly perform, sublicense, and distribute the Work and such Derivative Works in Source or Object form.

   3. Grant of Patent License. Subject to the terms and conditions of this License, each Contributor hereby grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable (except as stated in this section) patent license to make, have made, use, offer to sell, sell, import, and otherwise transfer the Work, where such license applies only to those patent claims licensable by such Contributor that are necessarily infringed by their Contribution(s) alone or by combination of their Contribution(s) with the Work to which such Contribution(s) was submitted. If You institute patent litigation against any entity (including a cross-claim or counterclaim in a lawsuit) alleging that the Work or a Contribution incorporated within the Work constitutes direct or contributory patent infringement, then any patent licenses granted to You under this License for that Work shall terminate as of the date such litigation is filed.

   4. Redistribution. You may reproduce and distribute copies of the Work or Derivative Works thereof in any medium, with or without modifications, and in Source or Object form, provided that You meet the following conditions:

      (a) You must give any other recipients of the Work or Derivative Works a copy of this License; and

      (b) You must cause any modified files to carry prominent notices stating that You changed the files; and

      (c) You must retain, in the Source form of any Derivative Works that You distribute, all copyright, patent, trademark, and attribution notices from the Source form of the Work, excluding those notices that do not pertain to any part of the Derivative Works; and

      (d) If the Work includes a "NOTICE" text file as part of its distribution, then any Derivative Works that You distribute must include a readable copy of the attribution notices contained within such NOTICE file, excluding those notices that do not pertain to any part of the Derivative Works, in at least one of the following places: within a NOTICE text file distributed as part of the Derivative Works; within the Source form or documentation, if provided along with the Derivative Works; or, within a display generated by the Derivative Works, if and wherever such third-party notices normally appear. The contents of the NOTICE file are for informational purposes only and do not modify the License. You may add Your own attribution notices within Derivative Works that You distribute, alongside or as an addendum to the NOTICE text from the Work, provided that such additional attribution notices cannot be construed as modifying the License.

      You may add Your own copyright statement to Your modifications and may provide additional or different license terms and conditions for use, reproduction, or distribution of Your modifications, or for any such Derivative Works as a whole, provided Your use, reproduction, and distribution of the Work otherwise complies with the conditions stated in this License.

   5. Submission of Contributions. Unless You explicitly state otherwise, any Contribution intentionally submitted for inclusion in the Work by You to the Licensor shall be under the terms and conditions of this License, without any additional terms or conditions. Notwithstanding the above, nothing herein shall supersede or modify the terms of any separate license agreement you may have executed with Licensor regarding such Contributions.

   6. Trademarks. This License does not grant permission to use the trade names, trademarks, service marks, or product names of the Licensor, except as required for reasonable and customary use in describing the origin of the Work and reproducing the content of the NOTICE file.

   7. Disclaimer of Warranty. Unless required by applicable law or agreed to in writing, Licensor provides the Work (and each Contributor provides its Contributions) on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied, including, without limitation, any warranties or conditions of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A PARTICULAR PURPOSE. You are solely responsible for determining the appropriateness of using or redistributing the Work and assume any risks associated with Your exercise of permissions under this License.

   8. Limitation of Liability. In no event and under no legal theory, whether in tort (including negligence), contract, or otherwise, unless required by applicable law (such as deliberate and grossly negligent acts) or agreed to in writing, shall any Contributor be liable to You for damages, including any direct, indirect, special, incidental, or consequential damages of any character arising as a result of this License or out of the use or inability to use the Work (including but not limited to damages for loss of goodwill, work stoppage, computer failure or malfunction, or any and all other commercial damages or losses), even if such Contributor has been advised of the possibility of such damages.

   9. Accepting Warranty or Additional Liability. While redistributing the Work or Derivative Works thereof, You may choose to offer, and charge a fee for, acceptance of support, warranty, indemnity, or other liability obligations and/or rights consistent with this License. However, in accepting such obligations, You may act only on Your own behalf and on Your sole responsibility, not on behalf of any other Contributor, and only if You agree to indemnify, defend, and hold each Contributor harmless for any liability incurred by, or claims asserted against, such Contributor by reason of your accepting any such warranty or additional liability. END OF TERMS AND CONDITIONS

APPENDIX: How to apply the Apache License to your work.

To apply the Apache License to your work, attach the following boilerplate notice, with the fields enclosed by brackets "[]" replaced with your own identifying information. (Don't include the brackets!) The text should be enclosed in the appropriate comment syntax for the file format. We also recommend that a file or class name and description of purpose be included on the same "printed page" as the copyright notice for easier identification within third-party archives.

Copyright [yyyy] [name of copyright owner]

Licensed under the Apache License, Version 2.0 (the "License");

you may not use this file except in compliance with the License.

You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software

distributed under the License is distributed on an "AS IS" BASIS,

WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

See the License for the specific language governing permissions and

limitations under the License.

---------------------------------------------------------

---------------------------------------------------------

waywardgeek/sonic 60eeb064f62a106069d2ce148fabd724f9df9780 - Apache-2.0


Copyright 2010 Bill Cox
NxRoN ue uI uIii (c) A2
Copyright (c) 2010 Bill Cox
Copyright 2010, 2011 Bill Cox
Copyright 2010, 2011, Bill Cox
Copyright 2010, Bill Cox, Apache
Copyright (c) 2010 Bill Cox <<EMAIL>>

Apache License

Version 2.0, January 2004

http://www.apache.org/licenses/ TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION

   1. Definitions.

      

      "License" shall mean the terms and conditions for use, reproduction, and distribution as defined by Sections 1 through 9 of this document.

      

      "Licensor" shall mean the copyright owner or entity authorized by the copyright owner that is granting the License.

      

      "Legal Entity" shall mean the union of the acting entity and all other entities that control, are controlled by, or are under common control with that entity. For the purposes of this definition, "control" means (i) the power, direct or indirect, to cause the direction or management of such entity, whether by contract or otherwise, or (ii) ownership of fifty percent (50%) or more of the outstanding shares, or (iii) beneficial ownership of such entity.

      

      "You" (or "Your") shall mean an individual or Legal Entity exercising permissions granted by this License.

      

      "Source" form shall mean the preferred form for making modifications, including but not limited to software source code, documentation source, and configuration files.

      

      "Object" form shall mean any form resulting from mechanical transformation or translation of a Source form, including but not limited to compiled object code, generated documentation, and conversions to other media types.

      

      "Work" shall mean the work of authorship, whether in Source or Object form, made available under the License, as indicated by a copyright notice that is included in or attached to the work (an example is provided in the Appendix below).

      

      "Derivative Works" shall mean any work, whether in Source or Object form, that is based on (or derived from) the Work and for which the editorial revisions, annotations, elaborations, or other modifications represent, as a whole, an original work of authorship. For the purposes of this License, Derivative Works shall not include works that remain separable from, or merely link (or bind by name) to the interfaces of, the Work and Derivative Works thereof.

      

      "Contribution" shall mean any work of authorship, including the original version of the Work and any modifications or additions to that Work or Derivative Works thereof, that is intentionally submitted to Licensor for inclusion in the Work by the copyright owner or by an individual or Legal Entity authorized to submit on behalf of the copyright owner. For the purposes of this definition, "submitted" means any form of electronic, verbal, or written communication sent to the Licensor or its representatives, including but not limited to communication on electronic mailing lists, source code control systems, and issue tracking systems that are managed by, or on behalf of, the Licensor for the purpose of discussing and improving the Work, but excluding communication that is conspicuously marked or otherwise designated in writing by the copyright owner as "Not a Contribution."

      

      "Contributor" shall mean Licensor and any individual or Legal Entity on behalf of whom a Contribution has been received by Licensor and subsequently incorporated within the Work.

   2. Grant of Copyright License. Subject to the terms and conditions of this License, each Contributor hereby grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable copyright license to reproduce, prepare Derivative Works of, publicly display, publicly perform, sublicense, and distribute the Work and such Derivative Works in Source or Object form.

   3. Grant of Patent License. Subject to the terms and conditions of this License, each Contributor hereby grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable (except as stated in this section) patent license to make, have made, use, offer to sell, sell, import, and otherwise transfer the Work, where such license applies only to those patent claims licensable by such Contributor that are necessarily infringed by their Contribution(s) alone or by combination of their Contribution(s) with the Work to which such Contribution(s) was submitted. If You institute patent litigation against any entity (including a cross-claim or counterclaim in a lawsuit) alleging that the Work or a Contribution incorporated within the Work constitutes direct or contributory patent infringement, then any patent licenses granted to You under this License for that Work shall terminate as of the date such litigation is filed.

   4. Redistribution. You may reproduce and distribute copies of the Work or Derivative Works thereof in any medium, with or without modifications, and in Source or Object form, provided that You meet the following conditions:

      (a) You must give any other recipients of the Work or Derivative Works a copy of this License; and

      (b) You must cause any modified files to carry prominent notices stating that You changed the files; and

      (c) You must retain, in the Source form of any Derivative Works that You distribute, all copyright, patent, trademark, and attribution notices from the Source form of the Work, excluding those notices that do not pertain to any part of the Derivative Works; and

      (d) If the Work includes a "NOTICE" text file as part of its distribution, then any Derivative Works that You distribute must include a readable copy of the attribution notices contained within such NOTICE file, excluding those notices that do not pertain to any part of the Derivative Works, in at least one of the following places: within a NOTICE text file distributed as part of the Derivative Works; within the Source form or documentation, if provided along with the Derivative Works; or, within a display generated by the Derivative Works, if and wherever such third-party notices normally appear. The contents of the NOTICE file are for informational purposes only and do not modify the License. You may add Your own attribution notices within Derivative Works that You distribute, alongside or as an addendum to the NOTICE text from the Work, provided that such additional attribution notices cannot be construed as modifying the License.

      You may add Your own copyright statement to Your modifications and may provide additional or different license terms and conditions for use, reproduction, or distribution of Your modifications, or for any such Derivative Works as a whole, provided Your use, reproduction, and distribution of the Work otherwise complies with the conditions stated in this License.

   5. Submission of Contributions. Unless You explicitly state otherwise, any Contribution intentionally submitted for inclusion in the Work by You to the Licensor shall be under the terms and conditions of this License, without any additional terms or conditions. Notwithstanding the above, nothing herein shall supersede or modify the terms of any separate license agreement you may have executed with Licensor regarding such Contributions.

   6. Trademarks. This License does not grant permission to use the trade names, trademarks, service marks, or product names of the Licensor, except as required for reasonable and customary use in describing the origin of the Work and reproducing the content of the NOTICE file.

   7. Disclaimer of Warranty. Unless required by applicable law or agreed to in writing, Licensor provides the Work (and each Contributor provides its Contributions) on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied, including, without limitation, any warranties or conditions of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A PARTICULAR PURPOSE. You are solely responsible for determining the appropriateness of using or redistributing the Work and assume any risks associated with Your exercise of permissions under this License.

   8. Limitation of Liability. In no event and under no legal theory, whether in tort (including negligence), contract, or otherwise, unless required by applicable law (such as deliberate and grossly negligent acts) or agreed to in writing, shall any Contributor be liable to You for damages, including any direct, indirect, special, incidental, or consequential damages of any character arising as a result of this License or out of the use or inability to use the Work (including but not limited to damages for loss of goodwill, work stoppage, computer failure or malfunction, or any and all other commercial damages or losses), even if such Contributor has been advised of the possibility of such damages.

   9. Accepting Warranty or Additional Liability. While redistributing the Work or Derivative Works thereof, You may choose to offer, and charge a fee for, acceptance of support, warranty, indemnity, or other liability obligations and/or rights consistent with this License. However, in accepting such obligations, You may act only on Your own behalf and on Your sole responsibility, not on behalf of any other Contributor, and only if You agree to indemnify, defend, and hold each Contributor harmless for any liability incurred by, or claims asserted against, such Contributor by reason of your accepting any such warranty or additional liability. END OF TERMS AND CONDITIONS

APPENDIX: How to apply the Apache License to your work.

To apply the Apache License to your work, attach the following boilerplate notice, with the fields enclosed by brackets "[]" replaced with your own identifying information. (Don't include the brackets!) The text should be enclosed in the appropriate comment syntax for the file format. We also recommend that a file or class name and description of purpose be included on the same "printed page" as the copyright notice for easier identification within third-party archives.

Copyright [yyyy] [name of copyright owner]

Licensed under the Apache License, Version 2.0 (the "License");

you may not use this file except in compliance with the License.

You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software

distributed under the License is distributed on an "AS IS" BASIS,

WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

See the License for the specific language governing permissions and

limitations under the License.

---------------------------------------------------------

---------------------------------------------------------

click 8.1.3 - BSD-2-Clause AND BSD-3-Clause


Copyright 2014 Pallets
copyright 2014 Pallets
Copyright 2001-2006 Gregory P. Ward
Copyright 2002-2006 Python Software Foundation

BSD-2-Clause AND BSD-3-Clause

---------------------------------------------------------

---------------------------------------------------------

colorama 0.4.6 - BSD-2-Clause AND BSD-3-Clause


Copyright Jonathan Hartley 2013
Copyright (c) 2010 Jonathan Hartley
Copyright Jonathan Hartley & Arnon Yaari, 2013-2020

BSD-2-Clause AND BSD-3-Clause

---------------------------------------------------------

---------------------------------------------------------

joblib 1.2.0 - BSD-2-Clause AND BSD-3-Clause


Copyright 2009 Brian Quinlan
Copyright 2017, Thomas Moreau
Copyright 2010, Gael Varoquaux
Copyright 2012, Olivier Grisel
Copyright (c) 2008 Gael Varoquaux
Copyright (c) 2009 Gael Varoquaux
Copyright (c) 2010 Gael Varoquaux
Copyright (c) 2008-2021, The joblib
Copyright (c) 2010-2011 Gael Varoquaux
copyright 2008-2021, Joblib developers
Copyright (c) 2012, Regents of the University of California
Copyright 2010, Gael Varoquaux 2001-2004, Fernando Perez 2001 Nathaniel Gray
Copyright (c) 2009 PiCloud, Inc. <https://web.archive.org/web/20140626004012/http://www.picloud.com/>

BSD-2-Clause AND BSD-3-Clause

---------------------------------------------------------

---------------------------------------------------------

websockets 10.4 - BSD-2-Clause AND BSD-3-Clause


Copyright (c) 2013-2021 Aymeric Augustin and contributors

BSD-2-Clause AND BSD-3-Clause

---------------------------------------------------------

---------------------------------------------------------

com.google.code.findbugs/jsr305 3.0.2 - BSD-3-Clause



Copyright (c) <year> <owner> . All rights reserved.

Redistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:

   1. Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.

   2. Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.

   3. Neither the name of the copyright holder nor the names of its contributors may be used to endorse or promote products derived from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

---------------------------------------------------------

---------------------------------------------------------

leetal/ios-cmake 051787e1656bcf765fa4cca532c588da03d751e4 - BSD-3-Clause



Copyright (c) 2011-2014, Andrew Fischer <<EMAIL>>

Copyright (c) 2017, Alexander Widerberg <<EMAIL>>

All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are met:

1. Redistributions of source code must retain the above copyright notice, this
list of conditions and the following disclaimer.

2. Redistributions in binary form must reproduce the above copyright notice,
this list of conditions and the following disclaimer in the documentation
and/or other materials provided with the distribution.

3. Neither the name of the copyright holder nor the names of its contributors
may be used to endorse or promote products derived from this software without
specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.


---------------------------------------------------------

---------------------------------------------------------

scipy/scipy b5d8bab88af61d61de09641243848df63380a67f - BSD-3-Clause


(c) . B' Both
(c) B Whether
(c) Date July, 1988
(c) Compute Hessian H
copyright Cephes Math
Copyright Albert Steppi
Copyright Gautam Sewani
(c) 2011 import warnings
copyrighted by Alan Genz
Copyright 2006 Johan Rade
Copyright Paul A. Bristow
Csp self.spmatrix (c) Dsp
Copyright ohn Maddock 2012
(c) 2011 import numpy as np
(c) 2012 import numpy as np
(c) 2014 import numpy as np
Copyright 2000 by Alan Genz
Copyright 2006 John Maddock
Copyright John Maddock 2005
Copyright John Maddock 2006
Copyright John Maddock 2007
Copyright John Maddock 2008
Copyright John Maddock 2009
Copyright John Maddock 2010
Copyright John Maddock 2011
Copyright John Maddock 2012
Copyright John Maddock 2013
Copyright Paul Bristow 2007
Copyright (c) 2018 ERGO-Code
Copyright (c) 2020 ERGO-Code
Copyright (c) Piers Lawrence
Copyright 2008 Gautam Sewani
Copyright 2013 Andrea Gavana
Copyright Gautam Sewani 2008
Copyright Yosef Meller, 2009
Copyright (c) 2006 Johan Rade
Copyright (c) 2014 Eric Moore
Copyright (c) 2019 Peter Bell
Copyright 2002 Gary Strangman
Copyright 2002 Pearu Peterson
Copyright 2014, Eric W. Moore
Copyright Christian Lorentzen
Copyright John Maddock 2006-7
Copyright John Maddock 2007-8
Copyright Xiaogang Zhang 2006
Copyright (c) 2008 Damian Eads
Copyright 1999 Travis Oliphant
Copyright 2005 Travis Oliphant
Copyright 2010 Paul A. Bristow
Copyright 2011 Paul A. Bristow
Copyright 2012 Paul A. Bristow
Copyright Paul A. Bristow 2006
Copyright Paul A. Bristow 2007
Copyright Paul A. Bristow 2010
Copyright Paul A. Bristow 2012
Copyright Paul A. Bristow 2013
copyrighted by Enthought, Inc.
(c) Copyright Hubert Holin 2003
(c) Copyright John Maddock 2005
(c) Copyright John Maddock 2006
(c) Copyright John Maddock 2007
(c) Copyright John Maddock 2008
(c) Copyright John Maddock 2010
Copyright (c) 2006 John Maddock
Copyright (c) 2007 John Maddock
Copyright (c) 2007, Damian Eads
Copyright (c) 2011 John Maddock
Copyright (c) 2016 Adrian Veres
Copyright (c) Tyler Reddy, 2016
Copyright Benjamin Sobotta 2012
(c) Copyright Bruno Lalande 2008
Copyright (c) 2013 Kenneth L. Ho
Copyright 1991 Dieter Kraft, FHM
Copyright Anne M. Archibald 2008
Copyright Paul A. Bristow 2006-7
(c) Copyright John Maddock 2006-7
Copyright (c) 2006 Xiaogang Zhang
Copyright (c) 2009 Pauli Virtanen
Copyright (c) 2009, Motorola, Inc
Copyright (c) 2013 Pauli Virtanen
Copyright (c) 2016-2018 ERGO-Code
Copyright (c) 2016-2019 ERGO-Code
Copyright (c) 2018-2019 ERGO-Code
Copyright 2011 Paul A. Bristow To
Copyright John Maddock 2006, 2007
Copyright John Maddock 2006, 2011
Copyright John Maddock 2006, 2012
Copyright John Maddock 2008, 2012
Copyright Paul Bristow 2006, 2007
Copyright Paul Bristow 2007, 2011
(c) Copyright Paul A. Bristow 2011
Copyright (c) 2002 Travis Oliphant
Copyright (c) 2011 Paul A. Bristow
Copyright (c) 2012 Paul A. Bristow
Copyright (c) 2018, Quansight-Labs
Copyright (c) 2019-2020 Peter Bell
Copyright (c) Pauli Virtanen, 2010
Copyright 2015 Jon Lund Steffensen
Copyright 2017 Two Blue Cubes Ltd.
Copyright Thijs van den Berg, 2008
Copyright (c) 1988 by Theo Jurriens
Copyright (c) 1993-2019 C.B. Barber
Copyright (c) Benjamin Sobotta 2012
Copyright 2002 H Lohninger, TU Wein
(c) Copyright Hubert Holin 2003-2005
Copyright (c) 2020 Michael Feldmeier
Copyright (c) Damian Eads, 2007-2008
Copyright Christopher Kormanyos 2013
Copyright Paul A. Bristow 2007, 2009
Copyright Paul A. Bristow 2007, 2010
Copyright Paul A. Bristow 2007, 2012
Copyright Paul A. Bristow 2008, 2009
Copyright Paul A. Bristow 2009, 2011
Copyright (c) 2007 - Sebastien Fabbro
Copyright (c) 2007, 2008, Damian Eads
Copyright (c) 2011 Paul A. Bristow To
Copyright (c) 2016 2017 Felix Lenders
Copyright (c) 2019 Max-Planck-Society
(c) Copyright Daryle Walker 2001, 2006
Copyright (c) 2012, Jaydeep P. Bardhan
Copyright (c) 2012, Matthew G. Knepley
Copyright (c) 2014, Janani Padmanabhan
Copyright (c) 2018 Two Blue Cubes Ltd.
Copyright 2004-2005 by Enthought, Inc.
copyright 2008- s, The SciPy community
Copyright (c) 1989-2004 Johannes Braams
Copyright (c) 1994 by Xerox Corporation
Copyright (c) 1996-2008 Rice University
Copyright (c) 2010 Thomas P. Robitaille
Copyright (c) 2001, 2002 Enthought, Inc.
Copyright (c) 2003, 2007-14 Matteo Frigo
Copyright (c) 2003-2005 Peter J. Verveer
Copyright 2002-2016 The SciPy Developers
Copyright (c) 2005-2015, Michele Simionato
Copyright (c) 2006-2008 Alexander Chemeris
Copyright (c) 2010-2019 Max-Planck-Society
Copyright (c) 2010-2020 Max-Planck-Society
Copyright 1984, 1995 by Stephen L. Moshier
Copyright 1984, 1996 by Stephen L. Moshier
Copyright (c) 1993-2019 The Geometry Center
Copyright 1985 by Stephen L. Moshier Direct
Copyright (c) 2001-2011 - Scilab Enterprises
Copyright (c) 2010 - Jordi Gutierrez Hermoso
Copyright 1997-2008 by Agner Fog. GNU General
Copyright 2002-2008 by Agner Fog. GNU General
Copyright 2002-2014 by Agner Fog. GNU General
Copyright 2004-2008 by Agner Fog. GNU General
Copyright 2004-2013 by Agner Fog. GNU General
Copyright J.S. Roy (<EMAIL>), 2002-2005
Copyright (c) 2009, Pauli Virtanen <<EMAIL>>
Copyright (c) 2015, Pauli Virtanen <<EMAIL>>
Copyright 1984, 1987, 1995 by Stephen L. Moshier
Copyright 1984, 1987, 2000 by Stephen L. Moshier
Copyright 1984, 1995, 2000 by Stephen L. Moshier
Copyright 1985, 1987, 2000 by Stephen L. Moshier
Copyright Paul A. Bristow 2007, 2009, 2010, 2012
Copyright 1984, 1987 by Stephen L. Moshier Direct
Copyright 1984, 1991 by Stephen L. Moshier Direct
Copyright 1985, 1987 by Stephen L. Moshier Direct
Copyright (c) 2010 David Fong and Michael Saunders
Copyright (c) 2006, Systems Optimization Laboratory
Copyright (c) 2007, John Travers <<EMAIL>>
Copyright (c) 1998-2003 by the University of Florida
Copyright (c) 2016 Wenzel Jakob <<EMAIL>>
Copyright 1984, 1987, 1988, 2000 by Stephen L. Moshier
Copyright 1984, 1987, 1989, 1995 by Stephen L. Moshier
Copyright 1984, 1987, 1989, 2000 by Stephen L. Moshier
Copyright 1984, 1987, 1992, 2000 by Stephen L. Moshier
Copyright (c) Donald Stufft and individual contributors
Copyright 1984, 1987, 1988 by Stephen L. Moshier Direct
Copyright 1984, 1987, 1989 by Stephen L. Moshier Direct
Copyright 1984, 1987, 1993 by Stephen L. Moshier Direct
Copyright 1985, 1987, 1989 by Stephen L. Moshier Direct
Copyright (c) 2012 Massachusetts Institute of Technology
Copyright (c) 2006-2015 The University of Colorado Denver
Copyright (c) 2006-2007, Robert Hetland <<EMAIL>>
Copyright (c) 2002-2005, Jean-Sebastien Roy (<EMAIL>)
Copyright (c) 2004-2005, Jean-Sebastien Roy (<EMAIL>)
Copyright 1984, 1987, 1989, 1992, 2000 by Stephen L. Moshier
Copyright (c) 2000-2015 The University of California Berkeley
Copyright 1984, 1987, 1988, 1992 by Stephen L. Moshier Direct
Copyright 1984, 1987, 1989, 1992 by Stephen L. Moshier Direct
Copyright (c) Tyler Reddy, Richard Gowers, and Max Linke, 2016
Copyright (c) 2004 David M. Cooke <<EMAIL>>
Copyright Daryle Walker, Hubert Holin, John Maddock 2006 - 2007
copyrighted 2004 by David M. Cooke <<EMAIL>>
Copyright (c) 2003, 2007-14 Massachusetts Institute of Technology
Copyright (c) 2001-2002 Enthought, Inc. 2003-2019, SciPy Developers
Copyright (c) 2008 Brian M. Clapper <<EMAIL>> , Gael Varoquaux
Copyright 2014 by P.-G. Martinsson, V. Rokhlin, Y. Shkolnisky, and M. Tygert
copyright A. Volgenant/Amsterdam School of Economics, University of Amsterdam
Copyright 1987-, A. Volgenant/Amsterdam School of Economics, University of Amsterdam
Copyright (c) 2018 Sylvain Gubian <<EMAIL>> , Yang Xiang <<EMAIL>>
Copyright (c) Tyler Reddy, Ross Hemsley, Edd Edmondson, Nikolai Nowaczyk, Joe Pitt-Francis, 2015
Copyright (c) 1992-2015 The University of Tennessee and The University of Tennessee Research Foundation
Copyright (c) 1990-2004 by Johannes Braams texniek at braams.cistron.nl Kersengaarde 33 2723 BP Zoetermeer NL
Copyright (c) 2003, The Regents of the University of California, through Lawrence Berkeley National Laboratory
Copyright (c) 2003-2009, The Regents of the University of California, through Lawrence Berkeley National Laboratory
Copyright (c) 2001, 2002, 2003, 2004, 2005, 2006, 2007, 2008, 2009, 2010, 2011, 2012, 2013, 2014, 2015, 2016, 2017, 2018, 2019, 2020, 2021 Python Software Foundation
(c) KOKOKOKOKKuKyKxKxKxKxKxKxKxKxKxKxKxKxKxKxKxKxKxKxKxKxKxKxKxKxKxKxKxKxKxKyK KyKxKzKzKzKzKzKzKzKzKzKzKzKzKyKxKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzK K K KzK K

Copyright (c) 2001-2002 Enthought, Inc.  2003-2019, SciPy Developers.
All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions
are met:

1. Redistributions of source code must retain the above copyright
   notice, this list of conditions and the following disclaimer.

2. Redistributions in binary form must reproduce the above
   copyright notice, this list of conditions and the following
   disclaimer in the documentation and/or other materials provided
   with the distribution.

3. Neither the name of the copyright holder nor the names of its
   contributors may be used to endorse or promote products derived
   from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.


---------------------------------------------------------

---------------------------------------------------------

curl/curl 2cfac302fbeec68f1727cba3d1705e16f02220ad - curl


(c) CopyRight EdelWeb
copyright When contributing
Copyright 2009, John Malmberg
Copyright 2011, John Malmberg
Copyright 2012, John Malmberg
Copyright 2013, John Malmberg
Copyright 2014, John Malmberg
Copyright (c) 2001 Eric Lavigne
Copyright (c) 2016, Evgeny Grin
Copyright (c) 2000 - 2019 David Odin
Copyright (c) 2001 Alexander Peslyak
Copyright (c) 2006-2015 wolfSSL Inc.
Copyright (c) 1998-2016 Daniel Stenberg
Copyright (c) 2017 - 2019 Red Hat, Inc.
Copyright (c) 2004 - 2014, Guenter Knauf
Copyright (c) 2004 - 2015, Guenter Knauf
Copyright (c) 2011 - 2019, Jim Hollinger
Copyright (c) 2004 - 2019 Daniel Stenberg
Copyright (c) 1998 - $year, Daniel Stenberg
Copyright (c) 2003 - 2019 Simtec Electronics
Copyright 2002-2016 Core Security Technologies
Copyright (c) 2017 Reini Urban <<EMAIL>>
Copyright (c) 2000 The Apache Software Foundation.
Copyright (c) 2003-2016 CORE Security Technologies
Copyright (c) 2010, Howard Chu, <<EMAIL>>
Copyright (c) 2010, Mandy Wu, <<EMAIL>>
Copyright (c) 2003 - 2019 The OpenEvidence Project.
Copyright (c) 2011 Daniel Stenberg <<EMAIL>>
Copyright (c) 2013 Daniel Stenberg <<EMAIL>>
Copyright (c) 1996-2019 Internet Software Consortium.
Copyright (c) 1998, 1999 Kungliga Tekniska Hogskolan.
Copyright (c) 2010, Howard Chu, <<EMAIL>>
Copyright (c) 2011, Daniel Stenberg, <<EMAIL>>
Copyright (c) 2015, Daniel Stenberg, <<EMAIL>>
Copyright (c) 2015, Jay Satiro, <<EMAIL>>
Copyright (c) 2016, Daniel Stenberg, <<EMAIL>>
Copyright (c) 2017, Daniel Stenberg, <<EMAIL>>
Copyright (c) 2018, Daniel Stenberg, <<EMAIL>>
Copyright (c) 2019, Daniel Stenberg, <<EMAIL>>
Copyright (c) 2006, David Shaw <<EMAIL>>
Copyright (c) 2001 Michael Teo <<EMAIL>>
Copyright (c) 2001-2004 Damien Miller <<EMAIL>>
Copyright (c) 2008 Kaveh Ghazi <<EMAIL>>
Copyright (c) 2010, Hoi-Ho Chan, <<EMAIL>>
Copyright (c) 2003 - 2007, Gisle Vanem <<EMAIL>>
Copyright (c) 2003 - 2008, Gisle Vanem <<EMAIL>>
Copyright (c) 2005 - 2008, Gisle Vanem <<EMAIL>>
Copyright (c) 2005 - 2009, Gisle Vanem <<EMAIL>>
Copyright (c) 2019, Michael Forney, <<EMAIL>>
Copyright (c) 1996 - 2019 by Internet Software Consortium.
Copyright (c) 2010-2012, Daniel Stenberg, <<EMAIL>>
Copyright (c) 2010-2015, Daniel Stenberg, <<EMAIL>>
Copyright (c) 2010-2018, Daniel Stenberg, <<EMAIL>>
Copyright (c) 2010-2019, Daniel Stenberg, <<EMAIL>>
Copyright (c) 2012, Marc Hoersken, <<EMAIL>>
Copyright (c) 2013 - 2018 Daniel Stenberg <<EMAIL>>
Copyright (c) 2013, Linus Nielsen Feltzing <<EMAIL>>
Copyright (c) 2013-2017, Daniel Stenberg, <<EMAIL>>
Copyright (c) 2013-2019, Daniel Stenberg, <<EMAIL>>
Copyright (c) 2014, Steve Holme, <<EMAIL>>
Copyright (c) 2016-2019, Daniel Stenberg, <<EMAIL>>
Copyright (c) 2017-2019, Daniel Stenberg, <<EMAIL>>
Copyright (c) 2018-2019, Daniel Stenberg, <<EMAIL>>
Copyright (c) 1983 Regents of the University of California.
Copyright (c) 1998 - 2019 Daniel Stenberg, <<EMAIL>>
Copyright (c) 2012, Mark Salisbury, <<EMAIL>>
Copyright (c) 1996 - 2020, Daniel Stenberg, <<EMAIL>>
Copyright (c) 1997 - 2019, Daniel Stenberg, <<EMAIL>>
Copyright (c) 1998 - 2009, Daniel Stenberg, <<EMAIL>>
Copyright (c) 1998 - 2010, Daniel Stenberg, <<EMAIL>>
Copyright (c) 1998 - 2011, Daniel Stenberg, <<EMAIL>>
Copyright (c) 1998 - 2012, Daniel Stenberg, <<EMAIL>>
Copyright (c) 1998 - 2013, Daniel Stenberg, <<EMAIL>>
Copyright (c) 1998 - 2014, Daniel Stenberg, <<EMAIL>>
Copyright (c) 1998 - 2015, Daniel Stenberg, <<EMAIL>>
Copyright (c) 1998 - 2016, Daniel Stenberg, <<EMAIL>>
Copyright (c) 1998 - 2017, Daniel Stenberg, <<EMAIL>>
Copyright (c) 1998 - 2018, Daniel Stenberg, <<EMAIL>>
Copyright (c) 1998 - 2019, Daniel Stenberg, <<EMAIL>>
Copyright (c) 1998 - 2020, Daniel Stenberg, <<EMAIL>>
Copyright (c) 1999 - 2018, Daniel Stenberg, <<EMAIL>>
Copyright (c) 1999 - 2019, Daniel Stenberg, <<EMAIL>>
Copyright (c) 2001 - 2018, Daniel Stenberg, <<EMAIL>>
Copyright (c) 2001 - 2019, Daniel Stenberg, <<EMAIL>>
Copyright (c) 2003 - 2015, Daniel Stenberg, <<EMAIL>>
Copyright (c) 2003 - 2017, Daniel Stenberg, <<EMAIL>>
Copyright (c) 2004 - 2020, Daniel Stenberg, <<EMAIL>>
Copyright (c) 2005 - 2015, Daniel Stenberg, <<EMAIL>>
Copyright (c) 2005 - 2017, Daniel Stenberg, <<EMAIL>>
Copyright (c) 2008 - 2016, Daniel Stenberg, <<EMAIL>>
Copyright (c) 2009 - 2019, Daniel Stenberg, <<EMAIL>>
Copyright (c) 2010 - 2018, Daniel Stenberg, <<EMAIL>>
Copyright (c) 2010 - 2019, Daniel Stenberg, <<EMAIL>>
Copyright (c) 2010 - 2019, Howard Chu, <<EMAIL>>
Copyright (c) 2011 - 2018, Daniel Stenberg, <<EMAIL>>
Copyright (c) 2011 - 2019, Daniel Stenberg, <<EMAIL>>
Copyright (c) 2012 - 2016, Daniel Stenberg, <<EMAIL>>
Copyright (c) 2012 - 2017, Daniel Stenberg, <<EMAIL>>
Copyright (c) 2012 - 2018, Daniel Stenberg, <<EMAIL>>
Copyright (c) 2012 - 2019, Daniel Stenberg, <<EMAIL>>
Copyright (c) 2012 - 2020, Daniel Stenberg, <<EMAIL>>
Copyright (c) 2013 - 2019, Daniel Stenberg, <<EMAIL>>
Copyright (c) 2014, Vijay Panghal, <<EMAIL>>
Copyright (c) 2015 - 2016, Daniel Stenberg, <<EMAIL>>
Copyright (c) 2015 - 2019, Daniel Stenberg, <<EMAIL>>
Copyright (c) 2016 - 2019, Daniel Stenberg, <<EMAIL>>
Copyright (c) 2017 - 2019, Daniel Stenberg, <<EMAIL>>
Copyright (c) 2017-2018, Yiming Jing, <<EMAIL>>
Copyright (c) 2018 - 2019 Jeroen Ooms <<EMAIL>>
Copyright (c) 2018 - 2019, Daniel Stenberg, <<EMAIL>>
Copyright (c) 2010, 2011, Hoi-Ho Chan, <<EMAIL>>
Copyright (c) 2010 - 2011, Hoi-Ho Chan, <<EMAIL>>
Copyright (c) 2012 - 2014, Nick Zitzmann, <<EMAIL>>
Copyright (c) 2012 - 2017, Nick Zitzmann, <<EMAIL>>
Copyright (c) 2017 - 2018, Yiming Jing, <<EMAIL>>
Copyright (c) 2012 - 2016, Marc Hoersken, <<EMAIL>>
Copyright (c) 2012 - 2018, Steve Holme, <<EMAIL>>
Copyright (c) 2012 - 2019, Steve Holme, <<EMAIL>>
Copyright (c) 2013 - 2018, Linus Nielsen Feltzing <<EMAIL>>
Copyright (c) 2014 - 2016, Steve Holme, <<EMAIL>>
Copyright (c) 2014 - 2017, Steve Holme, <<EMAIL>>
Copyright (c) 2014 - 2019, Steve Holme, <<EMAIL>>
Copyright (c) 2015 - 2019, Steve Holme, <<EMAIL>>
Copyright (c) 2016 - 2019, Steve Holme, <<EMAIL>>
Copyright (c) 1998 - 2011, 2017, Daniel Stenberg, <<EMAIL>>
Copyright (c) 1998 - 2011, 2018, Daniel Stenberg, <<EMAIL>>
Copyright (c) 1998 - 2012, 2017, Daniel Stenberg, <<EMAIL>>
Copyright (c) 1998 - 2013, 2017, Daniel Stenberg, <<EMAIL>>
Copyright (c) 1998 - 2014, 2017, Daniel Stenberg, <<EMAIL>>
Copyright (c) 1998 - 2014, 2019, Daniel Stenberg, <<EMAIL>>
Copyright (c) 1998 - 2015, 2017, Daniel Stenberg, <<EMAIL>>
Copyright (c) 1998 - 2019, 2018, Daniel Stenberg, <<EMAIL>>
Copyright (c) 2012 - 2014, Linus Nielsen Feltzing, <<EMAIL>>
Copyright (c) 2012 - 2016, Linus Nielsen Feltzing, <<EMAIL>>
Copyright (c) 2013 - 2019, Linus Nielsen Feltzing, <<EMAIL>>
Copyright (c) 1998 - 2016, Vijay Panghal, <<EMAIL>>
Copyright (c) 2009, Markus Moeller, <<EMAIL>>
Copyright (c) 1998 - 2019, Florin Petriuc, <<EMAIL>>
Copyright (c) 2014, Bill Nagel <<EMAIL>> , Exacq Technologies
Copyright (c) 2009, 2011, Markus Moeller, <<EMAIL>>
Copyright symbol VALUE License', https://curl.haxx.se/docs/copyright.html
Copyright (c) 1998, 1999, 2017 Kungliga Tekniska Hogskolan (Royal Institute of Technology, Stockholm, Sweden).
Copyright (c) 1995, 1996, 1997, 1998, 1999 Kungliga Tekniska Hogskolan (Royal Institute of Technology, Stockholm, Sweden).

COPYRIGHT AND PERMISSION NOTICE

Copyright (c) 1996 - 2020, Daniel Stenberg, <<EMAIL>>, and many
contributors, see the THANKS file.

All rights reserved.

Permission to use, copy, modify, and distribute this software for any purpose
with or without fee is hereby granted, provided that the above copyright
notice and this permission notice appear in all copies.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT OF THIRD PARTY RIGHTS. IN
NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,
DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR
OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE
OR OTHER DEALINGS IN THE SOFTWARE.

Except as contained in this notice, the name of a copyright holder shall not
be used in advertising or otherwise to promote the sale, use or other dealings
in this Software without prior written authorization of the copyright holder.


---------------------------------------------------------

---------------------------------------------------------

junit/junit 4.12 - EPL-1.0



Eclipse Public License - v 1.0

THE ACCOMPANYING PROGRAM IS PROVIDED UNDER THE TERMS OF THIS ECLIPSE PUBLIC LICENSE ("AGREEMENT"). ANY USE, REPRODUCTION OR DISTRIBUTION OF THE PROGRAM CONSTITUTES RECIPIENT'S ACCEPTANCE OF THIS AGREEMENT.

   1. DEFINITIONS

   "Contribution" means:

      a) in the case of the initial Contributor, the initial code and documentation distributed under this Agreement, and

      b) in the case of each subsequent Contributor:

         i) changes to the Program, and

         ii) additions to the Program;

         where such changes and/or additions to the Program originate from and are distributed by that particular Contributor. A Contribution 'originates' from a Contributor if it was added to the Program by such Contributor itself or anyone acting on such Contributor's behalf. Contributions do not include additions to the Program which: (i) are separate modules of software distributed in conjunction with the Program under their own license agreement, and (ii) are not derivative works of the Program.

   "Contributor" means any person or entity that distributes the Program.

   "Licensed Patents" mean patent claims licensable by a Contributor which are necessarily infringed by the use or sale of its Contribution alone or when combined with the Program.

   "Program" means the Contributions distributed in accordance with this Agreement.

   "Recipient" means anyone who receives the Program under this Agreement, including all Contributors.

   2. GRANT OF RIGHTS

      a) Subject to the terms of this Agreement, each Contributor hereby grants Recipient a non-exclusive, worldwide, royalty-free copyright license to reproduce, prepare derivative works of, publicly display, publicly perform, distribute and sublicense the Contribution of such Contributor, if any, and such derivative works, in source code and object code form.

      b) Subject to the terms of this Agreement, each Contributor hereby grants Recipient a non-exclusive, worldwide, royalty-free patent license under Licensed Patents to make, use, sell, offer to sell, import and otherwise transfer the Contribution of such Contributor, if any, in source code and object code form. This patent license shall apply to the combination of the Contribution and the Program if, at the time the Contribution is added by the Contributor, such addition of the Contribution causes such combination to be covered by the Licensed Patents. The patent license shall not apply to any other combinations which include the Contribution. No hardware per se is licensed hereunder.

      c) Recipient understands that although each Contributor grants the licenses to its Contributions set forth herein, no assurances are provided by any Contributor that the Program does not infringe the patent or other intellectual property rights of any other entity. Each Contributor disclaims any liability to Recipient for claims brought by any other entity based on infringement of intellectual property rights or otherwise. As a condition to exercising the rights and licenses granted hereunder, each Recipient hereby assumes sole responsibility to secure any other intellectual property rights needed, if any. For example, if a third party patent license is required to allow Recipient to distribute the Program, it is Recipient's responsibility to acquire that license before distributing the Program.

      d) Each Contributor represents that to its knowledge it has sufficient copyright rights in its Contribution, if any, to grant the copyright license set forth in this Agreement.

   3. REQUIREMENTS

   A Contributor may choose to distribute the Program in object code form under its own license agreement, provided that:

      a) it complies with the terms and conditions of this Agreement; and

      b) its license agreement:

         i) effectively disclaims on behalf of all Contributors all warranties and conditions, express and implied, including warranties or conditions of title and non-infringement, and implied warranties or conditions of merchantability and fitness for a particular purpose;

         ii) effectively excludes on behalf of all Contributors all liability for damages, including direct, indirect, special, incidental and consequential damages, such as lost profits;

         iii) states that any provisions which differ from this Agreement are offered by that Contributor alone and not by any other party; and

         iv) states that source code for the Program is available from such Contributor, and informs licensees how to obtain it in a reasonable manner on or through a medium customarily used for software exchange.

   When the Program is made available in source code form:

      a) it must be made available under this Agreement; and

      b) a copy of this Agreement must be included with each copy of the Program.

      Contributors may not remove or alter any copyright notices contained within the Program.

   Each Contributor must identify itself as the originator of its Contribution, if any, in a manner that reasonably allows subsequent Recipients to identify the originator of the Contribution.

   4. COMMERCIAL DISTRIBUTION

   Commercial distributors of software may accept certain responsibilities with respect to end users, business partners and the like. While this license is intended to facilitate the commercial use of the Program, the Contributor who includes the Program in a commercial product offering should do so in a manner which does not create potential liability for other Contributors. Therefore, if a Contributor includes the Program in a commercial product offering, such Contributor ("Commercial Contributor") hereby agrees to defend and indemnify every other Contributor ("Indemnified Contributor") against any losses, damages and costs (collectively "Losses") arising from claims, lawsuits and other legal actions brought by a third party against the Indemnified Contributor to the extent caused by the acts or omissions of such Commercial Contributor in connection with its distribution of the Program in a commercial product offering. The obligations in this section do not apply to any claims or Losses relating to any actual or alleged intellectual property infringement. In order to qualify, an Indemnified Contributor must: a) promptly notify the Commercial Contributor in writing of such claim, and b) allow the Commercial Contributor to control, and cooperate with the Commercial Contributor in, the defense and any related settlement negotiations. The Indemnified Contributor may participate in any such claim at its own expense.

   For example, a Contributor might include the Program in a commercial product offering, Product X. That Contributor is then a Commercial Contributor. If that Commercial Contributor then makes performance claims, or offers warranties related to Product X, those performance claims and warranties are such Commercial Contributor's responsibility alone. Under this section, the Commercial Contributor would have to defend claims against the other Contributors related to those performance claims and warranties, and if a court requires any other Contributor to pay any damages as a result, the Commercial Contributor must pay those damages.

   5. NO WARRANTY

   EXCEPT AS EXPRESSLY SET FORTH IN THIS AGREEMENT, THE PROGRAM IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED INCLUDING, WITHOUT LIMITATION, ANY WARRANTIES OR CONDITIONS OF TITLE, NON-INFRINGEMENT, MERCHANTABILITY OR FITNESS FOR A PARTICULAR PURPOSE. Each Recipient is solely responsible for determining the appropriateness of using and distributing the Program and assumes all risks associated with its exercise of rights under this Agreement, including but not limited to the risks and costs of program errors, compliance with applicable laws, damage to or loss of data, programs or equipment, and unavailability or interruption of operations.

   6. DISCLAIMER OF LIABILITY

   EXCEPT AS EXPRESSLY SET FORTH IN THIS AGREEMENT, NEITHER RECIPIENT NOR ANY CONTRIBUTORS SHALL HAVE ANY LIABILITY FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING WITHOUT LIMITATION LOST PROFITS), HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OR DISTRIBUTION OF THE PROGRAM OR THE EXERCISE OF ANY RIGHTS GRANTED HEREUNDER, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGES.

   7. GENERAL

   If any provision of this Agreement is invalid or unenforceable under applicable law, it shall not affect the validity or enforceability of the remainder of the terms of this Agreement, and without further action by the parties hereto, such provision shall be reformed to the minimum extent necessary to make such provision valid and enforceable.

   If Recipient institutes patent litigation against any entity (including a cross-claim or counterclaim in a lawsuit) alleging that the Program itself (excluding combinations of the Program with other software or hardware) infringes such Recipient's patent(s), then such Recipient's rights granted under Section 2(b) shall terminate as of the date such litigation is filed.

   All Recipient's rights under this Agreement shall terminate if it fails to comply with any of the material terms or conditions of this Agreement and does not cure such failure in a reasonable period of time after becoming aware of such noncompliance. If all Recipient's rights under this Agreement terminate, Recipient agrees to cease use and distribution of the Program as soon as reasonably practicable. However, Recipient's obligations under this Agreement and any licenses granted by Recipient relating to the Program shall continue and survive.

   Everyone is permitted to copy and distribute copies of this Agreement, but in order to avoid inconsistency the Agreement is copyrighted and may only be modified in the following manner. The Agreement Steward reserves the right to publish new versions (including revisions) of this Agreement from time to time. No one other than the Agreement Steward has the right to modify this Agreement. The Eclipse Foundation is the initial Agreement Steward. The Eclipse Foundation may assign the responsibility to serve as the Agreement Steward to a suitable separate entity. Each new version of the Agreement will be given a distinguishing version number. The Program (including Contributions) may always be distributed subject to the version of the Agreement under which it was received. In addition, after a new version of the Agreement is published, Contributor may elect to distribute the Program (including its Contributions) under the new version. Except as expressly stated in Sections 2(a) and 2(b) above, Recipient receives no rights or licenses to the intellectual property of any Contributor under this Agreement, whether expressly, by implication, estoppel or otherwise. All rights in the Program not expressly granted under this Agreement are reserved.

   This Agreement is governed by the laws of the State of New York and the intellectual property laws of the United States of America. No party to this Agreement will bring a legal action under this Agreement more than one year after the cause of action arose. Each party waives its rights to a jury trial in any resulting litigation.

---------------------------------------------------------

---------------------------------------------------------

jakarta.json/jakarta.json-api 2.0.1 - EPL-2.0 OR GPL-2.0-only WITH Classpath-exception-2.0


copyrighted by the Free Software Foundation
Copyright 2019, $ current.year Eclipse Foundation.
Copyright (c) 2011, 2021 Oracle and/or its affiliates.
Copyright (c) 2018, 2020 Oracle and/or its affiliates.
Copyright (c) 1989, 1991 Free Software Foundation, Inc.

EPL-2.0 OR GPL-2.0-only WITH Classpath-exception-2.0

---------------------------------------------------------

---------------------------------------------------------

org.glassfish/jakarta.json 2.0.1 - EPL-2.0 OR GPL-2.0-only WITH Classpath-exception-2.0


Copyright (c) 2019, 2020 Eclipse Foundation
copyrighted by the Free Software Foundation
Copyright (c) 2011, 2021 Oracle and/or its affiliates
Copyright (c) 2013, 2021 Oracle and/or its affiliates
Copyright (c) 2018, 2020 Oracle and/or its affiliates
Copyright (c) 1989, 1991 Free Software Foundation, Inc.
Copyright (c) 2011, 2021 Oracle and/or its affiliates. All rights reserved.
Copyright (c) 2013, 2021 Oracle and/or its affiliates. All rights reserved.

EPL-2.0 OR GPL-2.0-only WITH Classpath-exception-2.0

---------------------------------------------------------

---------------------------------------------------------

com.azure/azure-core 1.0.0 - MIT


Copyright (c) Microsoft Corporation.

MIT License

Copyright (c) <year> <copyright holders>

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

---------------------------------------------------------

---------------------------------------------------------

com.azure/azure-core 1.5.0 - MIT



MIT License

Copyright (c) <year> <copyright holders>

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

---------------------------------------------------------

---------------------------------------------------------

com.azure/azure-core-http-netty 1.0.0 - MIT



MIT License

Copyright (c) <year> <copyright holders>

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

---------------------------------------------------------

---------------------------------------------------------

com.azure/azure-core-http-netty 1.5.0 - MIT


Copyright (c) Microsoft Corporation.

MIT License

Copyright (c) <year> <copyright holders>

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

---------------------------------------------------------

---------------------------------------------------------

cpprestsdk.v141 2.10.12.1 - MIT


Copyright 2019
Copyright (c) Microsoft.
Copyright 1995-2017 Mark Adler
Copyright (c) Microsoft Corporation.
Copyright 1995-2017 Jean-loup Gailly and Mark Adler
Copyright 1995-2017 Jean-loup Gailly and Mark Adler Qkkbal MGiI

MIT License

Copyright (c) <year> <copyright holders>

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

---------------------------------------------------------

---------------------------------------------------------

fmegen/azure-c-shared-utility 4c1a492088fdd4a20b012462f78c0dd653003ee2 - MIT


Copyright (c) 2015
Copyright 2015 AzureIoT
Copyright (c) Microsoft.
Copyright (c) Firmwave Ltd.
Copyright Copyright (c) 2014
Copyright Copyright (c) 2015
Copyright (c) Texas Instruments.
Copyright (c) Microsoft Corporation.
Copyright 2016 Microsoft Azure C Native
Copyright (c) 1992-2013 by P.J. Plauger.
copyrighted by the Free Software Foundation
Copyright (c) 1998-2011 The OpenSSL Project.
Copyright (c) 1995-1998 Eric Young (<EMAIL>)
Copyright (c) 1989, 1991 Free Software Foundation, Inc.
Copyright 2015-2016 Espressif Systems (Shanghai) PTE LTD
Copyright (c) 2001-2004 Swedish Institute of Computer Science.

Microsoft Azure IoT SDKs 
Copyright (c) Microsoft Corporation
All rights reserved. 
MIT License
Permission is hereby granted, free of charge, to any person obtaining a copy 
of this software and associated documentation files (the ""Software""), to deal 
in the Software without restriction, including without limitation the rights 
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell 
copies of the Software, and to permit persons to whom the Software is 
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in 
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED *AS IS*, WITHOUT WARRANTY OF ANY KIND, EXPRESS OR 
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, 
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE 
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER 
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, 
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN 
THE SOFTWARE.

---------------------------------------------------------

---------------------------------------------------------

Microsoft.Win32.Registry 4.7.0 - MIT


(c) Microsoft Corporation.
Copyright (c) .NET Foundation.
Copyright (c) 2011, Google Inc.
(c) 1997-2005 Sean Eron Anderson.
Copyright (c) 2007 James Newton-King
Copyright (c) 1991-2017 Unicode, Inc.
Copyright (c) 2013-2017, Alfred Klomp
Copyright (c) 2015-2017, Wojciech Mula
Copyright (c) 2005-2007, Nick Galbreath
Portions (c) International Organization
Copyright (c) 2015 The Chromium Authors.
Copyright (c) 2004-2006 Intel Corporation
Copyright (c) 2016-2017, Matthieu Darbois
Copyright (c) .NET Foundation Contributors
Copyright (c) .NET Foundation and Contributors
Copyright (c) 2011 Novell, Inc (http://www.novell.com)
Copyright (c) 1995-2017 Jean-loup Gailly and Mark Adler
Copyright (c) 2009, 2010, 2013-2016 by the Brotli Authors.
Copyright (c) YEAR W3C(r) (MIT, ERCIM, Keio, Beihang). Disclaimers THIS WORK IS PROVIDED AS

The MIT License (MIT)

Copyright (c) .NET Foundation and Contributors

All rights reserved.

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.


---------------------------------------------------------

---------------------------------------------------------

microsoft/cntk d1925a19d8ddc25202a4a88fb7b2979e445bf6b8 - MIT


Copyright (c) Microsoft.
copyright (c) Microsoft.
copyright 2017, Microsoft
Feedback or (c) Licensee's
Copyright (c) 2015 Microsoft
Copyright Copyright (c) 2016
Copyright Copyright (c) 2017
Copyright (c) 2016 Microsoft.
Copyright (c) 2015-2016 AlpacaDB
Copyright Rene Rivera 2004-2007.
Copyright 2015 Guoguo Chen Apache
Copyright (c) 2016 Oussama ENNAFII
Copyright (c) 2015-2016, Itseez Inc.
Copyright (c) Microsoft Corporation.
Copyright (c) 2017 Intel Corporation.
Copyright (c) 2016, NVIDIA CORPORATION.
Copyright (c) 2017, NVIDIA CORPORATION.
Copyright 2012 Johns Hopkins University
Copyright (c) 2000-2016, Intel Corporation
Copyright (c) 2015-2016, OpenCV Foundation
Copyright (c) 2009-2011, Willow Garage Inc.
Copyright (c) 2009-2016, NVIDIA Corporation
Copyright (c) 2016-2017, NVIDIA CORPORATION.
Copyright 2012-2014 Brno University of Technology
Copyright Beman Dawes, David Abrahams, 1998-2005.
Copyright (c) 1991-2005 Carnegie Mellon University.
Copyright (c) 1993-2015 Carnegie Mellon University.
Copyright (c) 2010-2013, Advanced Micro Devices, Inc.
Copyright 2012-2013 Karel Vesely, Daniel Povey Apache
Copyright (c) Facebook Inc. and Microsoft Corporation.
Copyright (c) 1995-2013 Jean-loup Gailly and Mark Adler
Copyright (c) 1999-2014 Dieter Baron and Thomas Klausner
Copyright 2012-2013 Karel Vesely, Daniel Povey 2015 Yu Zhang Apache
Copyright 2012 Johns Hopkins University (Author Daniel Povey) Apache

# Microsoft Cognitive Toolkit (CNTK)

Copyright (c) Microsoft Corporation

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions: 

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software. 

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

## THIRD PARTY NOTICES

This project is based on or incorporates material from the projects listed below (Third Party IP). The original copyright notice and the license under which Microsoft received such Third Party IP, are set forth below. Such licenses and notices are provided for informational purposes only. Where permitted, Microsoft licenses the Third Party IP to you under the licensing terms for the Microsoft product. Microsoft reserves all other rights not expressly granted under this agreement, whether by implication, estoppel or otherwise.

### a. INTEL (R) MATH KERNEL LIBRARY (INTEL (R) MKL)

CNTK distribution contains Redistributable components of Intel (r) Math Kernel Library (Intel (r) MKL), Copyright © 2017 Intel Corporation. 

Limited patent license. Intel grants you a world-wide, royalty-free, non-exclusive license under patents it now or hereafter owns or controls to make, have made, use, import, offer to sell and sell (“Utilize”) this Software, but solely to the extent that any such patent is necessary to Utilize the Software alone. The patent license shall not apply to any combinations which include this software. No hardware per se is licensed hereunder.

Third party and other Intel programs. “Third Party Programs” are the files listed in the “third-party-programs.txt” text file that is included with the Software and may include Intel programs under separate license terms. Third Party Programs, even if included with the distribution of the Materials, are governed by separate license terms and those license terms solely govern your use of those programs.

DISCLAIMER. THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT ARE DISCLAIMED. THIS SOFTWARE IS NOT INTENDED NOR AUTHORIZED FOR USE IN SYSTEMS OR APPLICATIONS WHERE FAILURE OF THE SOFTWARE MAY CAUSE PERSONAL INJURY OR DEATH.

LIMITATION OF LIABILITY. IN NO EVENT WILL INTEL BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE. YOU AGREE TO INDEMNIFY AND HOLD INTEL HARMLESS AGAINST ANY CLAIMS AND EXPENSES RESULTING FROM YOUR USE OR UNAUTHORIZED USE OF THE SOFTWARE.

No support. Intel may make changes to the Software, at any time without notice, and is not obligated to support, update or provide training for the Software.

Termination. Intel may terminate your right to use the Software in the event of your breach of this Agreement and you fail to cure the breach within a reasonable period of time.

Feedback. Should you provide Intel with comments, modifications, corrections, enhancements or other input (“Feedback”) related to the Software Intel will be free to use, disclose, reproduce, license or otherwise distribute or exploit the Feedback in its sole discretion without any obligations or restrictions of any kind, including without limitation, intellectual property rights or licensing obligations.

Compliance with laws. You agree to comply with all relevant laws and regulations governing your use, transfer, import or export (or prohibition thereof) of the Software.

Governing law. All disputes will be governed by the laws of the United States of America and the State of Delaware without reference to conflict of law principles and subject to the exclusive jurisdiction of the state or federal courts sitting in the State of Delaware, and each party agrees that it submits to the personal jurisdiction and venue of those courts and waives any objections. The United Nations Convention on Contracts for the International Sale of Goods (1980) is specifically excluded and will not apply to the Software.


### b. BOOST C++ LIBRARIES

Copyright Beman Dawes, David Abrahams, 1998-2005.  
Copyright Rene Rivera 2004-2007.

Provided for Informational Purposes Only

Boost Software License - Version 1.0 - August 17th, 2003

Permission is hereby granted, free of charge, to any person or organization obtaining a copy of the software and accompanying documentation covered by this license (the "Software") to use, reproduce, display, distribute, execute, and transmit the Software, and to prepare derivative works of the Software, and to permit third-parties to whom the Software is furnished to do so, all subject to the following:

The copyright notices in the Software and this entire statement, including the above license grant, this restriction and the following disclaimer, must be included in all copies of the Software, in whole or in part, and all derivative works of the Software, unless such copies or derivative works are solely in the form of machine-executable object code generated by a source language processor.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE, TITLE AND NON-INFRINGEMENT. IN NO EVENT SHALL THE COPYRIGHT HOLDERS OR ANYONE DISTRIBUTING THE SOFTWARE BE LIABLE FOR ANY DAMAGES OR OTHER LIABILITY, WHETHER IN CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

### c. ATIS DATASETS

CNTK distribution contains a subset of ATIS Datasets:

Hemphill, Charles, et al. ATIS0 Complete LDC93S4A. Web Download. Philadelphia: Linguistic Data Consortium, 1993.

Garofolo, John, et al. ATIS2 LDC93S5. Web Download. Philadelphia: Linguistic Data Consortium, 1993.

Dahl, Deborah, et al. ATIS3 Test Data LDC95S26. Web Download. Philadelphia: Linguistic Data Consortium, 1995.

Dahl, Deborah, et al. ATIS3 Training Data LDC94S19. Web Download. Philadelphia: Linguistic Data Consortium, 1994.

### d. TIMIT ACOUSTIC-PHONETIC CONTINUOUS SPEECH CORPUS

CNTK distribution contains a subset of TIMIT Acoustic-Phonetic Continuous Speech Corpus:

Garofolo, John, et al. TIMIT Acoustic-Phonetic Continuous Speech Corpus LDC93S1. Web Download. Philadelphia: Linguistic Data Consortium, 1993.

### e. THE PENN TREEBANK PROJECT

CNTK distribution contains a subset of the data of The Penn Treebank Project:

Marcus, Mitchell, Beatrice Santorini, and Mary Ann Marcinkiewicz. Treebank-2 LDC95T7. Web Download. Philadelphia: Linguistic Data Consortium, 1995.

### f. THE CMU AUDIO DATABASES

CNTK distribution contains a subset of the CMU Audio Databases
Copyright (c) 1991-2005 Carnegie Mellon University.  All rights reserved.

### g. THE MNIST DATABASE OF HANDWRITTEN DIGITS

CNTK distribution contains a subset of the MNIST Database of Handwritten Digits


---------------------------------------------------------

---------------------------------------------------------

microsoft/onnxruntime 366f4ebcb425b6a47c2b0decd3b39fa14eb9dbf6 - MIT


Copyright 2017
Copyright (c) 2015
Copyright (c) 2017
Copyright (c) 2021
Copyright (c) Intel
Copyright 2016 gRPC
(c) 2021 GitHub, Inc.
Copyright (c) 2012-2014
Copyright (c) 2012-2018
Copyright (c) Microsoft
(c) Microsoft Corporation
Copyright (c) Xilinx Inc.
Copyright 2019 Google LLC
Copyright (c) ONNX Project
Copyright 2008 Google Inc.
Copyright 2014 Google Inc.
Copyright 2019 JD.com Inc.
Copyright (c) 2010 ARM ltd.
Copyright (c) 2011-2013 NYU
Copyright (c) 2016 HalideIR
Copyright 2008, Google Inc.
Copyright (c) 2016 ARM, Inc.
Copyright (c) 2016 manylinux
Copyright (c) 2018 Microsoft
Copyright (c) 2018 Emscripten
Copyright (c) 2019 Google LLC
Copyright (c) 2015 Google Inc.
Copyright (c) 2017, Apple Inc.
Copyright (c) 2018, Apple Inc.
Copyright (c) 2019, Apple Inc.
Copyright (c) 2020, Apple Inc.
Copyright 2019 AMD AMDMIGraphX
copyright 2018-2021, Microsoft
Copyright (c) 2008, Google Inc.
Copyright (c) 2015 Martin Moene
Copyright (c) 2015 Yangqing Jia
Copyright (c) 2008 Chelsio, Inc.
Copyright (c) 2016 Facebook Inc.
Copyright (c) 2016-2020 ARM Ltd.
Copyright (c) 2017 Tzu-Wei Huang
Copyright (c) 2007 Evergrid, Inc.
Copyright (c) 2009-2015 Bull SAS.
Copyright (c) 2014- Facebook, Inc
Copyright (c) 2016- Facebook, Inc
Copyright 2019 NVIDIA Corporation
Copyright c Microsoft Corporation
Copyright (c) 2009 The RE2 Authors
Copyright (c) 2010-2011 Alex Brick
Copyright (c) 2013 Matthew Stevens
Copyright 2020 rock-chips.com Inc.
Copyright (c) 2007 MITSUNARI Shigeo
Copyright (c) 2013-2016 Intel, Inc.
Copyright (c) 2016 Broadcom Limited
Copyright (c) 2017-2019, Apple Inc.
Copyright (c) 2019, Lisandro Dalcin
Copyright (c) Microsoft Corporation
copyright (c) microsoft corporation
Copyright (c) 2005, NumPy Developers
Copyright (c) 2010-2012 Marat Dukhan
Copyright (c) 2015-2018 Martin Moene
Copyright (c) 2018 Intel Corporation
Copyright (c) 2019 Intel Corporation
Copyright (c) 2020 Intel Corporation
Copyright (c) 2021 Intel Corporation
Copyright 2019 Microsoft Corporation
Copyright (c) 2006-2009 Myricom, Inc.
Copyright (c) 2013-2019 Niels Lohmann
Copyright (c) 2017-2018 Facebook Inc.
Copyright 2015 The TensorFlow Authors
Copyright 2016 The TensorFlow Authors
Copyright 2016-2018 Intel Corporation
Copyright 2017 The TensorFlow Authors
Copyright 2018 The TensorFlow Authors
Copyright (c) 2006-2010 Voltaire, Inc.
Copyright (c) 2010-2011, Duane Merrill
Copyright (c) 2012-2016 Nicola Iarocci
Copyright (c) 2014-2016 Baptiste Wicht
Copyright (c) 2018 DataDirect Networks
Copyright (c) 2019 NVIDIA CORPORATION.
Copyright 2017, The TensorFlow Authors
Copyright (c) 2007-2017 IBM Corporation
Copyright (c) 2011-2017 Fujitsu Limited
Copyright (c) 2016, NVIDIA CORPORATION.
Copyright (c) 2017, NVIDIA CORPORATION.
Copyright (c) 2018, NVIDIA CORPORATION.
Copyright (c) 2020, NVIDIA CORPORATION.
Copyright (c) Microsoft Corporation.All
Copyright Microsoft Corporation Company
Copyright (c) 2007-2018 The scikit-learn
Copyright (c) 2015 Microsoft Corporation
Copyright 2018 The HuggingFace Inc. team
Copyright 2020 The HuggingFace Inc. team
Copyright (c) 2007-2017 UT-Battelle, LLC.
Copyright (c) 2014-2015 UT-Battelle, LLC.
Copyright (c) 2016-present, Facebook Inc.
Copyright (c) 2019 UChicago Argonne, LLC.
Copyright (c) 2006-2010 QLogic Corporation
Copyright (c) 2011-2017 NVIDIA Corporation
Copyright (c) 2016-present, Facebook, Inc.
Copyright (c) Microsoft, Intel Corporation
Copyright 2018 The Google AI Language Team
Copyright (c) 2006 Idiap Research Institute
Copyright (c) 2006-2017 Cisco Systems, Inc.
Copyright (c) 2018-2020 NVIDIA CORPORATION.
Copyright (c) 2019, NXP Semiconductor, Inc.
Copyright (c) 2020, NXP Semiconductor, Inc.
Copyright 2020 The Microsoft DeepSpeed Team
Copyright (c) 2011-2018, NVIDIA CORPORATION.
Portions Copyright (c) Microsoft Corporation
(c) 1995-2017 Jean-loup Gailly and Mark Adler
Copyright (c) 2007-2017 Mellanox Technologies
Copyright (c) 2012-2014 Deepmind Technologies
Copyright (c) 2015-2018 Microsoft Corporation
Copyright 2015 the original author or authors
Copyright (c) 2006-2010 Sun Microsystems, Inc.
Copyright (c) 2016-2020 Stony Brook University
Copyright (c) 2008-2017 Oak Ridge National Labs
Copyright (c) 2018 Open Neural Network Exchange
Copyright (c) 2020 Oracle and/or its affiliates
Copyright (c) 2021 Oracle and/or its affiliates
Copyright (c) Facebook, Inc. and its affiliates
Copyright (c) 2011-2014 Idiap Research Institute
Copyright (c) 2019, Oracle and/or its affiliates
Copyright (c) 2019-2020, NXP Semiconductor, Inc.
Copyright (c) 2020 Huawei Technologies Co., Ltd.
Copyright (c) 2021, Oracle and/or its affiliates
Copyright (c) 2022, Oracle and/or its affiliates
Copyright (c) 2006-2017 The University of Houston
Copyright (c) 2014-2020 Mellanox Technologies Ltd.
Copyright (c) 2017 The Android Open Source Project
Copyright (c) 2006-2012 Oracle and/or its affiliates
Copyright (c) 2006-2017 Sandia National Laboratories
Copyright (c) 2014-2017, the respective contributors
Copyright (c) 2015, 2016 the respective contributors
Copyright (c) 2016-2020 Advanced Micro Devices, Inc.
Copyright (c) 2016 Dmitry Vyukov <<EMAIL>>
Copyright (c) 2016 Los Alamos National Security, LLC.
Copyright (c) 2018-2019 Triad National Security, LLC.
Copyright (c) 2019, 2020 Oracle and/or its affiliates
Copyright (c) 2019, 2022 Oracle and/or its affiliates
Copyright (c) 2016 Wenzel Jakob <<EMAIL>>
Copyright (c) 2019, 2020, Oracle and/or its affiliates
Copyright (c) 2019, 2021, Oracle and/or its affiliates
Copyright (c) 2019, 2022, Oracle and/or its affiliates
Copyright (c) 2020, 2021, Oracle and/or its affiliates
Copyright (c) Facebook, Inc. and Microsoft Corporation
Copyright (c) 2012-2017 Georgia Institute of Technology
Copyright (c) 2012 The University of Wisconsin-La Crosse
Copyright (c) 2014-2015 The University of Houston System
Copyright (c) 2006-2017 Los Alamos National Security, LLC.
Copyright (c) 2017-2018 Amazon.com, Inc. or its affiliates
Copyright (c) NVIDIA Corporation and Microsoft Corporation
Copyright (c) 2013, 2014, 2015, the respective contributors
Copyright (c) 2019, 2020, 2022 Oracle and/or its affiliates
Copyright (c) 2008-2011 INADA Naoki <<EMAIL>>
Copyright (c) 2007 Lawrence Livermore National Security, LLC.
Copyright (c) 2014-2015 Hewlett-Packard Development Company, LP.
Copyright (c) 2003-2008, Jouni Malinen <<EMAIL>> and contributors
Copyright (c) 2003-2017 University of Illinois at Urbana-Champaign
Copyright (c) 2004-2008 The Regents of the University of California
Copyright (c) 2005-2008 ZIH, TU Dresden, Federal Republic of Germany
Copyright (c) 2011-2012 NEC Laboratories America (Koray Kavukcuoglu)
Copyright (c) 2008-2009 Institut National de Recherche en Informatique
Copyright (c) 1987, 1993, 1994 The Regents of the University of California
Copyright (c) 2013-2017 Research Organization for Information Science (RIST)
Copyright (c) 2014-2017 The Regents of the University of California (Regents)
Copyright (c) 2004-2010 High Performance Computing Center Stuttgart, University of Stuttgart
Copyright (c) 2001-2004 Idiap Research Institute (Ronan Collobert, Samy Bengio, Johnny Mariethoz)
Copyright (c) 2015 The University of Tennessee and The University of Tennessee Research Foundation
Copyright (c) 2004-2017 The University of Tennessee and The University of Tennessee Research Foundation
Copyright (c) 2006-2010 NEC Laboratories America (Ronan Collobert, Leon Bottou, Iain Melvin, Jason Weston)
Copyright (c) 1998-2005 Forschungszentrum Juelich, Juelich Supercomputing Centre, Federal Republic of Germany
Copyright (c) 2004-2010 The Trustees of Indiana University and Indiana University Research and Technology Corporation

MIT License

Copyright (c) Microsoft Corporation

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.


---------------------------------------------------------

---------------------------------------------------------

microsoft/wil f9284c19c9873664978b873b8858d7dfacc6af1e - MIT


Copyright (c) Microsoft
(c) Microsoft Corporation
Copyright 2017 Two Blue Cubes Ltd.
Copyright (c) Microsoft Corporation
Copyright (c) 2019 Two Blue Cubes Ltd.
Copyright (c) 2009-2014 by the contributors

    MIT License

    Copyright (c) Microsoft Corporation. All rights reserved.

    Permission is hereby granted, free of charge, to any person obtaining a copy
    of this software and associated documentation files (the "Software"), to deal
    in the Software without restriction, including without limitation the rights
    to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
    copies of the Software, and to permit persons to whom the Software is
    furnished to do so, subject to the following conditions:

    The above copyright notice and this permission notice shall be included in all
    copies or substantial portions of the Software.

    THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
    AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
    LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
    OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
    SOFTWARE


---------------------------------------------------------

---------------------------------------------------------

NAudio 2.0.1 - MIT


(c) Mark Heath

Copyright 2020 Mark Heath

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.



---------------------------------------------------------

---------------------------------------------------------

NAudio.Asio 2.0.0 - MIT



MIT License

Copyright (c) <year> <copyright holders>

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

---------------------------------------------------------

---------------------------------------------------------

NAudio.Core 2.0.0 - MIT


(c) Mark Heath

MIT License

Copyright (c) <year> <copyright holders>

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

---------------------------------------------------------

---------------------------------------------------------

NAudio.Midi 2.0.1 - MIT


(c) Mark Heath

MIT License

Copyright (c) <year> <copyright holders>

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

---------------------------------------------------------

---------------------------------------------------------

NAudio.Wasapi 2.0.0 - MIT


(c) Mark Heath

MIT License

Copyright (c) <year> <copyright holders>

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

---------------------------------------------------------

---------------------------------------------------------

NAudio.WinForms 2.0.1 - MIT


(c) Mark Heath

MIT License

Copyright (c) <year> <copyright holders>

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

---------------------------------------------------------

---------------------------------------------------------

NAudio.WinMM 2.0.1 - MIT


(c) Mark Heath
CopyrightChars DisposeBuffers CreateBuffers

MIT License

Copyright (c) <year> <copyright holders>

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

---------------------------------------------------------

---------------------------------------------------------

Newtonsoft.Json 13.0.2 - MIT


Copyright James Newton-King 2008
Copyright (c) 2007 James Newton-King
Copyright (c) James Newton-King 2008
Copyright James Newton-King 2008 Json.NET

The MIT License (MIT)

Copyright (c) 2007 James Newton-King

Permission is hereby granted, free of charge, to any person obtaining a copy of
this software and associated documentation files (the "Software"), to deal in
the Software without restriction, including without limitation the rights to
use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of
the Software, and to permit persons to whom the Software is furnished to do so,
subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS
FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.


---------------------------------------------------------

---------------------------------------------------------

nlohmann.json 3.10.4 - MIT


Copyright (c) 2009 Florian Loitsch.
Copyright (c) 2013-2019 Niels Lohmann <http://nlohmann.me>
Copyright (c) 2013-2019 Niels Lohmann (http://nlohmann.me).
Copyright (c) 2008-2009 Bjoern Hoehrmann <<EMAIL>> sa http://bjoern.hoehrmann.de/utf-8/decoder/dfa

MIT License

Copyright (c) <year> <copyright holders>

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

---------------------------------------------------------

---------------------------------------------------------

nlohmann.json 3.7.3 - MIT


Copyright (c) 2009 Florian Loitsch.
Copyright (c) 2013-2018 Niels Lohmann
copyright (c) 2013-2017 Niels Lohmann
Copyright (c) 2013-2019 Niels Lohmann <http://nlohmann.me>
Copyright (c) 2008-2009 Bjoern Hoehrmann <<EMAIL>> sa http://bjoern.hoehrmann.de/utf-8/decoder/dfa

MIT License

Copyright (c) <year> <copyright holders>

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

---------------------------------------------------------

---------------------------------------------------------

nlohmann/json 53c3eefa2cf790a7130fed3e13a3be35c2f2ace2 - MIT


(c) 2016
(c) Y Seomi
Copyright 2014
T68Oa (c) PiP6Le
Si4i (c) AzZGz3vae
Copyright 2015 Google Inc.
Copyright 2018 Google Inc.
Copyright Louis Dionne 2015
Copyright (c) 2015 Max Woolf
Copyright 2017 Roman Lebedev.
Copyright (c) 2009 Google Inc.
copyright' (c) 2013-2017 Niels
Copyright (c) 2016 Nicolas Seriot
Copyright (c) 2009 Florian Loitsch.
copyright (c) 2013-2017 Niels Lohmann
Copyright (c) 2013-2019 Niels Lohmann.
Copyright (c) 2015-2017 Niels Lohmann.
Copyright (c) 2016-2018 Viktor Kirilov
Copyright (c) 2016-2019 Viktor Kirilov
Copyright (c) 2018-2019 Bryan Gillespie
Copyright 2016 Ismael Jimenez Martinez.
Copyright (c) 2012, Erik Edlund <<EMAIL>>
Copyright (c) 2018 Vitaliy Manushkin <<EMAIL>>
Copyright (c) 2013-2019 Niels Lohmann (http://nlohmann.me)
Copyright (c) 2013-2019 Niels Lohmann <http://nlohmann.me>
Copyright (c) 2009 Florian Loitsch (http://florian.loitsch.com/)
Copyright (c) 2007 Free Software Foundation, Inc. <https://fsf.org/>
Copyright (c) 2008-2009 Bjorn Hoehrmann (http://bjoern.hoehrmann.de/) <<EMAIL>>
Copyright (c) 2008-2009 Bjoern Hoehrmann <<EMAIL>> sa http://bjoern.hoehrmann.de/utf-8/decoder/dfa

MIT License 

Copyright (c) 2013-2019 Niels Lohmann

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.


---------------------------------------------------------

---------------------------------------------------------

nlohmann/json db78ac1d7716f56fc9f1b030b715f872f93964e4 - MIT


(c) 2016
Copyright 2015 Google Inc.
Copyright 2018 Google Inc.
Copyright Louis Dionne 2015
Copyright 2017 Roman Lebedev.
Copyright (c) 2009 Google Inc.
Copyright (c) 2009 Florian Loitsch.
Copyright (c) 2015-2017 Niels Lohmann.
Copyright (c) 2016-2019 Viktor Kirilov
Copyright (c) 2013 - 2020 Niels Lohmann
Copyright 2016 Ismael Jimenez Martinez.
Copyright (c) 2012, Erik Edlund <<EMAIL>>
Copyright (c) 2018 Vitaliy Manushkin <<EMAIL>>
Copyright (c) 2013-2019 Niels Lohmann (http://nlohmann.me)
Copyright (c) 2013-2019 Niels Lohmann <http://nlohmann.me>
Copyright (c) 2009 Florian Loitsch (http://florian.loitsch.com/)
Copyright (c) 2009 Florian Loitsch (https://florian.loitsch.com/)
Copyright (c) 2007 Free Software Foundation, Inc. <https://fsf.org/>
Copyright (c) 2008-2009 Bjorn Hoehrmann (http://bjoern.hoehrmann.de/) <<EMAIL>>
Copyright (c) 2008-2009 Bjorn Hoehrmann (https://bjoern.hoehrmann.de/) <<EMAIL>>
Copyright (c) 2008-2009 Bjoern Hoehrmann <<EMAIL>> sa http://bjoern.hoehrmann.de/utf-8/decoder/dfa

MIT License 

Copyright (c) 2013-2020 Niels Lohmann

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.


---------------------------------------------------------

---------------------------------------------------------

okdshin/picosha2 b699e6c900be6e00152db5a3d123c1db42ea13d0 - MIT


Copyright (c) 2017

MIT License

Copyright (c) 2017 okdshin

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.


---------------------------------------------------------

---------------------------------------------------------

org.bouncycastle/bcprov-jdk15on 1.70 - MIT


Copyright (c) 2000-2021 The Legion of the Bouncy Castle Inc. (https://www.bouncycastle.org)

MIT License

Copyright (c) <year> <copyright holders>

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

---------------------------------------------------------

---------------------------------------------------------

org.checkerframework/checker-qual 3.12.0 - MIT


Copyright 2004-present by the Checker Framework

MIT License

Copyright (c) <year> <copyright holders>

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

---------------------------------------------------------

---------------------------------------------------------

org.slf4j/slf4j-api 1.7.36 - MIT



MIT License

Copyright (c) <year> <copyright holders>

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

---------------------------------------------------------

---------------------------------------------------------

org.slf4j/slf4j-simple 1.7.36 - MIT



MIT License

Copyright (c) <year> <copyright holders>

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

---------------------------------------------------------

---------------------------------------------------------

System.Security.AccessControl 4.7.0 - MIT


(c) Microsoft Corporation.
Copyright (c) .NET Foundation.
Copyright (c) 2011, Google Inc.
(c) 1997-2005 Sean Eron Anderson.
Copyright (c) 2007 James Newton-King
Copyright (c) 1991-2017 Unicode, Inc.
Copyright (c) 2013-2017, Alfred Klomp
Copyright (c) 2015-2017, Wojciech Mula
Copyright (c) 2005-2007, Nick Galbreath
Portions (c) International Organization
Copyright (c) 2015 The Chromium Authors.
Copyright (c) 2004-2006 Intel Corporation
Copyright (c) 2016-2017, Matthieu Darbois
Copyright (c) .NET Foundation Contributors
Copyright (c) .NET Foundation and Contributors
Copyright (c) 2011 Novell, Inc (http://www.novell.com)
Copyright (c) 1995-2017 Jean-loup Gailly and Mark Adler
Copyright (c) 2009, 2010, 2013-2016 by the Brotli Authors.
Copyright (c) YEAR W3C(r) (MIT, ERCIM, Keio, Beihang). Disclaimers THIS WORK IS PROVIDED AS

The MIT License (MIT)

Copyright (c) .NET Foundation and Contributors

All rights reserved.

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.


---------------------------------------------------------

---------------------------------------------------------

System.Security.Principal.Windows 4.7.0 - MIT


(c) Microsoft Corporation.
Copyright (c) .NET Foundation.
Copyright (c) 2011, Google Inc.
(c) 1997-2005 Sean Eron Anderson.
Copyright (c) 2007 James Newton-King
Copyright (c) 1991-2017 Unicode, Inc.
Copyright (c) 2013-2017, Alfred Klomp
Copyright (c) 2015-2017, Wojciech Mula
Copyright (c) 2005-2007, Nick Galbreath
Portions (c) International Organization
Copyright (c) 2015 The Chromium Authors.
Copyright (c) 2004-2006 Intel Corporation
Copyright (c) 2016-2017, Matthieu Darbois
Copyright (c) .NET Foundation Contributors
Copyright (c) .NET Foundation and Contributors
Copyright (c) 2011 Novell, Inc (http://www.novell.com)
Copyright (c) 1995-2017 Jean-loup Gailly and Mark Adler
Copyright (c) 2009, 2010, 2013-2016 by the Brotli Authors.
Copyright (c) YEAR W3C(r) (MIT, ERCIM, Keio, Beihang). Disclaimers THIS WORK IS PROVIDED AS

The MIT License (MIT)

Copyright (c) .NET Foundation and Contributors

All rights reserved.

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.


---------------------------------------------------------

---------------------------------------------------------

tqdm 4.64.1 - MIT


Copyright (c) 2013 noamraph
(c) Noam Yorav-Raphael, original author
(c) Casper da Costa-Luis casperdcl (https://github.com/casperdcl)

MIT License

Copyright (c) <year> <copyright holders>

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

---------------------------------------------------------

---------------------------------------------------------

zipp 3.11.0 - MIT


Copyright Jason R. Coombs

MIT License

Copyright (c) <year> <copyright holders>

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

---------------------------------------------------------

---------------------------------------------------------

microsoft/openssl 92f02f5a311d536b2f7894e46d3b5039ad3568c8 - OpenSSL


Copyright (c) 2007
Copyright 2005 Nokia.
Copyright Eric A. Young.
Copyright 2011 Google Inc.
Copyright Copyright (c) 2014
Copyright (c) by P.J. Plauger
Copyright Patrick Powell 1995
Copyright (c) 1997, Eric Young
Copyright (c) 2011, RTFM, Inc.
Copyright (c) 2014, Google Inc.
Copyright Svend Olaf Mikkelsen.
(c) Copyright 1999 Bodo Moeller.
Copyright (c) 2002 Markus Friedl
Copyright (c) 2002 Theo de Raadt
Copyright (c) 2006 Cryptocom LTD
Copyright 2014 Intel Corporation
Copyright 2016 VMS Software, Inc.
Copyright (c) 1995-1997 Eric Young
Copyright 2016 The OpenSSL Project
Copyright 2017 The OpenSSL Project
Copyright 2018 The OpenSSL Project
(c) Copyright Microsoft Corp. 1993.
Copyright 2000 Broadcom Corporation
Michael Elkins <<EMAIL>> 1998
Copyright (c) Microsoft Corporation.
Copyright 2014 Microsoft Corporation
Copyright 2015 Microsoft Corporation
Luke Mewburn <<EMAIL>> 1999
9Copyright (c) Microsoft Corporation.
Brandon Long <<EMAIL>> 1996
Copyright (c) 2005-2006 Cryptocom LTD
Copyright (c) 2012, Intel Corporation
Copyright 2002 Sun Microsystems, Inc.
Andrew Tridgell <<EMAIL>> 1998
Copyright (c) 1999 The OpenSSL Project.
Copyright (c) 2000 The OpenSSL Project.
Copyright (c) 2001 The OpenSSL Project.
Copyright (c) 2002 The OpenSSL Project.
Copyright (c) 2002 The OpenTSA Project.
Copyright (c) 2003 The OpenSSL Project.
Copyright (c) 2004 The OpenSSL Project.
Copyright (c) 2005 The OpenSSL Project.
Copyright (c) 2006 The OpenSSL Project.
Copyright (c) 2007 The OpenSSL Project.
Copyright (c) 2008 The OpenSSL Project.
Copyright (c) 2009 The OpenSSL Project.
Copyright (c) 2010 The OpenSSL Project.
Copyright (c) 2011 The OpenSSL Project.
Copyright (c) 2012 The OpenSSL Project.
Copyright (c) 2013 The OpenSSL Project.
Copyright (c) 2014 The OpenSSL Project.
Copyright (c) 2016 The OpenSSL Project.
Copyright 2000-2017 The OpenSSL Project
Copyright 2017-2018 The OpenSSL Project
Copyright 2017-2019 The OpenSSL Project
Copyright 20xx-20yy The OpenSSL Project
Thomas Roessler <<EMAIL>> 1998
Copyright ?1998-2005 The OpenSSL Project.
Copyright (c) 1998-now The OpenSSL Project
Copyright@2001 Baltimore Technologies Ltd.
Copyright (c) 2006, Network Resonance, Inc.
Patrick Powell <<EMAIL>> (1995)
Copyright (c) 1986 by Sun Microsystems, Inc.
Copyright (c) 1998-1999 The OpenSSL Project.
Copyright (c) 1998-2000 The OpenSSL Project.
Copyright (c) 1998-2001 The OpenSSL Project.
Copyright (c) 1998-2002 The OpenSSL Project.
Copyright (c) 1998-2003 The OpenSSL Project.
Copyright (c) 1998-2005 The OpenSSL Project.
Copyright (c) 1998-2006 The OpenSSL Project.
Copyright (c) 1998-2007 The OpenSSL Project.
Copyright (c) 1998-2009 The OpenSSL Project.
Copyright (c) 1998-2010 The OpenSSL Project.
Copyright (c) 1998-2015 The OpenSSL Project.
Copyright (c) 1998-2018 The OpenSSL Project.
Copyright (c) 1998-2019 The OpenSSL Project.
Copyright (c) 1999-2001 The OpenSSL Project.
Copyright (c) 1999-2002 The OpenSSL Project.
Copyright (c) 1999-2003 The OpenSSL Project.
Copyright (c) 1999-2004 The OpenSSL Project.
Copyright (c) 1999-2005 The OpenSSL Project.
Copyright (c) 1999-2006 The OpenSSL Project.
Copyright (c) 1999-2007 The OpenSSL Project.
Copyright (c) 1999-2008 The OpenSSL Project.
Copyright (c) 1999-2009 The OpenSSL Project.
Copyright (c) 1999-2010 The OpenSSL Project.
Copyright (c) 1999-2011 The OpenSSL Project.
Copyright (c) 1999-2013 The OpenSSL Project.
Copyright (c) 1999-2014 The OpenSSL Project.
Copyright (c) 1999-2015 The OpenSSL Project.
Copyright (c) 1999-2016 The OpenSSL Project.
Copyright (c) 1999-2017 The OpenSSL Project.
Copyright (c) 1999-2018 The OpenSSL Project.
Copyright (c) 1999-2019 The OpenSSL Project.
Copyright (c) 2000,2005 The OpenSSL Project.
Copyright (c) 2000-2001 The OpenSSL Project.
Copyright (c) 2000-2002 The OpenSSL Project.
Copyright (c) 2000-2003 The OpenSSL Project.
Copyright (c) 2000-2004 The OpenSSL Project.
Copyright (c) 2000-2005 The OpenSSL Project.
Copyright (c) 2000-2018 The OpenSSL Project.
Copyright (c) 2001-2002 The OpenSSL Project.
Copyright (c) 2001-2004 The OpenSSL Project.
Copyright (c) 2001-2005 The OpenSSL Project.
Copyright (c) 2001-2008 The OpenSSL Project.
Copyright (c) 2001-2018 The OpenSSL Project.
Copyright (c) 2001-2019 The OpenSSL Project.
Copyright (c) 2002-2006 The OpenSSL Project.
Copyright (c) 2004-2011 The OpenSSL Project.
Copyright (c) 2005-2018 The OpenSSL Project.
Copyright (c) 2006,2007 The OpenSSL Project.
Copyright (c) 2006-2018 The OpenSSL Project.
Copyright (c) 2008-2018 The OpenSSL Project.
Copyright (c) 2011-2013 The OpenSSL Project.
Copyright (c) 2012-2018 The OpenSSL Project.
Copyright (c) 1999-$year The OpenSSL Project.
Copyright (c) 2001-$year The OpenSSL Project.
Copyright (c) 2002 Bob Beck <<EMAIL>>
Ralf S. Engelschall <<EMAIL>> 1999
Copyright 1998-2000 nCipher Corporation Limited.
Copyright ?1995-1998 Eric A. Young, Tim J. Hudson.
Copyright (c) 2008 Andy Polyakov <<EMAIL>>
Copyright (c) 1995-1998 Eric A. Young, Tim J. Hudson.
Copyright (c) 1995-1997 Eric Young (<EMAIL>)
Copyright (c) 1995-1998 Eric Young (eay cryptsoft.com)
Copyright (c) 1995-1998 Eric Young (<EMAIL>)
(c) COPYRIGHT International Business Machines Corp. 2001
Copyright (c) 2004, Richard Levitte <<EMAIL>>
Copyright (c) 2005 Hewlett-Packard Development Company, L.P.
Copyright 2006 NTT (Nippon Telegraph and Telephone Corporation)
Copyright (c) 2004 Kungliga Tekniska Hogskolan (Royal Institute of Technology, Stockholm, Sweden).


  LICENSE ISSUES
  ==============

  The OpenSSL toolkit stays under a double license, i.e. both the conditions of
  the OpenSSL License and the original SSLeay license apply to the toolkit.
  See below for the actual license texts. Actually both licenses are BSD-style
  Open Source licenses. In case of any license issues related to OpenSSL
  <NAME_EMAIL>.

  OpenSSL License
  ---------------

/* ====================================================================
 * Copyright (c) 1998-2018 The OpenSSL Project.  All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer. 
 *
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in
 *    the documentation and/or other materials provided with the
 *    distribution.
 *
 * 3. All advertising materials mentioning features or use of this
 *    software must display the following acknowledgment:
 *    "This product includes software developed by the OpenSSL Project
 *    for use in the OpenSSL Toolkit. (http://www.openssl.org/)"
 *
 * 4. The names "OpenSSL Toolkit" and "OpenSSL Project" must not be used to
 *    endorse or promote products derived from this software without
 *    prior written permission. For written permission, please contact
 *    <EMAIL>.
 *
 * 5. Products derived from this software may not be called "OpenSSL"
 *    nor may "OpenSSL" appear in their names without prior written
 *    permission of the OpenSSL Project.
 *
 * 6. Redistributions of any form whatsoever must retain the following
 *    acknowledgment:
 *    "This product includes software developed by the OpenSSL Project
 *    for use in the OpenSSL Toolkit (http://www.openssl.org/)"
 *
 * THIS SOFTWARE IS PROVIDED BY THE OpenSSL PROJECT ``AS IS'' AND ANY
 * EXPRESSED OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE OpenSSL PROJECT OR
 * ITS CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 * SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT
 * NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
 * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED
 * OF THE POSSIBILITY OF SUCH DAMAGE.
 * ====================================================================
 *
 * This product includes cryptographic software written by Eric Young
 * (<EMAIL>).  This product includes software written by Tim
 * Hudson (<EMAIL>).
 *
 */

 Original SSLeay License
 -----------------------

/* Copyright (C) 1995-1998 Eric Young (<EMAIL>)
 * All rights reserved.
 *
 * This package is an SSL implementation written
 * by Eric Young (<EMAIL>).
 * The implementation was written so as to conform with Netscapes SSL.
 * 
 * This library is free for commercial and non-commercial use as long as
 * the following conditions are aheared to.  The following conditions
 * apply to all code found in this distribution, be it the RC4, RSA,
 * lhash, DES, etc., code; not just the SSL code.  The SSL documentation
 * included with this distribution is covered by the same copyright terms
 * except that the holder is Tim Hudson (<EMAIL>).
 * 
 * Copyright remains Eric Young's, and as such any Copyright notices in
 * the code are not to be removed.
 * If this package is used in a product, Eric Young should be given attribution
 * as the author of the parts of the library used.
 * This can be in the form of a textual message at program startup or
 * in documentation (online or textual) provided with the package.
 * 
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 1. Redistributions of source code must retain the copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 * 3. All advertising materials mentioning features or use of this software
 *    must display the following acknowledgement:
 *    "This product includes cryptographic software written by
 *     Eric Young (<EMAIL>)"
 *    The word 'cryptographic' can be left out if the rouines from the library
 *    being used are not cryptographic related :-).
 * 4. If you include any Windows specific code (or a derivative thereof) from 
 *    the apps directory (application code) you must include an acknowledgement:
 *    "This product includes software written by Tim Hudson (<EMAIL>)"
 * 
 * THIS SOFTWARE IS PROVIDED BY ERIC YOUNG ``AS IS'' AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED.  IN NO EVENT SHALL THE AUTHOR OR CONTRIBUTORS BE LIABLE
 * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
 * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
 * OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
 * OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 * SUCH DAMAGE.
 * 
 * The licence and distribution terms for any publically available version or
 * derivative of this code cannot be changed.  i.e. this code cannot simply be
 * copied and put under another distribution licence
 * [including the GNU Public Licence.]
 */



---------------------------------------------------------

---------------------------------------------------------

openssl/openssl 29708a562a1887a91de0fa6ca668c71871accde9 - OpenSSL


(c) 2005 WISeKey SA1
Copyright 2005 Nokia
Copyright 2011 Google Inc.
Copyright 2017 Ribose Inc.
(c) 2013 ATT Wi-Fi Services
Copyright 2017 BaishanCloud
Copyright 2013 M. J. Dominus
Copyright Patrick Powell 1995
Copyright (c) 2011, RTFM, Inc.
Copyright 2013 Mark Jason Dominus
Copyright 2016 VMS Software, Inc.
Copyright (c) 2004, EdelKey Project
Copyright (c) 2015 CloudFlare, Inc.
Copyright (c) 2015, CloudFlare, Inc.
Copyright (c) 2005 WISeKey SA1 InterU
Copyright (c) 2012, Intel Corporation
Copyright (c) 2014, Intel Corporation
Copyright (c) 2002 The OpenTSA Project
Copyright 1998-$YEAR The OpenSSL Authors
Copyright 2004-2014, Akamai Technologies
holder is Tim Hudson (<EMAIL>)
Copyright 2014 Cryptography Research, Inc.
Copyright 2015 Cryptography Research, Inc.
Copyright 2016 Cryptography Research, Inc.
Copyright 2016 The OpenSSL Project Authors
Copyright 2017 The OpenSSL Project Authors
Copyright 2018 The OpenSSL Project Authors
Copyright 2019 The OpenSSL Project Authors
Copyright 2020 The OpenSSL Project Authors
Copyright (c) 1998-2019 The OpenSSL Project
Copyright (c) 1998-2022 The OpenSSL Project
Copyright (c) 2006, Network Resonance, Inc.
Copyright (c) 2012-2014 Daniel J. Bernstein
copyrighted by the Free Software Foundation
Copyright (c) 2012-2016 Jean-Philippe Aumasson
Copyright 1995-2016 The OpenSSL Project Authors
Copyright 1995-2017 The OpenSSL Project Authors
Copyright 1995-2018 The OpenSSL Project Authors
Copyright 1995-2019 The OpenSSL Project Authors
Copyright 1995-2020 The OpenSSL Project Authors
Copyright 1995-2021 The OpenSSL Project Authors
Copyright 1995-2022 The OpenSSL Project Authors
Copyright 1998-2001 The OpenSSL Project Authors
Copyright 1998-2016 The OpenSSL Project Authors
Copyright 1998-2017 The OpenSSL Project Authors
Copyright 1998-2018 The OpenSSL Project Authors
Copyright 1998-2019 The OpenSSL Project Authors
Copyright 1998-2020 The OpenSSL Project Authors
Copyright 1998-2021 The OpenSSL Project Authors
Copyright 1998-2022 The OpenSSL Project Authors
Copyright 1999-2016 The OpenSSL Project Authors
Copyright 1999-2017 The OpenSSL Project Authors
Copyright 1999-2018 The OpenSSL Project Authors
Copyright 1999-2019 The OpenSSL Project Authors
Copyright 1999-2020 The OpenSSL Project Authors
Copyright 1999-2021 The OpenSSL Project Authors
Copyright 1999-2022 The OpenSSL Project Authors
Copyright 2000-2016 The OpenSSL Project Authors
Copyright 2000-2017 The OpenSSL Project Authors
Copyright 2000-2018 The OpenSSL Project Authors
Copyright 2000-2019 The OpenSSL Project Authors
Copyright 2000-2020 The OpenSSL Project Authors
Copyright 2000-2021 The OpenSSL Project Authors
Copyright 2000-2022 The OpenSSL Project Authors
Copyright 2001-2016 The OpenSSL Project Authors
Copyright 2001-2017 The OpenSSL Project Authors
Copyright 2001-2018 The OpenSSL Project Authors
Copyright 2001-2019 The OpenSSL Project Authors
Copyright 2001-2020 The OpenSSL Project Authors
Copyright 2001-2021 The OpenSSL Project Authors
Copyright 2001-2022 The OpenSSL Project Authors
Copyright 2002-2016 The OpenSSL Project Authors
Copyright 2002-2017 The OpenSSL Project Authors
Copyright 2002-2018 The OpenSSL Project Authors
Copyright 2002-2019 The OpenSSL Project Authors
Copyright 2002-2020 The OpenSSL Project Authors
Copyright 2002-2021 The OpenSSL Project Authors
Copyright 2002-2022 The OpenSSL Project Authors
Copyright 2003-2016 The OpenSSL Project Authors
Copyright 2003-2017 The OpenSSL Project Authors
Copyright 2003-2018 The OpenSSL Project Authors
Copyright 2003-2020 The OpenSSL Project Authors
Copyright 2003-2021 The OpenSSL Project Authors
Copyright 2004-2016 The OpenSSL Project Authors
Copyright 2004-2017 The OpenSSL Project Authors
Copyright 2004-2018 The OpenSSL Project Authors
Copyright 2004-2019 The OpenSSL Project Authors
Copyright 2004-2020 The OpenSSL Project Authors
Copyright 2004-2021 The OpenSSL Project Authors
Copyright 2005-2016 The OpenSSL Project Authors
Copyright 2005-2017 The OpenSSL Project Authors
Copyright 2005-2018 The OpenSSL Project Authors
Copyright 2005-2019 The OpenSSL Project Authors
Copyright 2005-2020 The OpenSSL Project Authors
Copyright 2005-2021 The OpenSSL Project Authors
Copyright 2005-2022 The OpenSSL Project Authors
Copyright 2006-2016 The OpenSSL Project Authors
Copyright 2006-2017 The OpenSSL Project Authors
Copyright 2006-2018 The OpenSSL Project Authors
Copyright 2006-2019 The OpenSSL Project Authors
Copyright 2006-2020 The OpenSSL Project Authors
Copyright 2006-2021 The OpenSSL Project Authors
Copyright 2006-2022 The OpenSSL Project Authors
Copyright 2007-2016 The OpenSSL Project Authors
Copyright 2007-2018 The OpenSSL Project Authors
Copyright 2007-2020 The OpenSSL Project Authors
Copyright 2007-2021 The OpenSSL Project Authors
Copyright 2008-2016 The OpenSSL Project Authors
Copyright 2008-2018 The OpenSSL Project Authors
Copyright 2008-2019 The OpenSSL Project Authors
Copyright 2008-2020 The OpenSSL Project Authors
Copyright 2008-2021 The OpenSSL Project Authors
Copyright 2008-2022 The OpenSSL Project Authors
Copyright 2009-2016 The OpenSSL Project Authors
Copyright 2009-2018 The OpenSSL Project Authors
Copyright 2009-2020 The OpenSSL Project Authors
Copyright 2009-2021 The OpenSSL Project Authors
Copyright 2009-2022 The OpenSSL Project Authors
Copyright 2010-2016 The OpenSSL Project Authors
Copyright 2010-2020 The OpenSSL Project Authors
Copyright 2010-2021 The OpenSSL Project Authors
Copyright 2010-2022 The OpenSSL Project Authors
Copyright 2011-2016 The OpenSSL Project Authors
Copyright 2011-2017 The OpenSSL Project Authors
Copyright 2011-2018 The OpenSSL Project Authors
Copyright 2011-2019 The OpenSSL Project Authors
Copyright 2011-2020 The OpenSSL Project Authors
Copyright 2011-2021 The OpenSSL Project Authors
Copyright 2011-2022 The OpenSSL Project Authors
Copyright 2012, Samuel Neves <<EMAIL>>
Copyright 2012-2016 The OpenSSL Project Authors
Copyright 2012-2017 The OpenSSL Project Authors
Copyright 2012-2019 The OpenSSL Project Authors
Copyright 2012-2020 The OpenSSL Project Authors
Copyright 2012-2021 The OpenSSL Project Authors
Copyright 2012-2022 The OpenSSL Project Authors
Copyright 2013-2016 The OpenSSL Project Authors
Copyright 2013-2017 The OpenSSL Project Authors
Copyright 2013-2018 The OpenSSL Project Authors
Copyright 2013-2019 The OpenSSL Project Authors
Copyright 2013-2020 The OpenSSL Project Authors
Copyright 2013-2021 The OpenSSL Project Authors
Copyright 2013-2022 The OpenSSL Project Authors
Copyright 2014-2016 Cryptography Research, Inc.
Copyright 2014-2016 The OpenSSL Project Authors
Copyright 2014-2017 The OpenSSL Project Authors
Copyright 2014-2018 The OpenSSL Project Authors
Copyright 2014-2019 The OpenSSL Project Authors
Copyright 2014-2020 The OpenSSL Project Authors
Copyright 2014-2021 The OpenSSL Project Authors
Copyright 2014-2022 The OpenSSL Project Authors
Copyright 2015-2016 Cryptography Research, Inc.
Copyright 2015-2016 The OpenSSL Project Authors
Copyright 2015-2017 The OpenSSL Project Authors
Copyright 2015-2018 The OpenSSL Project Authors
Copyright 2015-2019 The OpenSSL Project Authors
Copyright 2015-2020 The OpenSSL Project Authors
Copyright 2015-2021 The OpenSSL Project Authors
Copyright 2015-2022 The OpenSSL Project Authors
Copyright 2016-2016 The OpenSSL Project Authors
Copyright 2016-2017 The OpenSSL Project Authors
Copyright 2016-2018 The OpenSSL Project Authors
Copyright 2016-2019 The OpenSSL Project Authors
Copyright 2016-2020 The OpenSSL Project Authors
Copyright 2016-2021 The OpenSSL Project Authors
Copyright 2016-2022 The OpenSSL Project Authors
Copyright 2017-2018 The OpenSSL Project Authors
Copyright 2017-2019 The OpenSSL Project Authors
Copyright 2017-2020 The OpenSSL Project Authors
Copyright 2017-2021 The OpenSSL Project Authors
Copyright 2017-2022 The OpenSSL Project Authors
Copyright 2018-2019 The OpenSSL Project Authors
Copyright 2018-2020 The OpenSSL Project Authors
Copyright 2018-2021 The OpenSSL Project Authors
Copyright 2019-2020 The OpenSSL Project Authors
Copyright 2019-2021 The OpenSSL Project Authors
Copyright 20xx-20yy The OpenSSL Project Authors
Copyright (c) 2002, Oracle and/or its affiliates
Copyright (c) 2017, Oracle and/or its affiliates
Copyright (c) 2018, Oracle and/or its affiliates
Copyright 1995-$YEAR The OpenSSL Project Authors
Copyright 1998-$YEAR The OpenSSL Project Authors
Copyright 1999-$YEAR The OpenSSL Project Authors
Copyright 2000-$YEAR The OpenSSL Project Authors
Copyright 2017 Ribose Inc. (https://www.ribose.com)
Copyright (c) 1995-1998 Eric A. Young, Tim J. Hudson
Copyright (c) 2008 Andy Polyakov <<EMAIL>>
Copyright (c) 1995-1998 Eric Young (<EMAIL>)
Copyright (c) 1989, 1991 Free Software Foundation, Inc.
Copyright (c) 2017 National Security Research Institute
Copyright (c) 2004, Richard Levitte <<EMAIL>>
Copyright (c) 2013-2014 Timo Teras <<EMAIL>>
Copyright (c) 2007 KISA(Korea Information Security Agency)
Copyright (c) 2004, 2018, Richard Levitte <<EMAIL>>
Copyright (c) 2016 Viktor Dukhovni <<EMAIL>>
Copyright 2006 NTT (Nippon Telegraph and Telephone Corporation)
Copyright (c) 2005 WISeKey SA1 Internat(onal1)0 WISeKey CertifyID Advanced G1 CA0
Copyright (c) 2005 WISeKey SA1 International1 0 WISeKey CertifyID Advanced G1 CA0
Copyright (c) 2004 Kungliga Tekniska Hogskolan (Royal Institute of Technology, Stockholm, Sweden)


  LICENSE ISSUES
  ==============

  The OpenSSL toolkit stays under a double license, i.e. both the conditions of
  the OpenSSL License and the original SSLeay license apply to the toolkit.
  See below for the actual license texts.

  OpenSSL License
  ---------------

/* ====================================================================
 * Copyright (c) 1998-2019 The OpenSSL Project.  All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in
 *    the documentation and/or other materials provided with the
 *    distribution.
 *
 * 3. All advertising materials mentioning features or use of this
 *    software must display the following acknowledgment:
 *    "This product includes software developed by the OpenSSL Project
 *    for use in the OpenSSL Toolkit. (http://www.openssl.org/)"
 *
 * 4. The names "OpenSSL Toolkit" and "OpenSSL Project" must not be used to
 *    endorse or promote products derived from this software without
 *    prior written permission. For written permission, please contact
 *    <EMAIL>.
 *
 * 5. Products derived from this software may not be called "OpenSSL"
 *    nor may "OpenSSL" appear in their names without prior written
 *    permission of the OpenSSL Project.
 *
 * 6. Redistributions of any form whatsoever must retain the following
 *    acknowledgment:
 *    "This product includes software developed by the OpenSSL Project
 *    for use in the OpenSSL Toolkit (http://www.openssl.org/)"
 *
 * THIS SOFTWARE IS PROVIDED BY THE OpenSSL PROJECT ``AS IS'' AND ANY
 * EXPRESSED OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE OpenSSL PROJECT OR
 * ITS CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 * SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT
 * NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
 * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED
 * OF THE POSSIBILITY OF SUCH DAMAGE.
 * ====================================================================
 *
 * This product includes cryptographic software written by Eric Young
 * (<EMAIL>).  This product includes software written by Tim
 * Hudson (<EMAIL>).
 *
 */

 Original SSLeay License
 -----------------------

/* Copyright (C) 1995-1998 Eric Young (<EMAIL>)
 * All rights reserved.
 *
 * This package is an SSL implementation written
 * by Eric Young (<EMAIL>).
 * The implementation was written so as to conform with Netscapes SSL.
 *
 * This library is free for commercial and non-commercial use as long as
 * the following conditions are aheared to.  The following conditions
 * apply to all code found in this distribution, be it the RC4, RSA,
 * lhash, DES, etc., code; not just the SSL code.  The SSL documentation
 * included with this distribution is covered by the same copyright terms
 * except that the holder is Tim Hudson (<EMAIL>).
 *
 * Copyright remains Eric Young's, and as such any Copyright notices in
 * the code are not to be removed.
 * If this package is used in a product, Eric Young should be given attribution
 * as the author of the parts of the library used.
 * This can be in the form of a textual message at program startup or
 * in documentation (online or textual) provided with the package.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 1. Redistributions of source code must retain the copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 * 3. All advertising materials mentioning features or use of this software
 *    must display the following acknowledgement:
 *    "This product includes cryptographic software written by
 *     Eric Young (<EMAIL>)"
 *    The word 'cryptographic' can be left out if the rouines from the library
 *    being used are not cryptographic related :-).
 * 4. If you include any Windows specific code (or a derivative thereof) from
 *    the apps directory (application code) you must include an acknowledgement:
 *    "This product includes software written by Tim Hudson (<EMAIL>)"
 *
 * THIS SOFTWARE IS PROVIDED BY ERIC YOUNG ``AS IS'' AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED.  IN NO EVENT SHALL THE AUTHOR OR CONTRIBUTORS BE LIABLE
 * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
 * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
 * OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
 * OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 * SUCH DAMAGE.
 *
 * The licence and distribution terms for any publically available version or
 * derivative of this code cannot be changed.  i.e. this code cannot simply be
 * copied and put under another distribution licence
 * [including the GNU Public Licence.]
 */



---------------------------------------------------------

---------------------------------------------------------

openssl/openssl d82e959e621a3d597f1e0d50ff8c2d8b96915fd7 - OpenSSL


Copyright 2005 Nokia
Copyright 2011 Google Inc.
Copyright 2017 Ribose Inc.
(c) 2013 ATT Wi-Fi Services
Copyright 2017 BaishanCloud
Copyright 2013 M. J. Dominus
Copyright Patrick Powell 1995
Copyright (c) 2011, RTFM, Inc.
Copyright 2013 Mark Jason Dominus
Copyright 2016 VMS Software, Inc.
Copyright (c) 2004, EdelKey Project
Copyright (c) 2015 CloudFlare, Inc.
Copyright (c) 2015, CloudFlare, Inc.
Copyright (c) 2005 WISeKey SA1 InterU
Copyright (c) 2012, Intel Corporation
Copyright (c) 2014, Intel Corporation
Copyright (c) 2002 The OpenTSA Project
Copyright 1998-$YEAR The OpenSSL Authors
Copyright 2004-2014, Akamai Technologies
holder is Tim Hudson (<EMAIL>)
Copyright 2014 Cryptography Research, Inc.
Copyright 2015 Cryptography Research, Inc.
Copyright 2016 Cryptography Research, Inc.
Copyright 2016 The OpenSSL Project Authors
Copyright 2017 The OpenSSL Project Authors
Copyright 2018 The OpenSSL Project Authors
Copyright 2019 The OpenSSL Project Authors
Copyright 2020 The OpenSSL Project Authors
Copyright (c) 1998-2019 The OpenSSL Project
Copyright (c) 1998-2021 The OpenSSL Project
Copyright (c) 2006, Network Resonance, Inc.
Copyright (c) 2012-2014 Daniel J. Bernstein
copyrighted by the Free Software Foundation
Copyright (c) 2012-2016 Jean-Philippe Aumasson
Copyright 1995-2016 The OpenSSL Project Authors
Copyright 1995-2017 The OpenSSL Project Authors
Copyright 1995-2018 The OpenSSL Project Authors
Copyright 1995-2019 The OpenSSL Project Authors
Copyright 1995-2020 The OpenSSL Project Authors
Copyright 1995-2021 The OpenSSL Project Authors
Copyright 1995-2022 The OpenSSL Project Authors
Copyright 1998-2001 The OpenSSL Project Authors
Copyright 1998-2016 The OpenSSL Project Authors
Copyright 1998-2017 The OpenSSL Project Authors
Copyright 1998-2018 The OpenSSL Project Authors
Copyright 1998-2019 The OpenSSL Project Authors
Copyright 1998-2020 The OpenSSL Project Authors
Copyright 1998-2021 The OpenSSL Project Authors
Copyright 1998-2022 The OpenSSL Project Authors
Copyright 1999-2016 The OpenSSL Project Authors
Copyright 1999-2017 The OpenSSL Project Authors
Copyright 1999-2018 The OpenSSL Project Authors
Copyright 1999-2019 The OpenSSL Project Authors
Copyright 1999-2020 The OpenSSL Project Authors
Copyright 1999-2021 The OpenSSL Project Authors
Copyright 1999-2022 The OpenSSL Project Authors
Copyright 2000-2016 The OpenSSL Project Authors
Copyright 2000-2017 The OpenSSL Project Authors
Copyright 2000-2018 The OpenSSL Project Authors
Copyright 2000-2019 The OpenSSL Project Authors
Copyright 2000-2020 The OpenSSL Project Authors
Copyright 2000-2021 The OpenSSL Project Authors
Copyright 2000-2022 The OpenSSL Project Authors
Copyright 2001-2016 The OpenSSL Project Authors
Copyright 2001-2017 The OpenSSL Project Authors
Copyright 2001-2018 The OpenSSL Project Authors
Copyright 2001-2019 The OpenSSL Project Authors
Copyright 2001-2020 The OpenSSL Project Authors
Copyright 2001-2021 The OpenSSL Project Authors
Copyright 2001-2022 The OpenSSL Project Authors
Copyright 2002-2016 The OpenSSL Project Authors
Copyright 2002-2017 The OpenSSL Project Authors
Copyright 2002-2018 The OpenSSL Project Authors
Copyright 2002-2019 The OpenSSL Project Authors
Copyright 2002-2020 The OpenSSL Project Authors
Copyright 2002-2021 The OpenSSL Project Authors
Copyright 2002-2022 The OpenSSL Project Authors
Copyright 2003-2016 The OpenSSL Project Authors
Copyright 2003-2017 The OpenSSL Project Authors
Copyright 2003-2018 The OpenSSL Project Authors
Copyright 2003-2020 The OpenSSL Project Authors
Copyright 2003-2021 The OpenSSL Project Authors
Copyright 2004-2016 The OpenSSL Project Authors
Copyright 2004-2017 The OpenSSL Project Authors
Copyright 2004-2018 The OpenSSL Project Authors
Copyright 2004-2019 The OpenSSL Project Authors
Copyright 2004-2020 The OpenSSL Project Authors
Copyright 2004-2021 The OpenSSL Project Authors
Copyright 2005-2016 The OpenSSL Project Authors
Copyright 2005-2017 The OpenSSL Project Authors
Copyright 2005-2018 The OpenSSL Project Authors
Copyright 2005-2019 The OpenSSL Project Authors
Copyright 2005-2020 The OpenSSL Project Authors
Copyright 2005-2021 The OpenSSL Project Authors
Copyright 2006-2016 The OpenSSL Project Authors
Copyright 2006-2017 The OpenSSL Project Authors
Copyright 2006-2018 The OpenSSL Project Authors
Copyright 2006-2019 The OpenSSL Project Authors
Copyright 2006-2020 The OpenSSL Project Authors
Copyright 2006-2021 The OpenSSL Project Authors
Copyright 2007-2016 The OpenSSL Project Authors
Copyright 2007-2018 The OpenSSL Project Authors
Copyright 2007-2020 The OpenSSL Project Authors
Copyright 2007-2021 The OpenSSL Project Authors
Copyright 2008-2016 The OpenSSL Project Authors
Copyright 2008-2018 The OpenSSL Project Authors
Copyright 2008-2019 The OpenSSL Project Authors
Copyright 2008-2020 The OpenSSL Project Authors
Copyright 2008-2021 The OpenSSL Project Authors
Copyright 2008-2022 The OpenSSL Project Authors
Copyright 2009-2016 The OpenSSL Project Authors
Copyright 2009-2018 The OpenSSL Project Authors
Copyright 2009-2020 The OpenSSL Project Authors
Copyright 2009-2021 The OpenSSL Project Authors
Copyright 2009-2022 The OpenSSL Project Authors
Copyright 2010-2016 The OpenSSL Project Authors
Copyright 2010-2019 The OpenSSL Project Authors
Copyright 2010-2020 The OpenSSL Project Authors
Copyright 2010-2021 The OpenSSL Project Authors
Copyright 2010-2022 The OpenSSL Project Authors
Copyright 2011-2016 The OpenSSL Project Authors
Copyright 2011-2017 The OpenSSL Project Authors
Copyright 2011-2018 The OpenSSL Project Authors
Copyright 2011-2019 The OpenSSL Project Authors
Copyright 2011-2020 The OpenSSL Project Authors
Copyright 2011-2021 The OpenSSL Project Authors
Copyright 2012, Samuel Neves <<EMAIL>>
Copyright 2012-2016 The OpenSSL Project Authors
Copyright 2012-2017 The OpenSSL Project Authors
Copyright 2012-2019 The OpenSSL Project Authors
Copyright 2012-2020 The OpenSSL Project Authors
Copyright 2012-2021 The OpenSSL Project Authors
Copyright 2012-2022 The OpenSSL Project Authors
Copyright 2013-2016 The OpenSSL Project Authors
Copyright 2013-2017 The OpenSSL Project Authors
Copyright 2013-2018 The OpenSSL Project Authors
Copyright 2013-2019 The OpenSSL Project Authors
Copyright 2013-2020 The OpenSSL Project Authors
Copyright 2013-2021 The OpenSSL Project Authors
Copyright 2014-2016 Cryptography Research, Inc.
Copyright 2014-2016 The OpenSSL Project Authors
Copyright 2014-2017 The OpenSSL Project Authors
Copyright 2014-2018 The OpenSSL Project Authors
Copyright 2014-2019 The OpenSSL Project Authors
Copyright 2014-2020 The OpenSSL Project Authors
Copyright 2014-2021 The OpenSSL Project Authors
Copyright 2015-2016 Cryptography Research, Inc.
Copyright 2015-2016 The OpenSSL Project Authors
Copyright 2015-2017 The OpenSSL Project Authors
Copyright 2015-2018 The OpenSSL Project Authors
Copyright 2015-2019 The OpenSSL Project Authors
Copyright 2015-2020 The OpenSSL Project Authors
Copyright 2015-2021 The OpenSSL Project Authors
Copyright 2015-2022 The OpenSSL Project Authors
Copyright 2016-2016 The OpenSSL Project Authors
Copyright 2016-2017 The OpenSSL Project Authors
Copyright 2016-2018 The OpenSSL Project Authors
Copyright 2016-2019 The OpenSSL Project Authors
Copyright 2016-2020 The OpenSSL Project Authors
Copyright 2016-2021 The OpenSSL Project Authors
Copyright 2016-2022 The OpenSSL Project Authors
Copyright 2017-2018 The OpenSSL Project Authors
Copyright 2017-2019 The OpenSSL Project Authors
Copyright 2017-2020 The OpenSSL Project Authors
Copyright 2017-2021 The OpenSSL Project Authors
Copyright 2017-2022 The OpenSSL Project Authors
Copyright 2018-2019 The OpenSSL Project Authors
Copyright 2018-2020 The OpenSSL Project Authors
Copyright 2018-2021 The OpenSSL Project Authors
Copyright 2019-2020 The OpenSSL Project Authors
Copyright 2019-2021 The OpenSSL Project Authors
Copyright 20xx-20yy The OpenSSL Project Authors
Copyright (c) 2002, Oracle and/or its affiliates
Copyright (c) 2017, Oracle and/or its affiliates
Copyright (c) 2018, Oracle and/or its affiliates
Copyright 1995-$YEAR The OpenSSL Project Authors
Copyright 1998-$YEAR The OpenSSL Project Authors
Copyright 1999-$YEAR The OpenSSL Project Authors
Copyright 2000-$YEAR The OpenSSL Project Authors
Copyright 2017 Ribose Inc. (https://www.ribose.com)
Copyright (c) 1995-1998 Eric A. Young, Tim J. Hudson
Copyright (c) 2008 Andy Polyakov <<EMAIL>>
Copyright (c) 1995-1998 Eric Young (<EMAIL>)
Copyright (c) 1989, 1991 Free Software Foundation, Inc.
Copyright (c) 2017 National Security Research Institute
Copyright (c) 2004, Richard Levitte <<EMAIL>>
Copyright (c) 2013-2014 Timo Teras <<EMAIL>>
Copyright (c) 2007 KISA(Korea Information Security Agency)
Copyright (c) 2004, 2018, Richard Levitte <<EMAIL>>
Copyright (c) 2016 Viktor Dukhovni <<EMAIL>>
Copyright 2006 NTT (Nippon Telegraph and Telephone Corporation)
Copyright (c) 2005 WISeKey SA1 Internat(onal1)0 WISeKey CertifyID Advanced G1 CA0
Copyright (c) 2005 WISeKey SA1 International1 0 WISeKey CertifyID Advanced G1 CA0
Copyright (c) 2004 Kungliga Tekniska Hogskolan (Royal Institute of Technology, Stockholm, Sweden)


  LICENSE ISSUES
  ==============

  The OpenSSL toolkit stays under a double license, i.e. both the conditions of
  the OpenSSL License and the original SSLeay license apply to the toolkit.
  See below for the actual license texts.

  OpenSSL License
  ---------------

/* ====================================================================
 * Copyright (c) 1998-2019 The OpenSSL Project.  All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in
 *    the documentation and/or other materials provided with the
 *    distribution.
 *
 * 3. All advertising materials mentioning features or use of this
 *    software must display the following acknowledgment:
 *    "This product includes software developed by the OpenSSL Project
 *    for use in the OpenSSL Toolkit. (http://www.openssl.org/)"
 *
 * 4. The names "OpenSSL Toolkit" and "OpenSSL Project" must not be used to
 *    endorse or promote products derived from this software without
 *    prior written permission. For written permission, please contact
 *    <EMAIL>.
 *
 * 5. Products derived from this software may not be called "OpenSSL"
 *    nor may "OpenSSL" appear in their names without prior written
 *    permission of the OpenSSL Project.
 *
 * 6. Redistributions of any form whatsoever must retain the following
 *    acknowledgment:
 *    "This product includes software developed by the OpenSSL Project
 *    for use in the OpenSSL Toolkit (http://www.openssl.org/)"
 *
 * THIS SOFTWARE IS PROVIDED BY THE OpenSSL PROJECT ``AS IS'' AND ANY
 * EXPRESSED OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE OpenSSL PROJECT OR
 * ITS CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 * SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT
 * NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
 * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED
 * OF THE POSSIBILITY OF SUCH DAMAGE.
 * ====================================================================
 *
 * This product includes cryptographic software written by Eric Young
 * (<EMAIL>).  This product includes software written by Tim
 * Hudson (<EMAIL>).
 *
 */

 Original SSLeay License
 -----------------------

/* Copyright (C) 1995-1998 Eric Young (<EMAIL>)
 * All rights reserved.
 *
 * This package is an SSL implementation written
 * by Eric Young (<EMAIL>).
 * The implementation was written so as to conform with Netscapes SSL.
 *
 * This library is free for commercial and non-commercial use as long as
 * the following conditions are aheared to.  The following conditions
 * apply to all code found in this distribution, be it the RC4, RSA,
 * lhash, DES, etc., code; not just the SSL code.  The SSL documentation
 * included with this distribution is covered by the same copyright terms
 * except that the holder is Tim Hudson (<EMAIL>).
 *
 * Copyright remains Eric Young's, and as such any Copyright notices in
 * the code are not to be removed.
 * If this package is used in a product, Eric Young should be given attribution
 * as the author of the parts of the library used.
 * This can be in the form of a textual message at program startup or
 * in documentation (online or textual) provided with the package.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 1. Redistributions of source code must retain the copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 * 3. All advertising materials mentioning features or use of this software
 *    must display the following acknowledgement:
 *    "This product includes cryptographic software written by
 *     Eric Young (<EMAIL>)"
 *    The word 'cryptographic' can be left out if the rouines from the library
 *    being used are not cryptographic related :-).
 * 4. If you include any Windows specific code (or a derivative thereof) from
 *    the apps directory (application code) you must include an acknowledgement:
 *    "This product includes software written by Tim Hudson (<EMAIL>)"
 *
 * THIS SOFTWARE IS PROVIDED BY ERIC YOUNG ``AS IS'' AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED.  IN NO EVENT SHALL THE AUTHOR OR CONTRIBUTORS BE LIABLE
 * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
 * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
 * OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
 * OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 * SUCH DAMAGE.
 *
 * The licence and distribution terms for any publically available version or
 * derivative of this code cannot be changed.  i.e. this code cannot simply be
 * copied and put under another distribution licence
 * [including the GNU Public Licence.]
 */



---------------------------------------------------------

---------------------------------------------------------

com.microsoft.cognitiveservices.speech/client-sdk-embedded 1.24.2


(c) . Bias
(c) PiP6Le Si4i
Copyright (c) 2014
Copyright (c) 2016
Copyright (c) 2017
(c) 2016 (c) Y Seomi
Copyright 2005 Nokia
Copyright 2014 T68Oa
Copyright (c) Microsoft
Copyright Eric A. Young
copyright (c) Microsoft
(c) Microsoft Corporation
Copyright (c) Google Inc.
Copyright (c) 2017 okdshin
Copyright 2003 Google Inc.
Copyright 2005 Google Inc.
Copyright 2007 Google Inc.
Copyright 2008 Google Inc.
Copyright 2009 Google Inc.
Copyright 2010 Google Inc.
Copyright 2011 Google Inc.
Copyright 2013 Google Inc.
Copyright 2015 Google Inc.
Copyright 2017 Ribose Inc.
Copyright 2018 Google Inc.
(c) 2013 ATT Wi-Fi Services
Copyright (c) 2010 Bill Cox
Copyright (c) Firmwave Ltd.
Copyright 2003, Google Inc.
Copyright 2005, Google Inc.
Copyright 2006, Google Inc.
Copyright 2007 Neal Norwitz
Copyright 2007, Google Inc.
Copyright 2008, Google Inc.
Copyright 2009 Neal Norwitz
Copyright 2009, Google Inc.
Copyright 2010, Google Inc.
Copyright 2013, Google Inc.
Copyright 2015 Google, Inc.
Copyright 2015, Google Inc.
Copyright 2016 Google, Inc.
Copyright 2017 BaishanCloud
Copyright 2017 Google, Inc.
Copyright 2018 Google, Inc.
Copyright Louis Dionne 2015
Copyright (c) 2015 Max Woolf
Copyright (c) 2015 Microsoft
Copyright 2013 M. J. Dominus
Copyright 2017 Roman Lebedev
Copyright (c) 2003 Mark Adler
Copyright (c) 2011 Mark Adler
Copyright (c) by P.J. Plauger
Copyright 2010 Bill Cox NxRoN
Copyright 2010, 2011 Bill Cox
Copyright Patrick Powell 1995
copyright (c) 2013-2017 Niels
(c) Copyright Henrik Ravn 2004
Copyright (c) 1997, Eric Young
Copyright (c) 2009 Google Inc.
Copyright (c) 2011, RTFM, Inc.
Copyright (c) 2013 Google Inc.
Copyright (c) 2015 Google Inc.
Copyright (c) Henrik Ravn 2004
Copyright 1995-2013 Mark Adler
Copyright 1995-2017 Mark Adler
Copyright 2010, 2011, Bill Cox
Copyright Svend Olaf Mikkelsen
copyright 2008 by Glenn Kasten
(c) Copyright 1999 Bodo Moeller
Copyright (c) 2003 Cosmin Truta
Copyright (c) 2003 David Blythe
Copyright (c) 2014, Google Inc.
Copyright 2004-2005 Jetro Lauha
Copyright Rene Rivera 2004-2007
Copyright (c) 2002 Markus Friedl
Copyright (c) 2002 Theo de Raadt
Copyright (c) 2006 Cryptocom LTD
Copyright (c) 2011, Tony Million
Copyright (c) 2015-2016 AlpacaDB
Copyright 2010, Bill Cox, Apache
Copyright 2014 Intel Corporation
Copyright (c) 1990-2000 Info-ZIP.
Copyright (c) 1998 by Bob Dellaca
Copyright (c) 2001-2012 Taku Kudo
Copyright (c) 2004 by Henrik Ravn
Copyright (c) 2016 Nicolas Seriot
Copyright 2013 Mark Jason Dominus
Copyright 2015 Guoguo Chen Apache
Copyright 2016 VMS Software, Inc.
(c) Copyright Microsoft Corp. 1993
Copyright (c) 1995-1997 Eric Young
Copyright (c) 1995-2003 Mark Adler
Copyright (c) 1995-2008 Mark Adler
Copyright (c) 1995-2013 Mark Adler
Copyright (c) 1995-2016 Mark Adler
Copyright (c) 1995-2017 Mark Adler
Copyright (c) 2002-2013 Mark Adler
Copyright (c) 2003 by Cosmin Truta
Copyright (c) 2003-2010 Mark Adler
Copyright (c) 2004-2017 Mark Adler
Copyright (c) 2009 Florian Loitsch
Copyright (c) 2016 Oussama ENNAFII
Copyright 1998-2004 Gilles Vollant
Copyright 2016 The OpenSSL Project
Copyright 2017 The OpenSSL Project
Copyright 2018 The OpenSSL Project
Copyright 2019 The OpenSSL Project
Copyright 2020 The OpenSSL Project
copyright 2017, Microsoft Feedback
(c) The Android Open Source Project
Copyright (c) 1996 L. Peter Deutsch
Copyright (c) 1997,99 Borland Corp.
Copyright (c) 2003, 2012 Mark Adler
Copyright (c) 2004, 2005 Mark Adler
Copyright (c) 2004, 2010 Mark Adler
Copyright (c) 2004, EdelKey Project
Copyright (c) 2005, 2012 Mark Adler
Copyright (c) 2015 CloudFlare, Inc.
Copyright (c) Microsoft Corporation
Portions Copyright 2007 Google Inc.
Portions Copyright 2009 Google Inc.
Copyright (c) 2004-2005, Jetro Lauha
Copyright (c) 2007-2008 Even Rouault
Copyright (c) 2013 Intel Corporation
Copyright (c) 2014 Intel Corporation
Copyright (c) 2015, CloudFlare, Inc.
Copyright (c) 2015-2016, Itseez Inc.
Copyright (c) 2017 Intel Corporation
Copyright 2014 Microsoft Corporation
Copyright (c) 2005 WISeKey SA1 InterU
Copyright (c) 2005-2006 Cryptocom LTD
Copyright (c) 2011-2013, Tony Million
Copyright (c) 2012, Intel Corporation
Copyright (c) 2013-2019 Niels Lohmann
Copyright (c) 2014, Intel Corporation
Copyright (c) 2015-2017 Niels Lohmann
copyright (c) 2013-2017 Niels Lohmann
Copyright (c) 1998-2005 Gilles Vollant
Copyright (c) 1999 The OpenSSL Project
Copyright (c) 2000 The OpenSSL Project
Copyright (c) 2001 The OpenSSL Project
Copyright (c) 2002 The OpenSSL Project
Copyright (c) 2002 The OpenTSA Project
Copyright (c) 2003 The OpenSSL Project
Copyright (c) 2004 The OpenSSL Project
Copyright (c) 2004, 2005 by Mark Adler
Copyright (c) 2005 The OpenSSL Project
Copyright (c) 2006 The OpenSSL Project
Copyright (c) 2007 The OpenSSL Project
Copyright (c) 2008 The OpenSSL Project
Copyright (c) 2009 The OpenSSL Project
Copyright (c) 2010 The OpenSSL Project
Copyright (c) 2011 The OpenSSL Project
Copyright (c) 2012 The OpenSSL Project
Copyright (c) 2013 The OpenSSL Project
Copyright (c) 2014 The OpenSSL Project
Copyright (c) 2016 The OpenSSL Project
Copyright (c) 2016-2018 Viktor Kirilov
Copyright (c) 2016-2019 Viktor Kirilov
Copyright 2016 Ismael Jimenez Martinez
Copyright (c) 1992-2013 by P.J. Plauger
Copyright (c) 2007 Copyright 2005 Nokia
Copyright (c) 2016, NVIDIA CORPORATION.
Copyright (c) 2017, NVIDIA CORPORATION.
Copyright (c) 2018-2019 Bryan Gillespie
Copyright 1995-2016 The OpenSSL Project
Copyright 1995-2017 The OpenSSL Project
Copyright 1995-2018 The OpenSSL Project
Copyright 1995-2019 The OpenSSL Project
Copyright 1995-2020 The OpenSSL Project
Copyright 1995-2021 The OpenSSL Project
Copyright 1995-2022 The OpenSSL Project
Copyright 1998-2001 The OpenSSL Project
Copyright 1998-2016 The OpenSSL Project
Copyright 1998-2017 The OpenSSL Project
Copyright 1998-2018 The OpenSSL Project
Copyright 1998-2019 The OpenSSL Project
Copyright 1998-2020 The OpenSSL Project
Copyright 1998-2021 The OpenSSL Project
Copyright 1998-2022 The OpenSSL Project
Copyright 1999-2016 The OpenSSL Project
Copyright 1999-2017 The OpenSSL Project
Copyright 1999-2018 The OpenSSL Project
Copyright 1999-2019 The OpenSSL Project
Copyright 1999-2020 The OpenSSL Project
Copyright 1999-2021 The OpenSSL Project
Copyright 1999-2022 The OpenSSL Project
Copyright 2000-2016 The OpenSSL Project
Copyright 2000-2017 The OpenSSL Project
Copyright 2000-2018 The OpenSSL Project
Copyright 2000-2019 The OpenSSL Project
Copyright 2000-2020 The OpenSSL Project
Copyright 2000-2021 The OpenSSL Project
Copyright 2000-2022 The OpenSSL Project
Copyright 2001-2016 The OpenSSL Project
Copyright 2001-2017 The OpenSSL Project
Copyright 2001-2018 The OpenSSL Project
Copyright 2001-2019 The OpenSSL Project
Copyright 2001-2020 The OpenSSL Project
Copyright 2001-2021 The OpenSSL Project
Copyright 2001-2022 The OpenSSL Project
Copyright 2002-2016 The OpenSSL Project
Copyright 2002-2017 The OpenSSL Project
Copyright 2002-2018 The OpenSSL Project
Copyright 2002-2019 The OpenSSL Project
Copyright 2002-2020 The OpenSSL Project
Copyright 2002-2021 The OpenSSL Project
Copyright 2002-2022 The OpenSSL Project
Copyright 2003-2016 The OpenSSL Project
Copyright 2003-2017 The OpenSSL Project
Copyright 2003-2018 The OpenSSL Project
Copyright 2003-2020 The OpenSSL Project
Copyright 2003-2021 The OpenSSL Project
Copyright 2004-2016 The OpenSSL Project
Copyright 2004-2017 The OpenSSL Project
Copyright 2004-2018 The OpenSSL Project
Copyright 2004-2019 The OpenSSL Project
Copyright 2004-2020 The OpenSSL Project
Copyright 2004-2021 The OpenSSL Project
Copyright 2005-2016 The OpenSSL Project
Copyright 2005-2017 The OpenSSL Project
Copyright 2005-2018 The OpenSSL Project
Copyright 2005-2019 The OpenSSL Project
Copyright 2005-2020 The OpenSSL Project
Copyright 2005-2021 The OpenSSL Project
Copyright 2006-2016 The OpenSSL Project
Copyright 2006-2017 The OpenSSL Project
Copyright 2006-2018 The OpenSSL Project
Copyright 2006-2019 The OpenSSL Project
Copyright 2006-2020 The OpenSSL Project
Copyright 2006-2021 The OpenSSL Project
Copyright 2007-2016 The OpenSSL Project
Copyright 2007-2018 The OpenSSL Project
Copyright 2007-2020 The OpenSSL Project
Copyright 2007-2021 The OpenSSL Project
Copyright 2008-2016 The OpenSSL Project
Copyright 2008-2018 The OpenSSL Project
Copyright 2008-2019 The OpenSSL Project
Copyright 2008-2020 The OpenSSL Project
Copyright 2008-2021 The OpenSSL Project
Copyright 2008-2022 The OpenSSL Project
Copyright 2009-2016 The OpenSSL Project
Copyright 2009-2018 The OpenSSL Project
Copyright 2009-2020 The OpenSSL Project
Copyright 2009-2021 The OpenSSL Project
Copyright 2009-2022 The OpenSSL Project
Copyright 2010-2016 The OpenSSL Project
Copyright 2010-2019 The OpenSSL Project
Copyright 2010-2020 The OpenSSL Project
Copyright 2010-2021 The OpenSSL Project
Copyright 2010-2022 The OpenSSL Project
Copyright 2011-2016 The OpenSSL Project
Copyright 2011-2017 The OpenSSL Project
Copyright 2011-2018 The OpenSSL Project
Copyright 2011-2019 The OpenSSL Project
Copyright 2011-2020 The OpenSSL Project
Copyright 2011-2021 The OpenSSL Project
Copyright 2012 Johns Hopkins University
Copyright 2012-2016 The OpenSSL Project
Copyright 2012-2017 The OpenSSL Project
Copyright 2012-2019 The OpenSSL Project
Copyright 2012-2020 The OpenSSL Project
Copyright 2012-2021 The OpenSSL Project
Copyright 2012-2022 The OpenSSL Project
Copyright 2013-2016 The OpenSSL Project
Copyright 2013-2017 The OpenSSL Project
Copyright 2013-2018 The OpenSSL Project
Copyright 2013-2019 The OpenSSL Project
Copyright 2013-2020 The OpenSSL Project
Copyright 2013-2021 The OpenSSL Project
Copyright 2014-2016 The OpenSSL Project
Copyright 2014-2017 The OpenSSL Project
Copyright 2014-2018 The OpenSSL Project
Copyright 2014-2019 The OpenSSL Project
Copyright 2014-2020 The OpenSSL Project
Copyright 2014-2021 The OpenSSL Project
Copyright 2015-2016 The OpenSSL Project
Copyright 2015-2017 The OpenSSL Project
Copyright 2015-2018 The OpenSSL Project
Copyright 2015-2019 The OpenSSL Project
Copyright 2015-2020 The OpenSSL Project
Copyright 2015-2021 The OpenSSL Project
Copyright 2015-2022 The OpenSSL Project
Copyright 2016 Microsoft Azure C Native
Copyright 2016-2016 The OpenSSL Project
Copyright 2016-2017 The OpenSSL Project
Copyright 2016-2018 The OpenSSL Project
Copyright 2016-2019 The OpenSSL Project
Copyright 2016-2020 The OpenSSL Project
Copyright 2016-2021 The OpenSSL Project
Copyright 2016-2022 The OpenSSL Project
Copyright 2017-2018 The OpenSSL Project
Copyright 2017-2019 The OpenSSL Project
Copyright 2017-2020 The OpenSSL Project
Copyright 2017-2021 The OpenSSL Project
Copyright 2017-2022 The OpenSSL Project
Copyright 2018-2019 The OpenSSL Project
Copyright 2018-2020 The OpenSSL Project
Copyright 2018-2021 The OpenSSL Project
Copyright 2019-2020 The OpenSSL Project
Copyright 2019-2021 The OpenSSL Project
Copyright 20xx-20yy The OpenSSL Project
Copyright (c) 1995-1998 Jean-loup Gailly
Copyright (c) 1995-2003 Jean-loup Gailly
Copyright (c) 1995-2003, 2010 Mark Adler
Copyright (c) 1995-2005, 2010 Mark Adler
Copyright (c) 1995-2011, 2016 Mark Adler
Copyright (c) 1995-2013 Jean-loup Gailly
Copyright (c) 1995-2016 Jean-loup Gailly
Copyright (c) 1995-2017 Jean-loup Gailly
Copyright 1995-$YEAR The OpenSSL Project
Copyright 1998-$YEAR The OpenSSL Authors
Copyright 1998-$YEAR The OpenSSL Project
Copyright 1999-$YEAR The OpenSSL Project
Copyright 2000-$YEAR The OpenSSL Project
Copyright 2004-2014, Akamai Technologies
Copyright ?1998-2005 The OpenSSL Project
holder is Tim Hudson (<EMAIL>)
Copyright (c) 1997,99 Borland Corporation
Copyright (c) 1998 by Andreas R. Kleinert
Copyright (c) 2002-2003 Dmitriy Anisimkov
Copyright (c) 2002-2004 Dmitriy Anisimkov
Copyright (c) 2004, 2005, 2012 Mark Adler
Copyright (c) 2004, 2008, 2012 Mark Adler
Copyright (c) 2007, 2008, 2012 Mark Adler
Copyright (c) 2012-2016 Intel Corporation
Copyright The Android Open Source Project
Copyright (c) 1998 by Jacques Nomssi Nzali
Copyright (c) 2000-2016, Intel Corporation
Copyright (c) 2015 Copyright 2015 AzureIoT
Copyright (c) 2015-2016, OpenCV Foundation
Copyright 2014 Cryptography Research, Inc.
Copyright 2015 Cryptography Research, Inc.
Copyright 2016 Cryptography Research, Inc.
(c) 1995-2013 Jean-loup Gailly & Mark Adler
Copyright (c) 1995-2003 by Jean-loup Gailly
Copyright (c) 1998-1999 The OpenSSL Project
Copyright (c) 1998-2000 The OpenSSL Project
Copyright (c) 1998-2001 The OpenSSL Project
Copyright (c) 1998-2002 The OpenSSL Project
Copyright (c) 1998-2003 The OpenSSL Project
Copyright (c) 1998-2005 The OpenSSL Project
Copyright (c) 1998-2006 The OpenSSL Project
Copyright (c) 1998-2007 The OpenSSL Project
Copyright (c) 1998-2009 The OpenSSL Project
Copyright (c) 1998-2010 - by Gilles Vollant
Copyright (c) 1998-2010 The OpenSSL Project
Copyright (c) 1998-2011 The OpenSSL Project
Copyright (c) 1998-2015 The OpenSSL Project
Copyright (c) 1998-2018 The OpenSSL Project
Copyright (c) 1998-2019 The OpenSSL Project
Copyright (c) 1998-2021 The OpenSSL Project
Copyright (c) 1999-2001 The OpenSSL Project
Copyright (c) 1999-2002 The OpenSSL Project
Copyright (c) 1999-2003 The OpenSSL Project
Copyright (c) 1999-2004 The OpenSSL Project
Copyright (c) 1999-2005 The OpenSSL Project
Copyright (c) 1999-2006 The OpenSSL Project
Copyright (c) 1999-2007 The OpenSSL Project
Copyright (c) 1999-2008 The OpenSSL Project
Copyright (c) 1999-2009 The OpenSSL Project
Copyright (c) 1999-2010 The OpenSSL Project
Copyright (c) 1999-2011 The OpenSSL Project
Copyright (c) 1999-2013 The OpenSSL Project
Copyright (c) 1999-2014 The OpenSSL Project
Copyright (c) 1999-2015 The OpenSSL Project
Copyright (c) 1999-2016 The OpenSSL Project
Copyright (c) 1999-2017 The OpenSSL Project
Copyright (c) 1999-2018 The OpenSSL Project
Copyright (c) 1999-2019 The OpenSSL Project
Copyright (c) 2000,2005 The OpenSSL Project
Copyright (c) 2000-2001 The OpenSSL Project
Copyright (c) 2000-2002 The OpenSSL Project
Copyright (c) 2000-2003 The OpenSSL Project
Copyright (c) 2000-2004 The OpenSSL Project
Copyright (c) 2000-2005 The OpenSSL Project
Copyright (c) 2000-2018 The OpenSSL Project
Copyright (c) 2001-2002 The OpenSSL Project
Copyright (c) 2001-2004 The OpenSSL Project
Copyright (c) 2001-2005 The OpenSSL Project
Copyright (c) 2001-2008 The OpenSSL Project
Copyright (c) 2001-2018 The OpenSSL Project
Copyright (c) 2001-2019 The OpenSSL Project
Copyright (c) 2002-2006 The OpenSSL Project
Copyright (c) 2004-2011 The OpenSSL Project
Copyright (c) 2005-2018 The OpenSSL Project
Copyright (c) 2006, Network Resonance, Inc.
Copyright (c) 2006,2007 The OpenSSL Project
Copyright (c) 2006-2018 The OpenSSL Project
Copyright (c) 2008-2018 The OpenSSL Project
Copyright (c) 2009-2011, Willow Garage Inc.
Copyright (c) 2009-2016, NVIDIA Corporation
Copyright (c) 2011-2013 The OpenSSL Project
Copyright (c) 2012-2014 Daniel J. Bernstein
Copyright (c) 2012-2018 The OpenSSL Project
copyrighted by the Free Software Foundation
Copyright (c) 1986 by Sun Microsystems, Inc.
Copyright (c) 1993 by Sun Microsystems, Inc.
Copyright (c) 1999-$year The OpenSSL Project
Copyright (c) 2001-$year The OpenSSL Project
Copyright (c) 2002, Industrial Light & Magic
Copyright (c) 2016-2017, NVIDIA CORPORATION.
(c) 1995-2012 Jean-loup Gailly and Mark Adler
Copyright (c) 2005 WISeKey SA1 International1
Copyright (c) The Android Open Source Project
Copyright (c) 1995-2006, 2011 Jean-loup Gailly
Copyright (c) 2007-2013 The Khronos Group Inc.
Copyright (c) 2012-2016 Jean-Philippe Aumasson
Copyright 2009 The Android Open Source Project
Copyright 2013 The Android Open Source Project
Copyright 2015 The Android Open Source Project
Copyright 2016 The Android Open Source Project
Copyright 2017 The Android Open Source Project
Copyright 2018 The Android Open Source Project
Copyright (c) 2017 Copyright (c) 2016 Microsoft
Copyright 1998-2000 nCipher Corporation Limited
Copyright 2006, The Android Open Source Project
Copyright 2009, The Android Open Source Project
Copyright 2012, Samuel Neves <<EMAIL>>
Copyright 2014-2016 Cryptography Research, Inc.
Copyright 2015-2016 Cryptography Research, Inc.
Copyright (c) 2002, Oracle and/or its affiliates
Copyright (c) 2013 Intel Corporation Jim Kukunas
Copyright (c) 2014 Copyright (c) by P.J. Plauger
Copyright (c) 2017, Oracle and/or its affiliates
Copyright (c) 2018, Oracle and/or its affiliates
Copyright Beman Dawes, David Abrahams, 1998-2005
Copyright (c) 2015 Free Software Foundation, Inc.
Copyright 2012-2014 Brno University of Technology
Copyright ?1995-1998 Eric A. Young, Tim J. Hudson
Copyright (c) 1991-2005 Carnegie Mellon University
Copyright (c) 1993-2015 Carnegie Mellon University
Copyright (c) 2007 The Android Open Source Project
Copyright (c) 2008 The Android Open Source Project
Copyright (c) 2009 The Android Open Source Project
Copyright (c) 2010 The Android Open Source Project
Copyright (c) 2011 The Android Open Source Project
Copyright (c) 2014 The Android Open Source Project
Copyright (c) 2015 Copyright (c) Texas Instruments
Copyright (c) 2015 The Android Open Source Project
Copyright (c) 2016 The Android Open Source Project
Copyright (c) 2017 The Android Open Source Project
Copyright (c) 2018 The Android Open Source Project
Copyright (c) 2006 G-Truc Creation (www.g-truc.net)
Copyright (c) 2010 Bill Cox <<EMAIL>>
Copyright 1995-2013 Jean-loup Gailly and Mark Adler
Copyright 1995-2017 Jean-loup Gailly and Mark Adler
Copyright 2017 Ribose Inc. (https://www.ribose.com)
Copyright (c) 1995-1998 Eric A. Young, Tim J. Hudson
Copyright (c) 1995-2006, 2010, 2011 Jean-loup Gailly
Copyright (c) 1995-2013 Jean-loup Gailly, Mark Adler
Copyright (c) 1995-2016 Jean-loup Gailly, Mark Adler
Copyright (c) 1998,1999,2000 by Jacques Nomssi Nzali
Copyright (c) 2008 Andy Polyakov <<EMAIL>>
Copyright 2015 The Android Open Source Project, Inc.
Copyright (c) 2003, 2005, 2008, 2010, 2012 Mark Adler
Copyright (c) 2010-2013, Advanced Micro Devices, Inc.
Copyright (c) Facebook Inc. and Microsoft Corporation
Copyright 2012-2013 Karel Vesely, Daniel Povey Apache
Copyright (c) 1995-1997 Eric Young (<EMAIL>)
Copyright (c) 1995-1998 Eric Young (eay cryptsoft.com)
Copyright (c) 1995-1998 Eric Young (<EMAIL>)
Copyright (c) 2003 Chris Anderson <<EMAIL>>
Copyright (c) 2012, Erik Edlund <<EMAIL>>
Copyright (c) 2018 Vitaliy Manushkin <<EMAIL>>
Copyright (c) 1989, 1991 Free Software Foundation, Inc.
Copyright (c) 1991, 1999 Free Software Foundation, Inc.
Copyright (c) 1995-2003 Jean-loup Gailly and Mark Adler
Copyright (c) 1995-2013 Jean-loup Gailly and Mark Adler
Copyright (c) 1995-2017 Jean-loup Gailly and Mark Adler
Copyright (c) 2017 National Security Research Institute
Copyright 2004-2005 Jetro Lauha Web http://iki.fi/jetro
copyright (c) 1995-2006 Jean-loup Gailly and Mark Adler
(c) COPYRIGHT International Business Machines Corp. 2001
Copyright (c) 1996 L. Peter Deutsch and Jean-Loup Gailly
Copyright (c) 1999-2014 Dieter Baron and Thomas Klausner
Copyright 2015-2016 Espressif Systems (Shanghai) PTE LTD
Copyright (c) 1998 Brian Raiter <<EMAIL>>
Copyright (c) 2004, Richard Levitte <<EMAIL>>
Copyright (c) 2013-2014 Timo Teras <<EMAIL>>
Copyright (c) 1995-2006, 2010, 2011, 2012, 2016 Mark Adler
Copyright (c) 2005 - 2012 G-Truc Creation (www.g-truc.net)
Copyright (c) 2005 - 2013 G-Truc Creation (www.g-truc.net)
Copyright (c) 2005 - 2014 G-Truc Creation (www.g-truc.net)
Copyright (c) 2007 KISA(Korea Information Security Agency)
Copyright (c) 2013-2019 Niels Lohmann (http://nlohmann.me)
Copyright (c) 2013-2019 Niels Lohmann <http://nlohmann.me>
Copyright (c) 2005 Hewlett-Packard Development Company, L.P.
Copyright (c) 2009-2010 Mathias Svensson http://result42.com
Copyright (c) 2001-2004 Swedish Institute of Computer Science
(c) 1995-2013 Jean-loup Gailly (<EMAIL>) and Mark Adler
Copyright (c) 2011-2014, Andrew Fischer <<EMAIL>>
Copyright (c) 1998, 2007 Brian Raiter <<EMAIL>>
Copyright (c) 2004, 2018, Richard Levitte <<EMAIL>>
Copyright (c) 2016 Viktor Dukhovni <<EMAIL>>
Copyright 2006 NTT (Nippon Telegraph and Telephone Corporation)
(c) Microsoft Corporation. Brandon Long <<EMAIL>> 1996
Copyright (c) 1995-2005, 2014, 2016 Jean-loup Gailly, Mark Adler
Copyright (c) 2009 Florian Loitsch (http://florian.loitsch.com/)
Copyright (c) 2004, 2005, 2010, 2011, 2012, 2013, 2016 Mark Adler
Copyright (c) 2017, Alexander Widerberg <<EMAIL>>
Copyright (c) 2004-2008 Nippon Telegraph and Telephone Corporation
Copyright (c) 2019 (c) 2008 Google Inc. CoprocPair IntPair I64Regs
Copyright 2012-2013 Karel Vesely, Daniel Povey 2015 Yu Zhang Apache
Copyright Jean-loup Gailly Osma Ahvenlampi <<EMAIL>>
Copyright (c) 2007 Free Software Foundation, Inc. <https://fsf.org/>
Copyright 2012 Johns Hopkins University (Author Daniel Povey) Apache
Copyright (c) 1997 Christian Michelsen Research AS Advanced Computing
Copyright (c) 1995-2003, 2010, 2014, 2016 Jean-loup Gailly, Mark Adler
Copyright (c) 2004-2008 Nippon Telegraph and Telephone Corporatioatomic
Copyright 2000 Broadcom Corporation Michael Elkins <<EMAIL>> 1998
Copyright (c) 1998 - 2010 Gilles Vollant, Even Rouault, Mathias Svensson
Copyright 2002 Sun Microsystems, Inc. Andrew Tridgell <<EMAIL>>
Copyright (c) 1995-1996 Jean-loup Gailly, Brian Raiter and Gilles Vollant
Copyright (c) 1995-2010 Jean-loup Gailly, Brian Raiter and Gilles Vollant
Copyright 2015 Microsoft Corporation Luke Mewburn <<EMAIL>> 1999
Copyright 20xx-20yy The OpenSSL Project Thomas Roessler <<EMAIL>> 1998
Copyright (c) 2005 WISeKey SA1 Internat(onal1)0 WISeKey CertifyID Advanced G1 CA0
Copyright (c) 1998-now The OpenSSL Project Copyright@2001 Baltimore Technologies Ltd.
Copyright (c) 2006, Network Resonance, Inc. Patrick Powell <<EMAIL>> (1995)
Copyright (c) 2008-2009 Bjorn Hoehrmann (http://bjoern.hoehrmann.de/) <<EMAIL>>
Copyright (c) 1998-2010 Gilles Vollant (minizip) http://www.winimage.com/zLibDll/minizip.html
Copyright (c) 2002 Bob Beck <<EMAIL>> Ralf S. Engelschall <<EMAIL>> 1999
Copyright (c) 2004 Kungliga Tekniska Hogskolan (Royal Institute of Technology, Stockholm, Sweden)
Copyright (c) 1995-2013 Jean-loup Gailly (<EMAIL>) and Mark Adler (<EMAIL>)
Copyright (c) 2008-2009 Bjoern Hoehrmann <<EMAIL>> sa http://bjoern.hoehrmann.de/utf-8/decoder/dfa

# MICROSOFT SOFTWARE LICENSE TERMS

MICROSOFT COGNITIVE SERVICES SPEECH SDK

**IF YOU LIVE IN (OR ARE A BUSINESS WITH A PRINCIPAL PLACE OF BUSINESS IN) THE UNITED STATES, PLEASE READ THE "BINDING ARBITRATION AND CLASS ACTION WAIVER" SECTION BELOW. IT AFFECTS HOW DISPUTES ARE RESOLVED.**

These license terms are an agreement between you and Microsoft Corporation (or one of its affiliates). They apply to the software named above and any Microsoft services or software updates (except to the extent such services or updates are accompanied by new or additional terms, in which case those different terms apply prospectively and do not alter your or Microsoft's rights relating to pre-updated software or services). IF YOU COMPLY WITH THESE LICENSE TERMS, YOU HAVE THE RIGHTS BELOW. BY USING THE SOFTWARE, YOU ACCEPT THESE TERMS.

## 1. INSTALLATION AND USE RIGHTS.

**a) General.** You may install and use any number of copies of the software to develop and test your applications that integrate API(s) used to access and/or use Cognitive Services Speech Services.

**b) Third Party Components.** The software may include third party components with separate legal notices or governed by other agreements, as may be described in the ThirdPartyNotices file(s) accompanying the software or may be accessible at <http://aka.ms/thirdpartynotices>. Even if such components are governed by other agreements, the disclaimer, limitations on, and exclusions of damages below also apply to the extent allowed by applicable law.

**c) Competitive Benchmarking**. If you are a direct competitor, and you access or use the software for purposes of competitive benchmarking, analysis, or intelligence gathering, you waive as against Microsoft, its subsidiaries, and its affiliated companies (including prospectively) any competitive use, access, and benchmarking test restrictions in the terms governing your software to the extent your terms of use are, or purport to be, more restrictive than Microsoft's terms. If this section applies and you do not waive any such purported restrictions in the terms governing your software, you are not allowed to access or use this software, and will not do so.

## 2. DISTRIBUTABLE CODE. 

The software may contain code you are permitted to distribute (i.e., make available for third parties) in applications you develop, as described in this Section.

**a) Distribution Rights.** The code and test files described below are distributable if included with the software.

i. REDIST.TXT Files. You may copy and distribute the object code form of code listed on the REDIST list in the software, if any; and

ii. Third Party Distribution. You may permit distributors of your applications to copy and distribute any of this distributable code you elect to distribute with your applications.

**b) Distribution Requirements.** For any code you distribute, you must:

i. add significant primary functionality to it in your applications;

ii. require distributors and external end users to agree to terms that protect it and Microsoft at least as much as this agreement; and

iii. indemnify, defend, and hold harmless Microsoft from any claims, including attorneys' fees, related to the distribution or use of your applications, except to the extent that any claim is based solely on the unmodified distributable code.

**c) Distribution Restrictions.** You may not:

i. use Microsoft's trademarks or trade dress in your application in any way that suggests your application comes from or is endorsed by Microsoft; or

ii. modify or distribute the source code of any distributable code so that any part of it becomes subject to any license that requires that the distributable code, any other part of the software, or any of Microsoft's other intellectual property be disclosed or distributed in source code form, or that others have the right to modify it.

## 3. CONDITIONAL RIGHTS TO USE AND DISTRIBUTE EMBEDDED SPEECH COMPONENTS (PREVIEW). 

If you are approved for access to Embedded Speech Components in accordance with the terms for Limited Access Services in the Service-Specific Product Terms for Azure Cognitive Services and Applied AI Services available at <https://www.microsoft.com/licensing/terms/welcome/welcomepage>, and you are in compliance with these license terms and all requirements in an applicable license agreement that references these rights, you have the following additional rights with respect to the Embedded Speech Components.

**a) Definitions.**

i. "Embedded Speech Components" means (i) Embedded Speech extensions in the software and (ii) Embedded Speech models provided to you by Microsoft.

ii. "Company Device" means computing devices, other than Excluded Devices, that you sell, distribute, or otherwise dispose of for use with the Embedded Speech Components.

iii. "End Customer" means a person, company, or other legal entity that acquires a Company Device from you.

iv. "Excluded Device" means any computing device that would subject the Embedded Speech Components, as implemented in such device, to High Risk Use.

v. "High Risk Use" means use of the Embedded Speech Components in any of your devices or in combination with third party materials where failure or fault of any kind of the Embedded Speech Components could reasonably be seen to lead to death or serious bodily injury, or to severe physical or environmental damage.

**b) Distribution and Use Rights.**

i. Subject to your compliance with these terms (including the distribution requirements and distribution restrictions for distributable code) and all requirements in an applicable license agreement that references these rights, you may (A) download and link the Embedded Speech Components into an application that adds significant primary functionality to the Embedded Speech Components; (B) install the Embedded Speech Components, in binary form as incorporated in your application, on Company Devices; and (C) distribute the Embedded Speech Components as installed on Company Devices to End Customers.

ii. No right is provided to you to sell, distribute, sublicense, or otherwise make available the Embedded Speech Components separate from your applications that are installed on Company Devices or to implement the Embedded Speech Components in your application or Company Device in any manner that would subject the Embedded Speech Components to High Risk Use. You will use commercially reasonable efforts to notify Microsoft within a reasonable time if you become aware that an End Customer is using the Embedded Speech Components separate from your applications that are installed on Company Devices.

iii. Before selling, distributing, or otherwise disposing of any version of a Company Device, you will: (i) make your own determination that the Embedded Speech Components are suitable in quality and performance for use as implemented in your application and used in such Company Device, and (ii) test and examine the Embedded Speech Components in your application as installed on such Company Device.

**c) Required Updates.** From time to time, Microsoft may release by written notice an Update to the Embedded Speech Components designated as required to be performed (each a "Required Update"). Microsoft will use commercially reasonable efforts to minimize the number of Required Updates. Additional terms may apply to a Required Update and Microsoft will make these additional terms available to you. By distributing a Required Update, you agree to the additional terms. If Microsoft makes a Required Update available to you and you elect not to perform the Required Update, Microsoft has no duty or liability based on your installation, use, sale, offer for sale, importation, or other disposition or promotion of a Company Device more than 90 days after Microsoft makes available the Required Update. "Update" means, with respect to the Embedded Speech Components, a royalty-free replacement, bug-fix, or re-release of the software.

**d) Change Event.** Your rights under this section are subject to the following:

i. Microsoft may notify you requiring you to cease installation, further use, sale, offer for sale, importation, or other disposition or promotion of a Company Device due to a Change Event (each, a "Change Event Notice"). Such Change Event Notice will specify the extent and timing of the cessation and any other necessary actions. "Change Event" means that an applicable government or regulatory body in any jurisdiction in which a Company Device may be sold has determined that the Embedded Speech Components are illegal or otherwise subject to any regulation or legal limitation that the Embedded Speech Components were not (or not clearly) subject to as of the date on which Microsoft made the Embedded Speech Components available.

ii. Microsoft will issue a Change Event Notice only to the extent it deems reasonably necessary to address the precipitating Change Event. Microsoft and you will work together, promptly and in good faith, to: (i) coordinate public communications related to the Change Event Notice that specifically reference you or your products or services; and (ii) implement the Change Event Notice as applicable to you in an orderly, effective, and prompt manner.

iii. Additional obligations or remedies related to Change Events may be set forth in the applicable license agreement that references these rights.

iv. DUTY TO MITIGATE. If Microsoft gives you a Change Event Notice, Microsoft has no duty or liability based on your installation, use, sale, offer for sale, importation, or other disposition or promotion of Company Devices more than 30 days after receipt of such Change Event Notice.

**e) Compliance with Laws.** In exercising your rights and performing your obligations under this section, you will comply with all applicable laws, rules, statutes, orders, regulations, and judgments of any government authority having jurisdiction. If you experience a safety or product liability issue with a Company Device which relates to or implicates the Embedded Speech Components in any way, you agree to notify Microsoft promptly in writing so that Microsoft can comply with its own obligations for such an event. Microsoft will treat such notice as your Confidential Information to the extent permitted by applicable law.

**f) Pre-Release Software.** The Embedded Speech Components are a pre-release version. They may not operate correctly. They may be different from the commercially released version.

**g) Feedback.** If you give feedback about the Embedded Speech Components to Microsoft, you give to Microsoft, without charge, the right to use, share and commercialize your feedback in any way and for any purpose. You will not give feedback that is subject to a license that requires Microsoft to license its software or documentation to third parties because Microsoft includes your feedback in them. These rights survive this agreement.

**h)** The rights and obligations set forth in this section 3 are in addition to and subject to all other provisions of these SDK license terms except to the extent of any conflict, in which case the provisions of this section 3 will control. To the extent of any conflict between the rights and obligations set forth in this section 3 and the applicable license agreement, the provisions of the applicable license agreement will control.

## 4. SCOPE OF LICENSE. 

The software is licensed, not sold. Microsoft reserves all other rights. Unless applicable law gives you more rights despite this limitation, you will not (and have no right to):

**a)** work around any technical limitations in the software that only allow you to use it in certain ways;

**b)** reverse engineer, decompile or disassemble the software, or otherwise attempt to derive the source code for the software, except and to the extent required by third party licensing terms governing use of certain open source components that may be included in the software;

**c)** remove, minimize, block, or modify any notices of Microsoft or its suppliers in the software;

**d)** use the software in any way that is against the law or to create or propagate malware; or

**e)** share, publish, distribute, or lease the software (including as part of an application and except for any distributable code, subject to the terms above), provide the software as a stand-alone offering for others to use, or transfer the software or this agreement to any third party.

## 5. DATA.

**a) Data Collection.** The software may collect information about you and your use of the software, and send that to Microsoft. Microsoft may use this information to provide services and improve our products and services. You may opt-out of many of these scenarios, but not all, as described in the product documentation. There are also some features in the software that may enable you to collect data from users of your applications. If you use these features to enable data collection in your applications, you must comply with applicable law, including providing appropriate notices to users of your applications. You can learn more about data collection and use in the help documentation and the privacy statement at <https://aka.ms/privacy>. Your use of the software operates as your consent to these practices.

**b) Processing of Personal Data**. To the extent Microsoft is a processor or subprocessor of personal data in connection with the software, Microsoft makes the commitments in the European Union General Data Protection Regulation Terms of the Online Services Terms to all customers effective May 25, 2018, at <https://docs.microsoft.com/legal/gdpr>.

## 6. EXPORT RESTRICTIONS.
You must comply with all domestic and international export laws and regulations that apply to the software, which include restrictions on destinations, end users, and end use. For further information on export restrictions, visit <https://aka.ms/exporting>.

## 7. SUPPORT SERVICES.
Microsoft is not obligated under this agreement to provide any support services for the software. Any support provided is "as is", "with all faults", and without warranty of any kind. 

## 8. UPDATES.
The software may periodically check for updates, and download and install them for you. You may obtain updates only from Microsoft or authorized sources. Microsoft may need to update your system to provide you with updates. You agree to receive these automatic updates without any additional notice. Updates may not include or support all existing software features, services, or peripheral devices.

## 9. BINDING ARBITRATION AND CLASS ACTION WAIVER.
This Section applies if you live in (or, if a business, your principal place of business is in) the United States.** If you and Microsoft have a dispute, you and Microsoft agree to try for 60 days to resolve it informally. If you and Microsoft can't, you and Microsoft **agree to binding individual arbitration before the American Arbitration Association under the Federal Arbitration Act ("FAA"), and not to sue in court in front of a judge or jury**. Instead, a neutral arbitrator will decide. **Class action lawsuits, class-wide arbitrations, private attorney-general actions, and any other proceeding where someone acts in a representative capacity are not allowed; nor is combining individual proceedings without the consent of all parties.** The complete Arbitration Agreement contains more terms and is at <https://aka.ms/arb-agreement-4>. You and Microsoft agree to these terms.

## 10. TERMINATION.
Without prejudice to any other rights, Microsoft may terminate this agreement if you fail to comply with any of its terms or conditions. In such event, you must destroy all copies of the software and all of its component parts.

## 11. ENTIRE AGREEMENT.
This agreement, and any other terms Microsoft may provide for supplements, updates, or third-party applications, is the entire agreement for the software.

## 12. APPLICABLE LAW AND PLACE TO RESOLVE DISPUTES.
If you acquired the software in the United States or Canada, the laws of the state or province where you live (or, if a business, where your principal place of business is located) govern the interpretation of this agreement, claims for its breach, and all other claims (including consumer protection, unfair competition, and tort claims), regardless of conflict of laws principles, except that the FAA governs everything related to arbitration. If you acquired the software in any other country, its laws apply, except that the FAA governs everything related to arbitration. If U.S. federal jurisdiction exists, you and Microsoft consent to exclusive jurisdiction and venue in the federal court in King County, Washington for all disputes heard in court (excluding arbitration). If not, you and Microsoft consent to exclusive jurisdiction and venue in the Superior Court of King County, Washington for all disputes heard in court (excluding arbitration).

## 13. CONSUMER RIGHTS; REGIONAL VARIATIONS.
This agreement describes certain legal rights. You may have other rights, including consumer rights, under the laws of your state, province, or country. Separate and apart from your relationship with Microsoft, you may also have rights with respect to the party from which you acquired the software. This agreement does not change those other rights if the laws of your state, province, or country do not permit it to do so. For example, if you acquired the software in one of the below regions, or mandatory country law applies, then the following provisions apply to you:

**a)  Australia.** You have statutory guarantees under the Australian Consumer Law and nothing in this agreement is intended to affect those rights.

**b)  Canada.** If you acquired this software in Canada, you may stop receiving updates by turning off the automatic update feature, disconnecting your device from the Internet (if and when you re-connect to the Internet, however, the software will resume checking for and installing updates), or uninstalling the software. The product documentation, if any, may also specify how to turn off updates for your specific device or software.

**c)  Germany and Austria**.

**i. Warranty.** The properly licensed software will perform substantially as described in any Microsoft materials that accompany the software. However, Microsoft gives no contractual guarantee in relation to the licensed software. 
**ii. Limitation of Liability.** In case of intentional conduct, gross negligence, claims based on the Product Liability Act, as well as, in case of death or personal or physical injury, Microsoft is liable according to the statutory law.

Subject to the foregoing clause ii., Microsoft will only be liable for slight negligence if Microsoft is in breach of such material contractual obligations, the fulfillment of which facilitate the due performance of this agreement, the breach of which would endanger the purpose of this agreement and the compliance with which a party may constantly trust in (so-called \"cardinal obligations\"). In other cases of slight negligence, Microsoft will not be liable for slight negligence.

## 14. DISCLAIMER OF WARRANTY.
THE SOFTWARE IS LICENSED "AS IS." YOU BEAR THE RISK OF USING IT. MICROSOFT GIVES NO EXPRESS WARRANTIES, GUARANTEES, OR CONDITIONS. TO THE EXTENT PERMITTED UNDER APPLICABLE LAWS, MICROSOFT EXCLUDES ALL IMPLIED WARRANTIES, INCLUDING MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT.**

## 15. LIMITATION ON AND EXCLUSION OF DAMAGES.
IF YOU HAVE ANY BASIS FOR RECOVERING DAMAGES DESPITE THE PRECEDING DISCLAIMER OF WARRANTY, YOU CAN RECOVER FROM MICROSOFT AND ITS SUPPLIERS ONLY DIRECT DAMAGES UP TO U.S. \$5.00. YOU CANNOT RECOVER ANY OTHER DAMAGES, INCLUDING CONSEQUENTIAL, LOST PROFITS, SPECIAL, INDIRECT OR INCIDENTAL DAMAGES.**

This limitation applies to (a) anything related to the software, services, content (including code) on third party Internet sites, or third party applications; and (b) claims for breach of contract, warranty, guarantee, or condition; strict liability, negligence, or other tort; or any other claim; in each case to the extent permitted by applicable law.

It also applies even if Microsoft knew or should have known about the possibility of the damages. The above limitation or exclusion may not apply to you because your state, province, or country may not allow the exclusion or limitation of incidental, consequential, or other damages.


**Please note: As this software is distributed in Canada, some of the clauses in this agreement are provided below in French.**

**Remarque: Ce logiciel étant distribué au Canada, certaines des clauses dans ce contrat sont fournies ci-dessous en français.**

**EXONÉRATION DE GARANTIE. Le logiciel visé par une licence est offert « tel quel ». Toute utilisation de ce logiciel est à votre seule risque et péril. Microsoft n'accorde aucune autre garantie expresse. Vous pouvez bénéficier de droits additionnels en vertu du droit local sur la protection des consommateurs, que ce contrat ne peut modifier. La ou elles sont permises par le droit locale, les garanties implicites de qualité marchande, d'adéquation à un usage particulier et d'absence de contrefaçon sont exclues.**

**LIMITATION DES DOMMAGES-INTÉRÊTS ET EXCLUSION DE RESPONSABILITÉ POUR LES DOMMAGES. Vous pouvez obtenir de Microsoft et de ses fournisseurs une indemnisation en cas de dommages directs uniquement à hauteur de 5,00 \$ US. Vous ne pouvez prétendre à aucune indemnisation pour les autres dommages, y compris les dommages spéciaux, indirects ou accessoires et pertes de bénéfices.**

**Cette limitation concerne:**

**•    tout ce qui est relié au logiciel, aux services ou au contenu (y compris le code) figurant sur des sites Internet tiers ou dans des programmes tiers; et**

**•    les réclamations au titre de violation de contrat ou de garantie, ou au titre de responsabilité stricte, de négligence ou d'une autre faute dans la limite autorisée par la loi en vigueur.**

**Elle s'applique également, même si Microsoft connaissait ou devrait connaître l'éventualité d'un tel dommage. Si votre pays n'autorise pas l'exclusion ou la limitation de responsabilité pour les dommages indirects, accessoires ou de quelque nature que ce soit, il se peut que la limitation ou l'exclusion ci-dessus ne s'appliquera pas à votre égard.**

**EFFET JURIDIQUE. Le présent contrat décrit certains droits juridiques. Vous pourriez avoir d'autres droits prévus par les lois de votre pays. Le présent contrat ne modifie pas les droits que vous confèrent les lois de votre pays si celles-ci ne le permettent pas.**


---------------------------------------------------------

---------------------------------------------------------

org.json/json 20220924



---------------------------------------------------------

---------------------------------------------------------

typing-extensions 4.4.0 - Python-2.0.1


Copyright (c) 1995-2001 Corporation for National Research Initiatives
Copyright (c) 1991 - 1995, Stichting Mathematisch Centrum Amsterdam, The Netherlands
Copyright (c) 2001, 2002, 2003, 2004, 2005, 2006, 2007, 2008, 2009, 2010, 2011, 2012, 2013, 2014, 2015, 2016, 2017, 2018, 2019, 2020, 2021, 2022 Python Software Foundation

Python-2.0.1

----------------------------------------------------------

