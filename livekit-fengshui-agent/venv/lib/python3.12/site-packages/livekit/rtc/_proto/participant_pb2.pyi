"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
Copyright 2023 LiveKit, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""

import builtins
import collections.abc
import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.internal.enum_type_wrapper
import google.protobuf.message
from . import handle_pb2
import sys
import typing

if sys.version_info >= (3, 10):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

class _ParticipantKind:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _ParticipantKindEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_ParticipantKind.ValueType], builtins.type):
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    PARTICIPANT_KIND_STANDARD: _ParticipantKind.ValueType  # 0
    PARTICIPANT_KIND_INGRESS: _ParticipantKind.ValueType  # 1
    PARTICIPANT_KIND_EGRESS: _ParticipantKind.ValueType  # 2
    PARTICIPANT_KIND_SIP: _ParticipantKind.ValueType  # 3
    PARTICIPANT_KIND_AGENT: _ParticipantKind.ValueType  # 4

class ParticipantKind(_ParticipantKind, metaclass=_ParticipantKindEnumTypeWrapper): ...

PARTICIPANT_KIND_STANDARD: ParticipantKind.ValueType  # 0
PARTICIPANT_KIND_INGRESS: ParticipantKind.ValueType  # 1
PARTICIPANT_KIND_EGRESS: ParticipantKind.ValueType  # 2
PARTICIPANT_KIND_SIP: ParticipantKind.ValueType  # 3
PARTICIPANT_KIND_AGENT: ParticipantKind.ValueType  # 4
global___ParticipantKind = ParticipantKind

class _DisconnectReason:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _DisconnectReasonEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_DisconnectReason.ValueType], builtins.type):
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    UNKNOWN_REASON: _DisconnectReason.ValueType  # 0
    CLIENT_INITIATED: _DisconnectReason.ValueType  # 1
    """the client initiated the disconnect"""
    DUPLICATE_IDENTITY: _DisconnectReason.ValueType  # 2
    """another participant with the same identity has joined the room"""
    SERVER_SHUTDOWN: _DisconnectReason.ValueType  # 3
    """the server instance is shutting down"""
    PARTICIPANT_REMOVED: _DisconnectReason.ValueType  # 4
    """RoomService.RemoveParticipant was called"""
    ROOM_DELETED: _DisconnectReason.ValueType  # 5
    """RoomService.DeleteRoom was called"""
    STATE_MISMATCH: _DisconnectReason.ValueType  # 6
    """the client is attempting to resume a session, but server is not aware of it"""
    JOIN_FAILURE: _DisconnectReason.ValueType  # 7
    """client was unable to connect fully"""
    MIGRATION: _DisconnectReason.ValueType  # 8
    """Cloud-only, the server requested Participant to migrate the connection elsewhere"""
    SIGNAL_CLOSE: _DisconnectReason.ValueType  # 9
    """the signal websocket was closed unexpectedly"""
    ROOM_CLOSED: _DisconnectReason.ValueType  # 10
    """the room was closed, due to all Standard and Ingress participants having left"""
    USER_UNAVAILABLE: _DisconnectReason.ValueType  # 11
    """SIP callee did not respond in time"""
    USER_REJECTED: _DisconnectReason.ValueType  # 12
    """SIP callee rejected the call (busy)"""
    SIP_TRUNK_FAILURE: _DisconnectReason.ValueType  # 13
    """SIP protocol failure or unexpected response"""
    CONNECTION_TIMEOUT: _DisconnectReason.ValueType  # 14
    MEDIA_FAILURE: _DisconnectReason.ValueType  # 15

class DisconnectReason(_DisconnectReason, metaclass=_DisconnectReasonEnumTypeWrapper): ...

UNKNOWN_REASON: DisconnectReason.ValueType  # 0
CLIENT_INITIATED: DisconnectReason.ValueType  # 1
"""the client initiated the disconnect"""
DUPLICATE_IDENTITY: DisconnectReason.ValueType  # 2
"""another participant with the same identity has joined the room"""
SERVER_SHUTDOWN: DisconnectReason.ValueType  # 3
"""the server instance is shutting down"""
PARTICIPANT_REMOVED: DisconnectReason.ValueType  # 4
"""RoomService.RemoveParticipant was called"""
ROOM_DELETED: DisconnectReason.ValueType  # 5
"""RoomService.DeleteRoom was called"""
STATE_MISMATCH: DisconnectReason.ValueType  # 6
"""the client is attempting to resume a session, but server is not aware of it"""
JOIN_FAILURE: DisconnectReason.ValueType  # 7
"""client was unable to connect fully"""
MIGRATION: DisconnectReason.ValueType  # 8
"""Cloud-only, the server requested Participant to migrate the connection elsewhere"""
SIGNAL_CLOSE: DisconnectReason.ValueType  # 9
"""the signal websocket was closed unexpectedly"""
ROOM_CLOSED: DisconnectReason.ValueType  # 10
"""the room was closed, due to all Standard and Ingress participants having left"""
USER_UNAVAILABLE: DisconnectReason.ValueType  # 11
"""SIP callee did not respond in time"""
USER_REJECTED: DisconnectReason.ValueType  # 12
"""SIP callee rejected the call (busy)"""
SIP_TRUNK_FAILURE: DisconnectReason.ValueType  # 13
"""SIP protocol failure or unexpected response"""
CONNECTION_TIMEOUT: DisconnectReason.ValueType  # 14
MEDIA_FAILURE: DisconnectReason.ValueType  # 15
global___DisconnectReason = DisconnectReason

@typing.final
class ParticipantInfo(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    @typing.final
    class AttributesEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.str
        value: builtins.str
        def __init__(
            self,
            *,
            key: builtins.str | None = ...,
            value: builtins.str | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing.Literal["key", b"key", "value", b"value"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing.Literal["key", b"key", "value", b"value"]) -> None: ...

    SID_FIELD_NUMBER: builtins.int
    NAME_FIELD_NUMBER: builtins.int
    IDENTITY_FIELD_NUMBER: builtins.int
    METADATA_FIELD_NUMBER: builtins.int
    ATTRIBUTES_FIELD_NUMBER: builtins.int
    KIND_FIELD_NUMBER: builtins.int
    DISCONNECT_REASON_FIELD_NUMBER: builtins.int
    sid: builtins.str
    name: builtins.str
    identity: builtins.str
    metadata: builtins.str
    kind: global___ParticipantKind.ValueType
    disconnect_reason: global___DisconnectReason.ValueType
    @property
    def attributes(self) -> google.protobuf.internal.containers.ScalarMap[builtins.str, builtins.str]: ...
    def __init__(
        self,
        *,
        sid: builtins.str | None = ...,
        name: builtins.str | None = ...,
        identity: builtins.str | None = ...,
        metadata: builtins.str | None = ...,
        attributes: collections.abc.Mapping[builtins.str, builtins.str] | None = ...,
        kind: global___ParticipantKind.ValueType | None = ...,
        disconnect_reason: global___DisconnectReason.ValueType | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["disconnect_reason", b"disconnect_reason", "identity", b"identity", "kind", b"kind", "metadata", b"metadata", "name", b"name", "sid", b"sid"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["attributes", b"attributes", "disconnect_reason", b"disconnect_reason", "identity", b"identity", "kind", b"kind", "metadata", b"metadata", "name", b"name", "sid", b"sid"]) -> None: ...

global___ParticipantInfo = ParticipantInfo

@typing.final
class OwnedParticipant(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    HANDLE_FIELD_NUMBER: builtins.int
    INFO_FIELD_NUMBER: builtins.int
    @property
    def handle(self) -> handle_pb2.FfiOwnedHandle: ...
    @property
    def info(self) -> global___ParticipantInfo: ...
    def __init__(
        self,
        *,
        handle: handle_pb2.FfiOwnedHandle | None = ...,
        info: global___ParticipantInfo | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["handle", b"handle", "info", b"info"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["handle", b"handle", "info", b"info"]) -> None: ...

global___OwnedParticipant = OwnedParticipant
