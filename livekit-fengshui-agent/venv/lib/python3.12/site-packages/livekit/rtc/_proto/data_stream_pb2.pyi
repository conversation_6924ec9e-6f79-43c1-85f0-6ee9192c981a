"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
Copyright 2025 LiveKit, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""

import builtins
import collections.abc
import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.internal.enum_type_wrapper
import google.protobuf.message
from . import handle_pb2
import sys
import typing

if sys.version_info >= (3, 10):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

@typing.final
class OwnedTextStreamReader(google.protobuf.message.Message):
    """MARK: - Text stream reader

    A reader for an incoming stream.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    HANDLE_FIELD_NUMBER: builtins.int
    INFO_FIELD_NUMBER: builtins.int
    @property
    def handle(self) -> handle_pb2.FfiOwnedHandle: ...
    @property
    def info(self) -> global___TextStreamInfo: ...
    def __init__(
        self,
        *,
        handle: handle_pb2.FfiOwnedHandle | None = ...,
        info: global___TextStreamInfo | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["handle", b"handle", "info", b"info"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["handle", b"handle", "info", b"info"]) -> None: ...

global___OwnedTextStreamReader = OwnedTextStreamReader

@typing.final
class TextStreamReaderReadIncrementalRequest(google.protobuf.message.Message):
    """Reads an incoming text stream incrementally."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    READER_HANDLE_FIELD_NUMBER: builtins.int
    reader_handle: builtins.int
    def __init__(
        self,
        *,
        reader_handle: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["reader_handle", b"reader_handle"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["reader_handle", b"reader_handle"]) -> None: ...

global___TextStreamReaderReadIncrementalRequest = TextStreamReaderReadIncrementalRequest

@typing.final
class TextStreamReaderReadIncrementalResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    def __init__(
        self,
    ) -> None: ...

global___TextStreamReaderReadIncrementalResponse = TextStreamReaderReadIncrementalResponse

@typing.final
class TextStreamReaderReadAllRequest(google.protobuf.message.Message):
    """Reads an incoming text stream in its entirety."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    READER_HANDLE_FIELD_NUMBER: builtins.int
    reader_handle: builtins.int
    def __init__(
        self,
        *,
        reader_handle: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["reader_handle", b"reader_handle"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["reader_handle", b"reader_handle"]) -> None: ...

global___TextStreamReaderReadAllRequest = TextStreamReaderReadAllRequest

@typing.final
class TextStreamReaderReadAllResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_ID_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id"]) -> None: ...

global___TextStreamReaderReadAllResponse = TextStreamReaderReadAllResponse

@typing.final
class TextStreamReaderReadAllCallback(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_ID_FIELD_NUMBER: builtins.int
    CONTENT_FIELD_NUMBER: builtins.int
    ERROR_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    content: builtins.str
    @property
    def error(self) -> global___StreamError: ...
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
        content: builtins.str | None = ...,
        error: global___StreamError | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id", "content", b"content", "error", b"error", "result", b"result"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id", "content", b"content", "error", b"error", "result", b"result"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["result", b"result"]) -> typing.Literal["content", "error"] | None: ...

global___TextStreamReaderReadAllCallback = TextStreamReaderReadAllCallback

@typing.final
class TextStreamReaderEvent(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    READER_HANDLE_FIELD_NUMBER: builtins.int
    CHUNK_RECEIVED_FIELD_NUMBER: builtins.int
    EOS_FIELD_NUMBER: builtins.int
    reader_handle: builtins.int
    @property
    def chunk_received(self) -> global___TextStreamReaderChunkReceived: ...
    @property
    def eos(self) -> global___TextStreamReaderEOS: ...
    def __init__(
        self,
        *,
        reader_handle: builtins.int | None = ...,
        chunk_received: global___TextStreamReaderChunkReceived | None = ...,
        eos: global___TextStreamReaderEOS | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["chunk_received", b"chunk_received", "detail", b"detail", "eos", b"eos", "reader_handle", b"reader_handle"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["chunk_received", b"chunk_received", "detail", b"detail", "eos", b"eos", "reader_handle", b"reader_handle"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["detail", b"detail"]) -> typing.Literal["chunk_received", "eos"] | None: ...

global___TextStreamReaderEvent = TextStreamReaderEvent

@typing.final
class TextStreamReaderChunkReceived(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    CONTENT_FIELD_NUMBER: builtins.int
    content: builtins.str
    def __init__(
        self,
        *,
        content: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["content", b"content"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["content", b"content"]) -> None: ...

global___TextStreamReaderChunkReceived = TextStreamReaderChunkReceived

@typing.final
class TextStreamReaderEOS(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ERROR_FIELD_NUMBER: builtins.int
    @property
    def error(self) -> global___StreamError: ...
    def __init__(
        self,
        *,
        error: global___StreamError | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["error", b"error"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["error", b"error"]) -> None: ...

global___TextStreamReaderEOS = TextStreamReaderEOS

@typing.final
class OwnedByteStreamReader(google.protobuf.message.Message):
    """MARK: - Byte stream reader

    A reader for an incoming stream.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    HANDLE_FIELD_NUMBER: builtins.int
    INFO_FIELD_NUMBER: builtins.int
    @property
    def handle(self) -> handle_pb2.FfiOwnedHandle: ...
    @property
    def info(self) -> global___ByteStreamInfo: ...
    def __init__(
        self,
        *,
        handle: handle_pb2.FfiOwnedHandle | None = ...,
        info: global___ByteStreamInfo | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["handle", b"handle", "info", b"info"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["handle", b"handle", "info", b"info"]) -> None: ...

global___OwnedByteStreamReader = OwnedByteStreamReader

@typing.final
class ByteStreamReaderReadIncrementalRequest(google.protobuf.message.Message):
    """Reads an incoming byte stream incrementally."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    READER_HANDLE_FIELD_NUMBER: builtins.int
    reader_handle: builtins.int
    def __init__(
        self,
        *,
        reader_handle: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["reader_handle", b"reader_handle"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["reader_handle", b"reader_handle"]) -> None: ...

global___ByteStreamReaderReadIncrementalRequest = ByteStreamReaderReadIncrementalRequest

@typing.final
class ByteStreamReaderReadIncrementalResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    def __init__(
        self,
    ) -> None: ...

global___ByteStreamReaderReadIncrementalResponse = ByteStreamReaderReadIncrementalResponse

@typing.final
class ByteStreamReaderReadAllRequest(google.protobuf.message.Message):
    """Reads an incoming byte stream in its entirety."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    READER_HANDLE_FIELD_NUMBER: builtins.int
    reader_handle: builtins.int
    def __init__(
        self,
        *,
        reader_handle: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["reader_handle", b"reader_handle"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["reader_handle", b"reader_handle"]) -> None: ...

global___ByteStreamReaderReadAllRequest = ByteStreamReaderReadAllRequest

@typing.final
class ByteStreamReaderReadAllResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_ID_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id"]) -> None: ...

global___ByteStreamReaderReadAllResponse = ByteStreamReaderReadAllResponse

@typing.final
class ByteStreamReaderReadAllCallback(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_ID_FIELD_NUMBER: builtins.int
    CONTENT_FIELD_NUMBER: builtins.int
    ERROR_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    content: builtins.bytes
    @property
    def error(self) -> global___StreamError: ...
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
        content: builtins.bytes | None = ...,
        error: global___StreamError | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id", "content", b"content", "error", b"error", "result", b"result"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id", "content", b"content", "error", b"error", "result", b"result"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["result", b"result"]) -> typing.Literal["content", "error"] | None: ...

global___ByteStreamReaderReadAllCallback = ByteStreamReaderReadAllCallback

@typing.final
class ByteStreamReaderWriteToFileRequest(google.protobuf.message.Message):
    """Writes data from an incoming stream to a file as it arrives."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    READER_HANDLE_FIELD_NUMBER: builtins.int
    DIRECTORY_FIELD_NUMBER: builtins.int
    NAME_OVERRIDE_FIELD_NUMBER: builtins.int
    reader_handle: builtins.int
    directory: builtins.str
    """Directory to write the file in (must be writable by the current process).
    If not provided, the file will be written to the system's temp directory.
    """
    name_override: builtins.str
    """Name to use for the written file.
    If not provided, the file's name and extension will be inferred from
    the stream's info.
    """
    def __init__(
        self,
        *,
        reader_handle: builtins.int | None = ...,
        directory: builtins.str | None = ...,
        name_override: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["directory", b"directory", "name_override", b"name_override", "reader_handle", b"reader_handle"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["directory", b"directory", "name_override", b"name_override", "reader_handle", b"reader_handle"]) -> None: ...

global___ByteStreamReaderWriteToFileRequest = ByteStreamReaderWriteToFileRequest

@typing.final
class ByteStreamReaderWriteToFileResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_ID_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id"]) -> None: ...

global___ByteStreamReaderWriteToFileResponse = ByteStreamReaderWriteToFileResponse

@typing.final
class ByteStreamReaderWriteToFileCallback(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_ID_FIELD_NUMBER: builtins.int
    FILE_PATH_FIELD_NUMBER: builtins.int
    ERROR_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    file_path: builtins.str
    """Path the file was written to."""
    @property
    def error(self) -> global___StreamError: ...
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
        file_path: builtins.str | None = ...,
        error: global___StreamError | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id", "error", b"error", "file_path", b"file_path", "result", b"result"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id", "error", b"error", "file_path", b"file_path", "result", b"result"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["result", b"result"]) -> typing.Literal["file_path", "error"] | None: ...

global___ByteStreamReaderWriteToFileCallback = ByteStreamReaderWriteToFileCallback

@typing.final
class ByteStreamReaderEvent(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    READER_HANDLE_FIELD_NUMBER: builtins.int
    CHUNK_RECEIVED_FIELD_NUMBER: builtins.int
    EOS_FIELD_NUMBER: builtins.int
    reader_handle: builtins.int
    @property
    def chunk_received(self) -> global___ByteStreamReaderChunkReceived: ...
    @property
    def eos(self) -> global___ByteStreamReaderEOS: ...
    def __init__(
        self,
        *,
        reader_handle: builtins.int | None = ...,
        chunk_received: global___ByteStreamReaderChunkReceived | None = ...,
        eos: global___ByteStreamReaderEOS | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["chunk_received", b"chunk_received", "detail", b"detail", "eos", b"eos", "reader_handle", b"reader_handle"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["chunk_received", b"chunk_received", "detail", b"detail", "eos", b"eos", "reader_handle", b"reader_handle"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["detail", b"detail"]) -> typing.Literal["chunk_received", "eos"] | None: ...

global___ByteStreamReaderEvent = ByteStreamReaderEvent

@typing.final
class ByteStreamReaderChunkReceived(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    CONTENT_FIELD_NUMBER: builtins.int
    content: builtins.bytes
    def __init__(
        self,
        *,
        content: builtins.bytes | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["content", b"content"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["content", b"content"]) -> None: ...

global___ByteStreamReaderChunkReceived = ByteStreamReaderChunkReceived

@typing.final
class ByteStreamReaderEOS(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ERROR_FIELD_NUMBER: builtins.int
    @property
    def error(self) -> global___StreamError: ...
    def __init__(
        self,
        *,
        error: global___StreamError | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["error", b"error"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["error", b"error"]) -> None: ...

global___ByteStreamReaderEOS = ByteStreamReaderEOS

@typing.final
class StreamSendFileRequest(google.protobuf.message.Message):
    """MARK: - Send file

    Sends the contents of a file over a data stream.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LOCAL_PARTICIPANT_HANDLE_FIELD_NUMBER: builtins.int
    OPTIONS_FIELD_NUMBER: builtins.int
    FILE_PATH_FIELD_NUMBER: builtins.int
    local_participant_handle: builtins.int
    file_path: builtins.str
    """Path of the file to send (must be readable by the current process)."""
    @property
    def options(self) -> global___StreamByteOptions: ...
    def __init__(
        self,
        *,
        local_participant_handle: builtins.int | None = ...,
        options: global___StreamByteOptions | None = ...,
        file_path: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["file_path", b"file_path", "local_participant_handle", b"local_participant_handle", "options", b"options"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["file_path", b"file_path", "local_participant_handle", b"local_participant_handle", "options", b"options"]) -> None: ...

global___StreamSendFileRequest = StreamSendFileRequest

@typing.final
class StreamSendFileResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_ID_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id"]) -> None: ...

global___StreamSendFileResponse = StreamSendFileResponse

@typing.final
class StreamSendFileCallback(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_ID_FIELD_NUMBER: builtins.int
    INFO_FIELD_NUMBER: builtins.int
    ERROR_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    @property
    def info(self) -> global___ByteStreamInfo: ...
    @property
    def error(self) -> global___StreamError: ...
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
        info: global___ByteStreamInfo | None = ...,
        error: global___StreamError | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id", "error", b"error", "info", b"info", "result", b"result"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id", "error", b"error", "info", b"info", "result", b"result"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["result", b"result"]) -> typing.Literal["info", "error"] | None: ...

global___StreamSendFileCallback = StreamSendFileCallback

@typing.final
class StreamSendTextRequest(google.protobuf.message.Message):
    """MARK: - Send text

    Sends text over a data stream.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LOCAL_PARTICIPANT_HANDLE_FIELD_NUMBER: builtins.int
    OPTIONS_FIELD_NUMBER: builtins.int
    TEXT_FIELD_NUMBER: builtins.int
    local_participant_handle: builtins.int
    text: builtins.str
    """Text to send."""
    @property
    def options(self) -> global___StreamTextOptions: ...
    def __init__(
        self,
        *,
        local_participant_handle: builtins.int | None = ...,
        options: global___StreamTextOptions | None = ...,
        text: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["local_participant_handle", b"local_participant_handle", "options", b"options", "text", b"text"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["local_participant_handle", b"local_participant_handle", "options", b"options", "text", b"text"]) -> None: ...

global___StreamSendTextRequest = StreamSendTextRequest

@typing.final
class StreamSendTextResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_ID_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id"]) -> None: ...

global___StreamSendTextResponse = StreamSendTextResponse

@typing.final
class StreamSendTextCallback(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_ID_FIELD_NUMBER: builtins.int
    INFO_FIELD_NUMBER: builtins.int
    ERROR_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    @property
    def info(self) -> global___TextStreamInfo: ...
    @property
    def error(self) -> global___StreamError: ...
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
        info: global___TextStreamInfo | None = ...,
        error: global___StreamError | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id", "error", b"error", "info", b"info", "result", b"result"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id", "error", b"error", "info", b"info", "result", b"result"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["result", b"result"]) -> typing.Literal["info", "error"] | None: ...

global___StreamSendTextCallback = StreamSendTextCallback

@typing.final
class OwnedByteStreamWriter(google.protobuf.message.Message):
    """MARK: - Byte stream writer"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    HANDLE_FIELD_NUMBER: builtins.int
    INFO_FIELD_NUMBER: builtins.int
    @property
    def handle(self) -> handle_pb2.FfiOwnedHandle: ...
    @property
    def info(self) -> global___ByteStreamInfo: ...
    def __init__(
        self,
        *,
        handle: handle_pb2.FfiOwnedHandle | None = ...,
        info: global___ByteStreamInfo | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["handle", b"handle", "info", b"info"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["handle", b"handle", "info", b"info"]) -> None: ...

global___OwnedByteStreamWriter = OwnedByteStreamWriter

@typing.final
class ByteStreamOpenRequest(google.protobuf.message.Message):
    """Opens an outgoing stream.
    Call must be balanced with a StreamCloseRequest.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LOCAL_PARTICIPANT_HANDLE_FIELD_NUMBER: builtins.int
    OPTIONS_FIELD_NUMBER: builtins.int
    local_participant_handle: builtins.int
    @property
    def options(self) -> global___StreamByteOptions:
        """Options to use for opening the stream."""

    def __init__(
        self,
        *,
        local_participant_handle: builtins.int | None = ...,
        options: global___StreamByteOptions | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["local_participant_handle", b"local_participant_handle", "options", b"options"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["local_participant_handle", b"local_participant_handle", "options", b"options"]) -> None: ...

global___ByteStreamOpenRequest = ByteStreamOpenRequest

@typing.final
class ByteStreamOpenResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_ID_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id"]) -> None: ...

global___ByteStreamOpenResponse = ByteStreamOpenResponse

@typing.final
class ByteStreamOpenCallback(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_ID_FIELD_NUMBER: builtins.int
    WRITER_FIELD_NUMBER: builtins.int
    ERROR_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    @property
    def writer(self) -> global___OwnedByteStreamWriter: ...
    @property
    def error(self) -> global___StreamError: ...
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
        writer: global___OwnedByteStreamWriter | None = ...,
        error: global___StreamError | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id", "error", b"error", "result", b"result", "writer", b"writer"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id", "error", b"error", "result", b"result", "writer", b"writer"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["result", b"result"]) -> typing.Literal["writer", "error"] | None: ...

global___ByteStreamOpenCallback = ByteStreamOpenCallback

@typing.final
class ByteStreamWriterWriteRequest(google.protobuf.message.Message):
    """Writes data to a stream writer."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    WRITER_HANDLE_FIELD_NUMBER: builtins.int
    BYTES_FIELD_NUMBER: builtins.int
    writer_handle: builtins.int
    bytes: builtins.bytes
    def __init__(
        self,
        *,
        writer_handle: builtins.int | None = ...,
        bytes: builtins.bytes | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["bytes", b"bytes", "writer_handle", b"writer_handle"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["bytes", b"bytes", "writer_handle", b"writer_handle"]) -> None: ...

global___ByteStreamWriterWriteRequest = ByteStreamWriterWriteRequest

@typing.final
class ByteStreamWriterWriteResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_ID_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id"]) -> None: ...

global___ByteStreamWriterWriteResponse = ByteStreamWriterWriteResponse

@typing.final
class ByteStreamWriterWriteCallback(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_ID_FIELD_NUMBER: builtins.int
    ERROR_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    @property
    def error(self) -> global___StreamError: ...
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
        error: global___StreamError | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id", "error", b"error"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id", "error", b"error"]) -> None: ...

global___ByteStreamWriterWriteCallback = ByteStreamWriterWriteCallback

@typing.final
class ByteStreamWriterCloseRequest(google.protobuf.message.Message):
    """Closes a stream writer."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    WRITER_HANDLE_FIELD_NUMBER: builtins.int
    REASON_FIELD_NUMBER: builtins.int
    writer_handle: builtins.int
    reason: builtins.str
    def __init__(
        self,
        *,
        writer_handle: builtins.int | None = ...,
        reason: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["reason", b"reason", "writer_handle", b"writer_handle"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["reason", b"reason", "writer_handle", b"writer_handle"]) -> None: ...

global___ByteStreamWriterCloseRequest = ByteStreamWriterCloseRequest

@typing.final
class ByteStreamWriterCloseResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_ID_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id"]) -> None: ...

global___ByteStreamWriterCloseResponse = ByteStreamWriterCloseResponse

@typing.final
class ByteStreamWriterCloseCallback(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_ID_FIELD_NUMBER: builtins.int
    ERROR_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    @property
    def error(self) -> global___StreamError: ...
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
        error: global___StreamError | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id", "error", b"error"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id", "error", b"error"]) -> None: ...

global___ByteStreamWriterCloseCallback = ByteStreamWriterCloseCallback

@typing.final
class OwnedTextStreamWriter(google.protobuf.message.Message):
    """MARK: - Text stream writer"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    HANDLE_FIELD_NUMBER: builtins.int
    INFO_FIELD_NUMBER: builtins.int
    @property
    def handle(self) -> handle_pb2.FfiOwnedHandle: ...
    @property
    def info(self) -> global___TextStreamInfo: ...
    def __init__(
        self,
        *,
        handle: handle_pb2.FfiOwnedHandle | None = ...,
        info: global___TextStreamInfo | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["handle", b"handle", "info", b"info"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["handle", b"handle", "info", b"info"]) -> None: ...

global___OwnedTextStreamWriter = OwnedTextStreamWriter

@typing.final
class TextStreamOpenRequest(google.protobuf.message.Message):
    """Opens an outgoing text stream.
    Call must be balanced with a TextStreamCloseRequest.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LOCAL_PARTICIPANT_HANDLE_FIELD_NUMBER: builtins.int
    OPTIONS_FIELD_NUMBER: builtins.int
    local_participant_handle: builtins.int
    @property
    def options(self) -> global___StreamTextOptions:
        """Options to use for opening the stream."""

    def __init__(
        self,
        *,
        local_participant_handle: builtins.int | None = ...,
        options: global___StreamTextOptions | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["local_participant_handle", b"local_participant_handle", "options", b"options"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["local_participant_handle", b"local_participant_handle", "options", b"options"]) -> None: ...

global___TextStreamOpenRequest = TextStreamOpenRequest

@typing.final
class TextStreamOpenResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_ID_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id"]) -> None: ...

global___TextStreamOpenResponse = TextStreamOpenResponse

@typing.final
class TextStreamOpenCallback(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_ID_FIELD_NUMBER: builtins.int
    WRITER_FIELD_NUMBER: builtins.int
    ERROR_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    @property
    def writer(self) -> global___OwnedTextStreamWriter: ...
    @property
    def error(self) -> global___StreamError: ...
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
        writer: global___OwnedTextStreamWriter | None = ...,
        error: global___StreamError | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id", "error", b"error", "result", b"result", "writer", b"writer"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id", "error", b"error", "result", b"result", "writer", b"writer"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["result", b"result"]) -> typing.Literal["writer", "error"] | None: ...

global___TextStreamOpenCallback = TextStreamOpenCallback

@typing.final
class TextStreamWriterWriteRequest(google.protobuf.message.Message):
    """Writes text to a text stream writer."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    WRITER_HANDLE_FIELD_NUMBER: builtins.int
    TEXT_FIELD_NUMBER: builtins.int
    writer_handle: builtins.int
    text: builtins.str
    def __init__(
        self,
        *,
        writer_handle: builtins.int | None = ...,
        text: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["text", b"text", "writer_handle", b"writer_handle"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["text", b"text", "writer_handle", b"writer_handle"]) -> None: ...

global___TextStreamWriterWriteRequest = TextStreamWriterWriteRequest

@typing.final
class TextStreamWriterWriteResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_ID_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id"]) -> None: ...

global___TextStreamWriterWriteResponse = TextStreamWriterWriteResponse

@typing.final
class TextStreamWriterWriteCallback(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_ID_FIELD_NUMBER: builtins.int
    ERROR_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    @property
    def error(self) -> global___StreamError: ...
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
        error: global___StreamError | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id", "error", b"error"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id", "error", b"error"]) -> None: ...

global___TextStreamWriterWriteCallback = TextStreamWriterWriteCallback

@typing.final
class TextStreamWriterCloseRequest(google.protobuf.message.Message):
    """Closes a text stream writer."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    WRITER_HANDLE_FIELD_NUMBER: builtins.int
    REASON_FIELD_NUMBER: builtins.int
    writer_handle: builtins.int
    reason: builtins.str
    def __init__(
        self,
        *,
        writer_handle: builtins.int | None = ...,
        reason: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["reason", b"reason", "writer_handle", b"writer_handle"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["reason", b"reason", "writer_handle", b"writer_handle"]) -> None: ...

global___TextStreamWriterCloseRequest = TextStreamWriterCloseRequest

@typing.final
class TextStreamWriterCloseResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_ID_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id"]) -> None: ...

global___TextStreamWriterCloseResponse = TextStreamWriterCloseResponse

@typing.final
class TextStreamWriterCloseCallback(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_ID_FIELD_NUMBER: builtins.int
    ERROR_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    @property
    def error(self) -> global___StreamError: ...
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
        error: global___StreamError | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id", "error", b"error"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id", "error", b"error"]) -> None: ...

global___TextStreamWriterCloseCallback = TextStreamWriterCloseCallback

@typing.final
class TextStreamInfo(google.protobuf.message.Message):
    """Contains a subset of the fields from the stream header.
    Protocol-level fields not relevant to the FFI client are omitted (e.g. encryption info).
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _OperationType:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _OperationTypeEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[TextStreamInfo._OperationType.ValueType], builtins.type):
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        CREATE: TextStreamInfo._OperationType.ValueType  # 0
        UPDATE: TextStreamInfo._OperationType.ValueType  # 1
        DELETE: TextStreamInfo._OperationType.ValueType  # 2
        REACTION: TextStreamInfo._OperationType.ValueType  # 3

    class OperationType(_OperationType, metaclass=_OperationTypeEnumTypeWrapper): ...
    CREATE: TextStreamInfo.OperationType.ValueType  # 0
    UPDATE: TextStreamInfo.OperationType.ValueType  # 1
    DELETE: TextStreamInfo.OperationType.ValueType  # 2
    REACTION: TextStreamInfo.OperationType.ValueType  # 3

    @typing.final
    class AttributesEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.str
        value: builtins.str
        def __init__(
            self,
            *,
            key: builtins.str | None = ...,
            value: builtins.str | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing.Literal["key", b"key", "value", b"value"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing.Literal["key", b"key", "value", b"value"]) -> None: ...

    STREAM_ID_FIELD_NUMBER: builtins.int
    TIMESTAMP_FIELD_NUMBER: builtins.int
    MIME_TYPE_FIELD_NUMBER: builtins.int
    TOPIC_FIELD_NUMBER: builtins.int
    TOTAL_LENGTH_FIELD_NUMBER: builtins.int
    ATTRIBUTES_FIELD_NUMBER: builtins.int
    OPERATION_TYPE_FIELD_NUMBER: builtins.int
    VERSION_FIELD_NUMBER: builtins.int
    REPLY_TO_STREAM_ID_FIELD_NUMBER: builtins.int
    ATTACHED_STREAM_IDS_FIELD_NUMBER: builtins.int
    GENERATED_FIELD_NUMBER: builtins.int
    stream_id: builtins.str
    """unique identifier for this data stream"""
    timestamp: builtins.int
    """using int64 for Unix timestamp"""
    mime_type: builtins.str
    topic: builtins.str
    total_length: builtins.int
    """only populated for finite streams, if it's a stream of unknown size this stays empty"""
    operation_type: global___TextStreamInfo.OperationType.ValueType
    version: builtins.int
    """Optional: Version for updates/edits"""
    reply_to_stream_id: builtins.str
    """Optional: Reply to specific message"""
    generated: builtins.bool
    """true if the text has been generated by an agent from a participant's audio transcription"""
    @property
    def attributes(self) -> google.protobuf.internal.containers.ScalarMap[builtins.str, builtins.str]:
        """user defined attributes map that can carry additional info"""

    @property
    def attached_stream_ids(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]:
        """file attachments for text streams"""

    def __init__(
        self,
        *,
        stream_id: builtins.str | None = ...,
        timestamp: builtins.int | None = ...,
        mime_type: builtins.str | None = ...,
        topic: builtins.str | None = ...,
        total_length: builtins.int | None = ...,
        attributes: collections.abc.Mapping[builtins.str, builtins.str] | None = ...,
        operation_type: global___TextStreamInfo.OperationType.ValueType | None = ...,
        version: builtins.int | None = ...,
        reply_to_stream_id: builtins.str | None = ...,
        attached_stream_ids: collections.abc.Iterable[builtins.str] | None = ...,
        generated: builtins.bool | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["generated", b"generated", "mime_type", b"mime_type", "operation_type", b"operation_type", "reply_to_stream_id", b"reply_to_stream_id", "stream_id", b"stream_id", "timestamp", b"timestamp", "topic", b"topic", "total_length", b"total_length", "version", b"version"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["attached_stream_ids", b"attached_stream_ids", "attributes", b"attributes", "generated", b"generated", "mime_type", b"mime_type", "operation_type", b"operation_type", "reply_to_stream_id", b"reply_to_stream_id", "stream_id", b"stream_id", "timestamp", b"timestamp", "topic", b"topic", "total_length", b"total_length", "version", b"version"]) -> None: ...

global___TextStreamInfo = TextStreamInfo

@typing.final
class ByteStreamInfo(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    @typing.final
    class AttributesEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.str
        value: builtins.str
        def __init__(
            self,
            *,
            key: builtins.str | None = ...,
            value: builtins.str | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing.Literal["key", b"key", "value", b"value"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing.Literal["key", b"key", "value", b"value"]) -> None: ...

    STREAM_ID_FIELD_NUMBER: builtins.int
    TIMESTAMP_FIELD_NUMBER: builtins.int
    MIME_TYPE_FIELD_NUMBER: builtins.int
    TOPIC_FIELD_NUMBER: builtins.int
    TOTAL_LENGTH_FIELD_NUMBER: builtins.int
    ATTRIBUTES_FIELD_NUMBER: builtins.int
    NAME_FIELD_NUMBER: builtins.int
    stream_id: builtins.str
    """unique identifier for this data stream"""
    timestamp: builtins.int
    """using int64 for Unix timestamp"""
    mime_type: builtins.str
    topic: builtins.str
    total_length: builtins.int
    """only populated for finite streams, if it's a stream of unknown size this stays empty"""
    name: builtins.str
    @property
    def attributes(self) -> google.protobuf.internal.containers.ScalarMap[builtins.str, builtins.str]:
        """user defined attributes map that can carry additional info"""

    def __init__(
        self,
        *,
        stream_id: builtins.str | None = ...,
        timestamp: builtins.int | None = ...,
        mime_type: builtins.str | None = ...,
        topic: builtins.str | None = ...,
        total_length: builtins.int | None = ...,
        attributes: collections.abc.Mapping[builtins.str, builtins.str] | None = ...,
        name: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["mime_type", b"mime_type", "name", b"name", "stream_id", b"stream_id", "timestamp", b"timestamp", "topic", b"topic", "total_length", b"total_length"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["attributes", b"attributes", "mime_type", b"mime_type", "name", b"name", "stream_id", b"stream_id", "timestamp", b"timestamp", "topic", b"topic", "total_length", b"total_length"]) -> None: ...

global___ByteStreamInfo = ByteStreamInfo

@typing.final
class StreamTextOptions(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    @typing.final
    class AttributesEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.str
        value: builtins.str
        def __init__(
            self,
            *,
            key: builtins.str | None = ...,
            value: builtins.str | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing.Literal["key", b"key", "value", b"value"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing.Literal["key", b"key", "value", b"value"]) -> None: ...

    TOPIC_FIELD_NUMBER: builtins.int
    ATTRIBUTES_FIELD_NUMBER: builtins.int
    DESTINATION_IDENTITIES_FIELD_NUMBER: builtins.int
    ID_FIELD_NUMBER: builtins.int
    OPERATION_TYPE_FIELD_NUMBER: builtins.int
    VERSION_FIELD_NUMBER: builtins.int
    REPLY_TO_STREAM_ID_FIELD_NUMBER: builtins.int
    ATTACHED_STREAM_IDS_FIELD_NUMBER: builtins.int
    GENERATED_FIELD_NUMBER: builtins.int
    topic: builtins.str
    id: builtins.str
    operation_type: global___TextStreamInfo.OperationType.ValueType
    version: builtins.int
    reply_to_stream_id: builtins.str
    generated: builtins.bool
    @property
    def attributes(self) -> google.protobuf.internal.containers.ScalarMap[builtins.str, builtins.str]: ...
    @property
    def destination_identities(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    @property
    def attached_stream_ids(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    def __init__(
        self,
        *,
        topic: builtins.str | None = ...,
        attributes: collections.abc.Mapping[builtins.str, builtins.str] | None = ...,
        destination_identities: collections.abc.Iterable[builtins.str] | None = ...,
        id: builtins.str | None = ...,
        operation_type: global___TextStreamInfo.OperationType.ValueType | None = ...,
        version: builtins.int | None = ...,
        reply_to_stream_id: builtins.str | None = ...,
        attached_stream_ids: collections.abc.Iterable[builtins.str] | None = ...,
        generated: builtins.bool | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["generated", b"generated", "id", b"id", "operation_type", b"operation_type", "reply_to_stream_id", b"reply_to_stream_id", "topic", b"topic", "version", b"version"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["attached_stream_ids", b"attached_stream_ids", "attributes", b"attributes", "destination_identities", b"destination_identities", "generated", b"generated", "id", b"id", "operation_type", b"operation_type", "reply_to_stream_id", b"reply_to_stream_id", "topic", b"topic", "version", b"version"]) -> None: ...

global___StreamTextOptions = StreamTextOptions

@typing.final
class StreamByteOptions(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    @typing.final
    class AttributesEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.str
        value: builtins.str
        def __init__(
            self,
            *,
            key: builtins.str | None = ...,
            value: builtins.str | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing.Literal["key", b"key", "value", b"value"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing.Literal["key", b"key", "value", b"value"]) -> None: ...

    TOPIC_FIELD_NUMBER: builtins.int
    ATTRIBUTES_FIELD_NUMBER: builtins.int
    DESTINATION_IDENTITIES_FIELD_NUMBER: builtins.int
    ID_FIELD_NUMBER: builtins.int
    NAME_FIELD_NUMBER: builtins.int
    MIME_TYPE_FIELD_NUMBER: builtins.int
    TOTAL_LENGTH_FIELD_NUMBER: builtins.int
    topic: builtins.str
    id: builtins.str
    name: builtins.str
    mime_type: builtins.str
    total_length: builtins.int
    @property
    def attributes(self) -> google.protobuf.internal.containers.ScalarMap[builtins.str, builtins.str]: ...
    @property
    def destination_identities(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    def __init__(
        self,
        *,
        topic: builtins.str | None = ...,
        attributes: collections.abc.Mapping[builtins.str, builtins.str] | None = ...,
        destination_identities: collections.abc.Iterable[builtins.str] | None = ...,
        id: builtins.str | None = ...,
        name: builtins.str | None = ...,
        mime_type: builtins.str | None = ...,
        total_length: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["id", b"id", "mime_type", b"mime_type", "name", b"name", "topic", b"topic", "total_length", b"total_length"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["attributes", b"attributes", "destination_identities", b"destination_identities", "id", b"id", "mime_type", b"mime_type", "name", b"name", "topic", b"topic", "total_length", b"total_length"]) -> None: ...

global___StreamByteOptions = StreamByteOptions

@typing.final
class StreamError(google.protobuf.message.Message):
    """Error pertaining to a stream."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DESCRIPTION_FIELD_NUMBER: builtins.int
    description: builtins.str
    """TODO(ladvoc): make this an enum."""
    def __init__(
        self,
        *,
        description: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["description", b"description"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["description", b"description"]) -> None: ...

global___StreamError = StreamError
