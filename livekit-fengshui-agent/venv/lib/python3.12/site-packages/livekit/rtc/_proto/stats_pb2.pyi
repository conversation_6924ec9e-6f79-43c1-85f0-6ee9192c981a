"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
Copyright 2023 LiveKit, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""

import builtins
import collections.abc
import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.internal.enum_type_wrapper
import google.protobuf.message
import sys
import typing

if sys.version_info >= (3, 10):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

class _DataChannelState:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _DataChannelStateEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_DataChannelState.ValueType], builtins.type):
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    DC_CONNECTING: _DataChannelState.ValueType  # 0
    DC_OPEN: _DataChannelState.ValueType  # 1
    DC_CLOSING: _DataChannelState.ValueType  # 2
    DC_CLOSED: _DataChannelState.ValueType  # 3

class DataChannelState(_DataChannelState, metaclass=_DataChannelStateEnumTypeWrapper): ...

DC_CONNECTING: DataChannelState.ValueType  # 0
DC_OPEN: DataChannelState.ValueType  # 1
DC_CLOSING: DataChannelState.ValueType  # 2
DC_CLOSED: DataChannelState.ValueType  # 3
global___DataChannelState = DataChannelState

class _QualityLimitationReason:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _QualityLimitationReasonEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_QualityLimitationReason.ValueType], builtins.type):
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    LIMITATION_NONE: _QualityLimitationReason.ValueType  # 0
    LIMITATION_CPU: _QualityLimitationReason.ValueType  # 1
    LIMITATION_BANDWIDTH: _QualityLimitationReason.ValueType  # 2
    LIMITATION_OTHER: _QualityLimitationReason.ValueType  # 3

class QualityLimitationReason(_QualityLimitationReason, metaclass=_QualityLimitationReasonEnumTypeWrapper): ...

LIMITATION_NONE: QualityLimitationReason.ValueType  # 0
LIMITATION_CPU: QualityLimitationReason.ValueType  # 1
LIMITATION_BANDWIDTH: QualityLimitationReason.ValueType  # 2
LIMITATION_OTHER: QualityLimitationReason.ValueType  # 3
global___QualityLimitationReason = QualityLimitationReason

class _IceRole:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _IceRoleEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_IceRole.ValueType], builtins.type):
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    ICE_UNKNOWN: _IceRole.ValueType  # 0
    ICE_CONTROLLING: _IceRole.ValueType  # 1
    ICE_CONTROLLED: _IceRole.ValueType  # 2

class IceRole(_IceRole, metaclass=_IceRoleEnumTypeWrapper): ...

ICE_UNKNOWN: IceRole.ValueType  # 0
ICE_CONTROLLING: IceRole.ValueType  # 1
ICE_CONTROLLED: IceRole.ValueType  # 2
global___IceRole = IceRole

class _DtlsTransportState:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _DtlsTransportStateEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_DtlsTransportState.ValueType], builtins.type):
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    DTLS_TRANSPORT_NEW: _DtlsTransportState.ValueType  # 0
    DTLS_TRANSPORT_CONNECTING: _DtlsTransportState.ValueType  # 1
    DTLS_TRANSPORT_CONNECTED: _DtlsTransportState.ValueType  # 2
    DTLS_TRANSPORT_CLOSED: _DtlsTransportState.ValueType  # 3
    DTLS_TRANSPORT_FAILED: _DtlsTransportState.ValueType  # 4

class DtlsTransportState(_DtlsTransportState, metaclass=_DtlsTransportStateEnumTypeWrapper): ...

DTLS_TRANSPORT_NEW: DtlsTransportState.ValueType  # 0
DTLS_TRANSPORT_CONNECTING: DtlsTransportState.ValueType  # 1
DTLS_TRANSPORT_CONNECTED: DtlsTransportState.ValueType  # 2
DTLS_TRANSPORT_CLOSED: DtlsTransportState.ValueType  # 3
DTLS_TRANSPORT_FAILED: DtlsTransportState.ValueType  # 4
global___DtlsTransportState = DtlsTransportState

class _IceTransportState:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _IceTransportStateEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_IceTransportState.ValueType], builtins.type):
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    ICE_TRANSPORT_NEW: _IceTransportState.ValueType  # 0
    ICE_TRANSPORT_CHECKING: _IceTransportState.ValueType  # 1
    ICE_TRANSPORT_CONNECTED: _IceTransportState.ValueType  # 2
    ICE_TRANSPORT_COMPLETED: _IceTransportState.ValueType  # 3
    ICE_TRANSPORT_DISCONNECTED: _IceTransportState.ValueType  # 4
    ICE_TRANSPORT_FAILED: _IceTransportState.ValueType  # 5
    ICE_TRANSPORT_CLOSED: _IceTransportState.ValueType  # 6

class IceTransportState(_IceTransportState, metaclass=_IceTransportStateEnumTypeWrapper): ...

ICE_TRANSPORT_NEW: IceTransportState.ValueType  # 0
ICE_TRANSPORT_CHECKING: IceTransportState.ValueType  # 1
ICE_TRANSPORT_CONNECTED: IceTransportState.ValueType  # 2
ICE_TRANSPORT_COMPLETED: IceTransportState.ValueType  # 3
ICE_TRANSPORT_DISCONNECTED: IceTransportState.ValueType  # 4
ICE_TRANSPORT_FAILED: IceTransportState.ValueType  # 5
ICE_TRANSPORT_CLOSED: IceTransportState.ValueType  # 6
global___IceTransportState = IceTransportState

class _DtlsRole:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _DtlsRoleEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_DtlsRole.ValueType], builtins.type):
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    DTLS_CLIENT: _DtlsRole.ValueType  # 0
    DTLS_SERVER: _DtlsRole.ValueType  # 1
    DTLS_UNKNOWN: _DtlsRole.ValueType  # 2

class DtlsRole(_DtlsRole, metaclass=_DtlsRoleEnumTypeWrapper): ...

DTLS_CLIENT: DtlsRole.ValueType  # 0
DTLS_SERVER: DtlsRole.ValueType  # 1
DTLS_UNKNOWN: DtlsRole.ValueType  # 2
global___DtlsRole = DtlsRole

class _IceCandidatePairState:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _IceCandidatePairStateEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_IceCandidatePairState.ValueType], builtins.type):
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    PAIR_FROZEN: _IceCandidatePairState.ValueType  # 0
    PAIR_WAITING: _IceCandidatePairState.ValueType  # 1
    PAIR_IN_PROGRESS: _IceCandidatePairState.ValueType  # 2
    PAIR_FAILED: _IceCandidatePairState.ValueType  # 3
    PAIR_SUCCEEDED: _IceCandidatePairState.ValueType  # 4

class IceCandidatePairState(_IceCandidatePairState, metaclass=_IceCandidatePairStateEnumTypeWrapper): ...

PAIR_FROZEN: IceCandidatePairState.ValueType  # 0
PAIR_WAITING: IceCandidatePairState.ValueType  # 1
PAIR_IN_PROGRESS: IceCandidatePairState.ValueType  # 2
PAIR_FAILED: IceCandidatePairState.ValueType  # 3
PAIR_SUCCEEDED: IceCandidatePairState.ValueType  # 4
global___IceCandidatePairState = IceCandidatePairState

class _IceCandidateType:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _IceCandidateTypeEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_IceCandidateType.ValueType], builtins.type):
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    HOST: _IceCandidateType.ValueType  # 0
    SRFLX: _IceCandidateType.ValueType  # 1
    PRFLX: _IceCandidateType.ValueType  # 2
    RELAY: _IceCandidateType.ValueType  # 3

class IceCandidateType(_IceCandidateType, metaclass=_IceCandidateTypeEnumTypeWrapper): ...

HOST: IceCandidateType.ValueType  # 0
SRFLX: IceCandidateType.ValueType  # 1
PRFLX: IceCandidateType.ValueType  # 2
RELAY: IceCandidateType.ValueType  # 3
global___IceCandidateType = IceCandidateType

class _IceServerTransportProtocol:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _IceServerTransportProtocolEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_IceServerTransportProtocol.ValueType], builtins.type):
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    TRANSPORT_UDP: _IceServerTransportProtocol.ValueType  # 0
    TRANSPORT_TCP: _IceServerTransportProtocol.ValueType  # 1
    TRANSPORT_TLS: _IceServerTransportProtocol.ValueType  # 2

class IceServerTransportProtocol(_IceServerTransportProtocol, metaclass=_IceServerTransportProtocolEnumTypeWrapper): ...

TRANSPORT_UDP: IceServerTransportProtocol.ValueType  # 0
TRANSPORT_TCP: IceServerTransportProtocol.ValueType  # 1
TRANSPORT_TLS: IceServerTransportProtocol.ValueType  # 2
global___IceServerTransportProtocol = IceServerTransportProtocol

class _IceTcpCandidateType:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _IceTcpCandidateTypeEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_IceTcpCandidateType.ValueType], builtins.type):
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    CANDIDATE_ACTIVE: _IceTcpCandidateType.ValueType  # 0
    CANDIDATE_PASSIVE: _IceTcpCandidateType.ValueType  # 1
    CANDIDATE_SO: _IceTcpCandidateType.ValueType  # 2

class IceTcpCandidateType(_IceTcpCandidateType, metaclass=_IceTcpCandidateTypeEnumTypeWrapper): ...

CANDIDATE_ACTIVE: IceTcpCandidateType.ValueType  # 0
CANDIDATE_PASSIVE: IceTcpCandidateType.ValueType  # 1
CANDIDATE_SO: IceTcpCandidateType.ValueType  # 2
global___IceTcpCandidateType = IceTcpCandidateType

@typing.final
class RtcStats(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    @typing.final
    class Codec(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        RTC_FIELD_NUMBER: builtins.int
        CODEC_FIELD_NUMBER: builtins.int
        @property
        def rtc(self) -> global___RtcStatsData: ...
        @property
        def codec(self) -> global___CodecStats: ...
        def __init__(
            self,
            *,
            rtc: global___RtcStatsData | None = ...,
            codec: global___CodecStats | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing.Literal["codec", b"codec", "rtc", b"rtc"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing.Literal["codec", b"codec", "rtc", b"rtc"]) -> None: ...

    @typing.final
    class InboundRtp(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        RTC_FIELD_NUMBER: builtins.int
        STREAM_FIELD_NUMBER: builtins.int
        RECEIVED_FIELD_NUMBER: builtins.int
        INBOUND_FIELD_NUMBER: builtins.int
        @property
        def rtc(self) -> global___RtcStatsData: ...
        @property
        def stream(self) -> global___RtpStreamStats: ...
        @property
        def received(self) -> global___ReceivedRtpStreamStats: ...
        @property
        def inbound(self) -> global___InboundRtpStreamStats: ...
        def __init__(
            self,
            *,
            rtc: global___RtcStatsData | None = ...,
            stream: global___RtpStreamStats | None = ...,
            received: global___ReceivedRtpStreamStats | None = ...,
            inbound: global___InboundRtpStreamStats | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing.Literal["inbound", b"inbound", "received", b"received", "rtc", b"rtc", "stream", b"stream"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing.Literal["inbound", b"inbound", "received", b"received", "rtc", b"rtc", "stream", b"stream"]) -> None: ...

    @typing.final
    class OutboundRtp(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        RTC_FIELD_NUMBER: builtins.int
        STREAM_FIELD_NUMBER: builtins.int
        SENT_FIELD_NUMBER: builtins.int
        OUTBOUND_FIELD_NUMBER: builtins.int
        @property
        def rtc(self) -> global___RtcStatsData: ...
        @property
        def stream(self) -> global___RtpStreamStats: ...
        @property
        def sent(self) -> global___SentRtpStreamStats: ...
        @property
        def outbound(self) -> global___OutboundRtpStreamStats: ...
        def __init__(
            self,
            *,
            rtc: global___RtcStatsData | None = ...,
            stream: global___RtpStreamStats | None = ...,
            sent: global___SentRtpStreamStats | None = ...,
            outbound: global___OutboundRtpStreamStats | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing.Literal["outbound", b"outbound", "rtc", b"rtc", "sent", b"sent", "stream", b"stream"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing.Literal["outbound", b"outbound", "rtc", b"rtc", "sent", b"sent", "stream", b"stream"]) -> None: ...

    @typing.final
    class RemoteInboundRtp(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        RTC_FIELD_NUMBER: builtins.int
        STREAM_FIELD_NUMBER: builtins.int
        RECEIVED_FIELD_NUMBER: builtins.int
        REMOTE_INBOUND_FIELD_NUMBER: builtins.int
        @property
        def rtc(self) -> global___RtcStatsData: ...
        @property
        def stream(self) -> global___RtpStreamStats: ...
        @property
        def received(self) -> global___ReceivedRtpStreamStats: ...
        @property
        def remote_inbound(self) -> global___RemoteInboundRtpStreamStats: ...
        def __init__(
            self,
            *,
            rtc: global___RtcStatsData | None = ...,
            stream: global___RtpStreamStats | None = ...,
            received: global___ReceivedRtpStreamStats | None = ...,
            remote_inbound: global___RemoteInboundRtpStreamStats | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing.Literal["received", b"received", "remote_inbound", b"remote_inbound", "rtc", b"rtc", "stream", b"stream"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing.Literal["received", b"received", "remote_inbound", b"remote_inbound", "rtc", b"rtc", "stream", b"stream"]) -> None: ...

    @typing.final
    class RemoteOutboundRtp(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        RTC_FIELD_NUMBER: builtins.int
        STREAM_FIELD_NUMBER: builtins.int
        SENT_FIELD_NUMBER: builtins.int
        REMOTE_OUTBOUND_FIELD_NUMBER: builtins.int
        @property
        def rtc(self) -> global___RtcStatsData: ...
        @property
        def stream(self) -> global___RtpStreamStats: ...
        @property
        def sent(self) -> global___SentRtpStreamStats: ...
        @property
        def remote_outbound(self) -> global___RemoteOutboundRtpStreamStats: ...
        def __init__(
            self,
            *,
            rtc: global___RtcStatsData | None = ...,
            stream: global___RtpStreamStats | None = ...,
            sent: global___SentRtpStreamStats | None = ...,
            remote_outbound: global___RemoteOutboundRtpStreamStats | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing.Literal["remote_outbound", b"remote_outbound", "rtc", b"rtc", "sent", b"sent", "stream", b"stream"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing.Literal["remote_outbound", b"remote_outbound", "rtc", b"rtc", "sent", b"sent", "stream", b"stream"]) -> None: ...

    @typing.final
    class MediaSource(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        RTC_FIELD_NUMBER: builtins.int
        SOURCE_FIELD_NUMBER: builtins.int
        AUDIO_FIELD_NUMBER: builtins.int
        VIDEO_FIELD_NUMBER: builtins.int
        @property
        def rtc(self) -> global___RtcStatsData: ...
        @property
        def source(self) -> global___MediaSourceStats: ...
        @property
        def audio(self) -> global___AudioSourceStats: ...
        @property
        def video(self) -> global___VideoSourceStats: ...
        def __init__(
            self,
            *,
            rtc: global___RtcStatsData | None = ...,
            source: global___MediaSourceStats | None = ...,
            audio: global___AudioSourceStats | None = ...,
            video: global___VideoSourceStats | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing.Literal["audio", b"audio", "rtc", b"rtc", "source", b"source", "video", b"video"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing.Literal["audio", b"audio", "rtc", b"rtc", "source", b"source", "video", b"video"]) -> None: ...

    @typing.final
    class MediaPlayout(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        RTC_FIELD_NUMBER: builtins.int
        AUDIO_PLAYOUT_FIELD_NUMBER: builtins.int
        @property
        def rtc(self) -> global___RtcStatsData: ...
        @property
        def audio_playout(self) -> global___AudioPlayoutStats: ...
        def __init__(
            self,
            *,
            rtc: global___RtcStatsData | None = ...,
            audio_playout: global___AudioPlayoutStats | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing.Literal["audio_playout", b"audio_playout", "rtc", b"rtc"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing.Literal["audio_playout", b"audio_playout", "rtc", b"rtc"]) -> None: ...

    @typing.final
    class PeerConnection(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        RTC_FIELD_NUMBER: builtins.int
        PC_FIELD_NUMBER: builtins.int
        @property
        def rtc(self) -> global___RtcStatsData: ...
        @property
        def pc(self) -> global___PeerConnectionStats: ...
        def __init__(
            self,
            *,
            rtc: global___RtcStatsData | None = ...,
            pc: global___PeerConnectionStats | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing.Literal["pc", b"pc", "rtc", b"rtc"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing.Literal["pc", b"pc", "rtc", b"rtc"]) -> None: ...

    @typing.final
    class DataChannel(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        RTC_FIELD_NUMBER: builtins.int
        DC_FIELD_NUMBER: builtins.int
        @property
        def rtc(self) -> global___RtcStatsData: ...
        @property
        def dc(self) -> global___DataChannelStats: ...
        def __init__(
            self,
            *,
            rtc: global___RtcStatsData | None = ...,
            dc: global___DataChannelStats | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing.Literal["dc", b"dc", "rtc", b"rtc"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing.Literal["dc", b"dc", "rtc", b"rtc"]) -> None: ...

    @typing.final
    class Transport(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        RTC_FIELD_NUMBER: builtins.int
        TRANSPORT_FIELD_NUMBER: builtins.int
        @property
        def rtc(self) -> global___RtcStatsData: ...
        @property
        def transport(self) -> global___TransportStats: ...
        def __init__(
            self,
            *,
            rtc: global___RtcStatsData | None = ...,
            transport: global___TransportStats | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing.Literal["rtc", b"rtc", "transport", b"transport"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing.Literal["rtc", b"rtc", "transport", b"transport"]) -> None: ...

    @typing.final
    class CandidatePair(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        RTC_FIELD_NUMBER: builtins.int
        CANDIDATE_PAIR_FIELD_NUMBER: builtins.int
        @property
        def rtc(self) -> global___RtcStatsData: ...
        @property
        def candidate_pair(self) -> global___CandidatePairStats: ...
        def __init__(
            self,
            *,
            rtc: global___RtcStatsData | None = ...,
            candidate_pair: global___CandidatePairStats | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing.Literal["candidate_pair", b"candidate_pair", "rtc", b"rtc"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing.Literal["candidate_pair", b"candidate_pair", "rtc", b"rtc"]) -> None: ...

    @typing.final
    class LocalCandidate(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        RTC_FIELD_NUMBER: builtins.int
        CANDIDATE_FIELD_NUMBER: builtins.int
        @property
        def rtc(self) -> global___RtcStatsData: ...
        @property
        def candidate(self) -> global___IceCandidateStats: ...
        def __init__(
            self,
            *,
            rtc: global___RtcStatsData | None = ...,
            candidate: global___IceCandidateStats | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing.Literal["candidate", b"candidate", "rtc", b"rtc"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing.Literal["candidate", b"candidate", "rtc", b"rtc"]) -> None: ...

    @typing.final
    class RemoteCandidate(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        RTC_FIELD_NUMBER: builtins.int
        CANDIDATE_FIELD_NUMBER: builtins.int
        @property
        def rtc(self) -> global___RtcStatsData: ...
        @property
        def candidate(self) -> global___IceCandidateStats: ...
        def __init__(
            self,
            *,
            rtc: global___RtcStatsData | None = ...,
            candidate: global___IceCandidateStats | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing.Literal["candidate", b"candidate", "rtc", b"rtc"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing.Literal["candidate", b"candidate", "rtc", b"rtc"]) -> None: ...

    @typing.final
    class Certificate(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        RTC_FIELD_NUMBER: builtins.int
        CERTIFICATE_FIELD_NUMBER: builtins.int
        @property
        def rtc(self) -> global___RtcStatsData: ...
        @property
        def certificate(self) -> global___CertificateStats: ...
        def __init__(
            self,
            *,
            rtc: global___RtcStatsData | None = ...,
            certificate: global___CertificateStats | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing.Literal["certificate", b"certificate", "rtc", b"rtc"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing.Literal["certificate", b"certificate", "rtc", b"rtc"]) -> None: ...

    @typing.final
    class Stream(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        RTC_FIELD_NUMBER: builtins.int
        STREAM_FIELD_NUMBER: builtins.int
        @property
        def rtc(self) -> global___RtcStatsData: ...
        @property
        def stream(self) -> global___StreamStats: ...
        def __init__(
            self,
            *,
            rtc: global___RtcStatsData | None = ...,
            stream: global___StreamStats | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing.Literal["rtc", b"rtc", "stream", b"stream"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing.Literal["rtc", b"rtc", "stream", b"stream"]) -> None: ...

    @typing.final
    class Track(google.protobuf.message.Message):
        """Deprecated"""

        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        def __init__(
            self,
        ) -> None: ...

    CODEC_FIELD_NUMBER: builtins.int
    INBOUND_RTP_FIELD_NUMBER: builtins.int
    OUTBOUND_RTP_FIELD_NUMBER: builtins.int
    REMOTE_INBOUND_RTP_FIELD_NUMBER: builtins.int
    REMOTE_OUTBOUND_RTP_FIELD_NUMBER: builtins.int
    MEDIA_SOURCE_FIELD_NUMBER: builtins.int
    MEDIA_PLAYOUT_FIELD_NUMBER: builtins.int
    PEER_CONNECTION_FIELD_NUMBER: builtins.int
    DATA_CHANNEL_FIELD_NUMBER: builtins.int
    TRANSPORT_FIELD_NUMBER: builtins.int
    CANDIDATE_PAIR_FIELD_NUMBER: builtins.int
    LOCAL_CANDIDATE_FIELD_NUMBER: builtins.int
    REMOTE_CANDIDATE_FIELD_NUMBER: builtins.int
    CERTIFICATE_FIELD_NUMBER: builtins.int
    STREAM_FIELD_NUMBER: builtins.int
    TRACK_FIELD_NUMBER: builtins.int
    @property
    def codec(self) -> global___RtcStats.Codec: ...
    @property
    def inbound_rtp(self) -> global___RtcStats.InboundRtp: ...
    @property
    def outbound_rtp(self) -> global___RtcStats.OutboundRtp: ...
    @property
    def remote_inbound_rtp(self) -> global___RtcStats.RemoteInboundRtp: ...
    @property
    def remote_outbound_rtp(self) -> global___RtcStats.RemoteOutboundRtp: ...
    @property
    def media_source(self) -> global___RtcStats.MediaSource: ...
    @property
    def media_playout(self) -> global___RtcStats.MediaPlayout: ...
    @property
    def peer_connection(self) -> global___RtcStats.PeerConnection: ...
    @property
    def data_channel(self) -> global___RtcStats.DataChannel: ...
    @property
    def transport(self) -> global___RtcStats.Transport: ...
    @property
    def candidate_pair(self) -> global___RtcStats.CandidatePair: ...
    @property
    def local_candidate(self) -> global___RtcStats.LocalCandidate: ...
    @property
    def remote_candidate(self) -> global___RtcStats.RemoteCandidate: ...
    @property
    def certificate(self) -> global___RtcStats.Certificate: ...
    @property
    def stream(self) -> global___RtcStats.Stream: ...
    @property
    def track(self) -> global___RtcStats.Track: ...
    def __init__(
        self,
        *,
        codec: global___RtcStats.Codec | None = ...,
        inbound_rtp: global___RtcStats.InboundRtp | None = ...,
        outbound_rtp: global___RtcStats.OutboundRtp | None = ...,
        remote_inbound_rtp: global___RtcStats.RemoteInboundRtp | None = ...,
        remote_outbound_rtp: global___RtcStats.RemoteOutboundRtp | None = ...,
        media_source: global___RtcStats.MediaSource | None = ...,
        media_playout: global___RtcStats.MediaPlayout | None = ...,
        peer_connection: global___RtcStats.PeerConnection | None = ...,
        data_channel: global___RtcStats.DataChannel | None = ...,
        transport: global___RtcStats.Transport | None = ...,
        candidate_pair: global___RtcStats.CandidatePair | None = ...,
        local_candidate: global___RtcStats.LocalCandidate | None = ...,
        remote_candidate: global___RtcStats.RemoteCandidate | None = ...,
        certificate: global___RtcStats.Certificate | None = ...,
        stream: global___RtcStats.Stream | None = ...,
        track: global___RtcStats.Track | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["candidate_pair", b"candidate_pair", "certificate", b"certificate", "codec", b"codec", "data_channel", b"data_channel", "inbound_rtp", b"inbound_rtp", "local_candidate", b"local_candidate", "media_playout", b"media_playout", "media_source", b"media_source", "outbound_rtp", b"outbound_rtp", "peer_connection", b"peer_connection", "remote_candidate", b"remote_candidate", "remote_inbound_rtp", b"remote_inbound_rtp", "remote_outbound_rtp", b"remote_outbound_rtp", "stats", b"stats", "stream", b"stream", "track", b"track", "transport", b"transport"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["candidate_pair", b"candidate_pair", "certificate", b"certificate", "codec", b"codec", "data_channel", b"data_channel", "inbound_rtp", b"inbound_rtp", "local_candidate", b"local_candidate", "media_playout", b"media_playout", "media_source", b"media_source", "outbound_rtp", b"outbound_rtp", "peer_connection", b"peer_connection", "remote_candidate", b"remote_candidate", "remote_inbound_rtp", b"remote_inbound_rtp", "remote_outbound_rtp", b"remote_outbound_rtp", "stats", b"stats", "stream", b"stream", "track", b"track", "transport", b"transport"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["stats", b"stats"]) -> typing.Literal["codec", "inbound_rtp", "outbound_rtp", "remote_inbound_rtp", "remote_outbound_rtp", "media_source", "media_playout", "peer_connection", "data_channel", "transport", "candidate_pair", "local_candidate", "remote_candidate", "certificate", "stream", "track"] | None: ...

global___RtcStats = RtcStats

@typing.final
class RtcStatsData(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ID_FIELD_NUMBER: builtins.int
    TIMESTAMP_FIELD_NUMBER: builtins.int
    id: builtins.str
    timestamp: builtins.int
    def __init__(
        self,
        *,
        id: builtins.str | None = ...,
        timestamp: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["id", b"id", "timestamp", b"timestamp"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["id", b"id", "timestamp", b"timestamp"]) -> None: ...

global___RtcStatsData = RtcStatsData

@typing.final
class CodecStats(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PAYLOAD_TYPE_FIELD_NUMBER: builtins.int
    TRANSPORT_ID_FIELD_NUMBER: builtins.int
    MIME_TYPE_FIELD_NUMBER: builtins.int
    CLOCK_RATE_FIELD_NUMBER: builtins.int
    CHANNELS_FIELD_NUMBER: builtins.int
    SDP_FMTP_LINE_FIELD_NUMBER: builtins.int
    payload_type: builtins.int
    transport_id: builtins.str
    mime_type: builtins.str
    clock_rate: builtins.int
    channels: builtins.int
    sdp_fmtp_line: builtins.str
    def __init__(
        self,
        *,
        payload_type: builtins.int | None = ...,
        transport_id: builtins.str | None = ...,
        mime_type: builtins.str | None = ...,
        clock_rate: builtins.int | None = ...,
        channels: builtins.int | None = ...,
        sdp_fmtp_line: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["channels", b"channels", "clock_rate", b"clock_rate", "mime_type", b"mime_type", "payload_type", b"payload_type", "sdp_fmtp_line", b"sdp_fmtp_line", "transport_id", b"transport_id"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["channels", b"channels", "clock_rate", b"clock_rate", "mime_type", b"mime_type", "payload_type", b"payload_type", "sdp_fmtp_line", b"sdp_fmtp_line", "transport_id", b"transport_id"]) -> None: ...

global___CodecStats = CodecStats

@typing.final
class RtpStreamStats(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SSRC_FIELD_NUMBER: builtins.int
    KIND_FIELD_NUMBER: builtins.int
    TRANSPORT_ID_FIELD_NUMBER: builtins.int
    CODEC_ID_FIELD_NUMBER: builtins.int
    ssrc: builtins.int
    kind: builtins.str
    transport_id: builtins.str
    codec_id: builtins.str
    def __init__(
        self,
        *,
        ssrc: builtins.int | None = ...,
        kind: builtins.str | None = ...,
        transport_id: builtins.str | None = ...,
        codec_id: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["codec_id", b"codec_id", "kind", b"kind", "ssrc", b"ssrc", "transport_id", b"transport_id"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["codec_id", b"codec_id", "kind", b"kind", "ssrc", b"ssrc", "transport_id", b"transport_id"]) -> None: ...

global___RtpStreamStats = RtpStreamStats

@typing.final
class ReceivedRtpStreamStats(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PACKETS_RECEIVED_FIELD_NUMBER: builtins.int
    PACKETS_LOST_FIELD_NUMBER: builtins.int
    JITTER_FIELD_NUMBER: builtins.int
    packets_received: builtins.int
    packets_lost: builtins.int
    jitter: builtins.float
    def __init__(
        self,
        *,
        packets_received: builtins.int | None = ...,
        packets_lost: builtins.int | None = ...,
        jitter: builtins.float | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["jitter", b"jitter", "packets_lost", b"packets_lost", "packets_received", b"packets_received"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["jitter", b"jitter", "packets_lost", b"packets_lost", "packets_received", b"packets_received"]) -> None: ...

global___ReceivedRtpStreamStats = ReceivedRtpStreamStats

@typing.final
class InboundRtpStreamStats(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TRACK_IDENTIFIER_FIELD_NUMBER: builtins.int
    MID_FIELD_NUMBER: builtins.int
    REMOTE_ID_FIELD_NUMBER: builtins.int
    FRAMES_DECODED_FIELD_NUMBER: builtins.int
    KEY_FRAMES_DECODED_FIELD_NUMBER: builtins.int
    FRAMES_RENDERED_FIELD_NUMBER: builtins.int
    FRAMES_DROPPED_FIELD_NUMBER: builtins.int
    FRAME_WIDTH_FIELD_NUMBER: builtins.int
    FRAME_HEIGHT_FIELD_NUMBER: builtins.int
    FRAMES_PER_SECOND_FIELD_NUMBER: builtins.int
    QP_SUM_FIELD_NUMBER: builtins.int
    TOTAL_DECODE_TIME_FIELD_NUMBER: builtins.int
    TOTAL_INTER_FRAME_DELAY_FIELD_NUMBER: builtins.int
    TOTAL_SQUARED_INTER_FRAME_DELAY_FIELD_NUMBER: builtins.int
    PAUSE_COUNT_FIELD_NUMBER: builtins.int
    TOTAL_PAUSE_DURATION_FIELD_NUMBER: builtins.int
    FREEZE_COUNT_FIELD_NUMBER: builtins.int
    TOTAL_FREEZE_DURATION_FIELD_NUMBER: builtins.int
    LAST_PACKET_RECEIVED_TIMESTAMP_FIELD_NUMBER: builtins.int
    HEADER_BYTES_RECEIVED_FIELD_NUMBER: builtins.int
    PACKETS_DISCARDED_FIELD_NUMBER: builtins.int
    FEC_BYTES_RECEIVED_FIELD_NUMBER: builtins.int
    FEC_PACKETS_RECEIVED_FIELD_NUMBER: builtins.int
    FEC_PACKETS_DISCARDED_FIELD_NUMBER: builtins.int
    BYTES_RECEIVED_FIELD_NUMBER: builtins.int
    NACK_COUNT_FIELD_NUMBER: builtins.int
    FIR_COUNT_FIELD_NUMBER: builtins.int
    PLI_COUNT_FIELD_NUMBER: builtins.int
    TOTAL_PROCESSING_DELAY_FIELD_NUMBER: builtins.int
    ESTIMATED_PLAYOUT_TIMESTAMP_FIELD_NUMBER: builtins.int
    JITTER_BUFFER_DELAY_FIELD_NUMBER: builtins.int
    JITTER_BUFFER_TARGET_DELAY_FIELD_NUMBER: builtins.int
    JITTER_BUFFER_EMITTED_COUNT_FIELD_NUMBER: builtins.int
    JITTER_BUFFER_MINIMUM_DELAY_FIELD_NUMBER: builtins.int
    TOTAL_SAMPLES_RECEIVED_FIELD_NUMBER: builtins.int
    CONCEALED_SAMPLES_FIELD_NUMBER: builtins.int
    SILENT_CONCEALED_SAMPLES_FIELD_NUMBER: builtins.int
    CONCEALMENT_EVENTS_FIELD_NUMBER: builtins.int
    INSERTED_SAMPLES_FOR_DECELERATION_FIELD_NUMBER: builtins.int
    REMOVED_SAMPLES_FOR_ACCELERATION_FIELD_NUMBER: builtins.int
    AUDIO_LEVEL_FIELD_NUMBER: builtins.int
    TOTAL_AUDIO_ENERGY_FIELD_NUMBER: builtins.int
    TOTAL_SAMPLES_DURATION_FIELD_NUMBER: builtins.int
    FRAMES_RECEIVED_FIELD_NUMBER: builtins.int
    DECODER_IMPLEMENTATION_FIELD_NUMBER: builtins.int
    PLAYOUT_ID_FIELD_NUMBER: builtins.int
    POWER_EFFICIENT_DECODER_FIELD_NUMBER: builtins.int
    FRAMES_ASSEMBLED_FROM_MULTIPLE_PACKETS_FIELD_NUMBER: builtins.int
    TOTAL_ASSEMBLY_TIME_FIELD_NUMBER: builtins.int
    RETRANSMITTED_PACKETS_RECEIVED_FIELD_NUMBER: builtins.int
    RETRANSMITTED_BYTES_RECEIVED_FIELD_NUMBER: builtins.int
    RTX_SSRC_FIELD_NUMBER: builtins.int
    FEC_SSRC_FIELD_NUMBER: builtins.int
    track_identifier: builtins.str
    mid: builtins.str
    remote_id: builtins.str
    frames_decoded: builtins.int
    key_frames_decoded: builtins.int
    frames_rendered: builtins.int
    frames_dropped: builtins.int
    frame_width: builtins.int
    frame_height: builtins.int
    frames_per_second: builtins.float
    qp_sum: builtins.int
    total_decode_time: builtins.float
    total_inter_frame_delay: builtins.float
    total_squared_inter_frame_delay: builtins.float
    pause_count: builtins.int
    total_pause_duration: builtins.float
    freeze_count: builtins.int
    total_freeze_duration: builtins.float
    last_packet_received_timestamp: builtins.float
    header_bytes_received: builtins.int
    packets_discarded: builtins.int
    fec_bytes_received: builtins.int
    fec_packets_received: builtins.int
    fec_packets_discarded: builtins.int
    bytes_received: builtins.int
    nack_count: builtins.int
    fir_count: builtins.int
    pli_count: builtins.int
    total_processing_delay: builtins.float
    estimated_playout_timestamp: builtins.float
    jitter_buffer_delay: builtins.float
    jitter_buffer_target_delay: builtins.float
    jitter_buffer_emitted_count: builtins.int
    jitter_buffer_minimum_delay: builtins.float
    total_samples_received: builtins.int
    concealed_samples: builtins.int
    silent_concealed_samples: builtins.int
    concealment_events: builtins.int
    inserted_samples_for_deceleration: builtins.int
    removed_samples_for_acceleration: builtins.int
    audio_level: builtins.float
    total_audio_energy: builtins.float
    total_samples_duration: builtins.float
    frames_received: builtins.int
    decoder_implementation: builtins.str
    playout_id: builtins.str
    power_efficient_decoder: builtins.bool
    frames_assembled_from_multiple_packets: builtins.int
    total_assembly_time: builtins.float
    retransmitted_packets_received: builtins.int
    retransmitted_bytes_received: builtins.int
    rtx_ssrc: builtins.int
    fec_ssrc: builtins.int
    def __init__(
        self,
        *,
        track_identifier: builtins.str | None = ...,
        mid: builtins.str | None = ...,
        remote_id: builtins.str | None = ...,
        frames_decoded: builtins.int | None = ...,
        key_frames_decoded: builtins.int | None = ...,
        frames_rendered: builtins.int | None = ...,
        frames_dropped: builtins.int | None = ...,
        frame_width: builtins.int | None = ...,
        frame_height: builtins.int | None = ...,
        frames_per_second: builtins.float | None = ...,
        qp_sum: builtins.int | None = ...,
        total_decode_time: builtins.float | None = ...,
        total_inter_frame_delay: builtins.float | None = ...,
        total_squared_inter_frame_delay: builtins.float | None = ...,
        pause_count: builtins.int | None = ...,
        total_pause_duration: builtins.float | None = ...,
        freeze_count: builtins.int | None = ...,
        total_freeze_duration: builtins.float | None = ...,
        last_packet_received_timestamp: builtins.float | None = ...,
        header_bytes_received: builtins.int | None = ...,
        packets_discarded: builtins.int | None = ...,
        fec_bytes_received: builtins.int | None = ...,
        fec_packets_received: builtins.int | None = ...,
        fec_packets_discarded: builtins.int | None = ...,
        bytes_received: builtins.int | None = ...,
        nack_count: builtins.int | None = ...,
        fir_count: builtins.int | None = ...,
        pli_count: builtins.int | None = ...,
        total_processing_delay: builtins.float | None = ...,
        estimated_playout_timestamp: builtins.float | None = ...,
        jitter_buffer_delay: builtins.float | None = ...,
        jitter_buffer_target_delay: builtins.float | None = ...,
        jitter_buffer_emitted_count: builtins.int | None = ...,
        jitter_buffer_minimum_delay: builtins.float | None = ...,
        total_samples_received: builtins.int | None = ...,
        concealed_samples: builtins.int | None = ...,
        silent_concealed_samples: builtins.int | None = ...,
        concealment_events: builtins.int | None = ...,
        inserted_samples_for_deceleration: builtins.int | None = ...,
        removed_samples_for_acceleration: builtins.int | None = ...,
        audio_level: builtins.float | None = ...,
        total_audio_energy: builtins.float | None = ...,
        total_samples_duration: builtins.float | None = ...,
        frames_received: builtins.int | None = ...,
        decoder_implementation: builtins.str | None = ...,
        playout_id: builtins.str | None = ...,
        power_efficient_decoder: builtins.bool | None = ...,
        frames_assembled_from_multiple_packets: builtins.int | None = ...,
        total_assembly_time: builtins.float | None = ...,
        retransmitted_packets_received: builtins.int | None = ...,
        retransmitted_bytes_received: builtins.int | None = ...,
        rtx_ssrc: builtins.int | None = ...,
        fec_ssrc: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["audio_level", b"audio_level", "bytes_received", b"bytes_received", "concealed_samples", b"concealed_samples", "concealment_events", b"concealment_events", "decoder_implementation", b"decoder_implementation", "estimated_playout_timestamp", b"estimated_playout_timestamp", "fec_bytes_received", b"fec_bytes_received", "fec_packets_discarded", b"fec_packets_discarded", "fec_packets_received", b"fec_packets_received", "fec_ssrc", b"fec_ssrc", "fir_count", b"fir_count", "frame_height", b"frame_height", "frame_width", b"frame_width", "frames_assembled_from_multiple_packets", b"frames_assembled_from_multiple_packets", "frames_decoded", b"frames_decoded", "frames_dropped", b"frames_dropped", "frames_per_second", b"frames_per_second", "frames_received", b"frames_received", "frames_rendered", b"frames_rendered", "freeze_count", b"freeze_count", "header_bytes_received", b"header_bytes_received", "inserted_samples_for_deceleration", b"inserted_samples_for_deceleration", "jitter_buffer_delay", b"jitter_buffer_delay", "jitter_buffer_emitted_count", b"jitter_buffer_emitted_count", "jitter_buffer_minimum_delay", b"jitter_buffer_minimum_delay", "jitter_buffer_target_delay", b"jitter_buffer_target_delay", "key_frames_decoded", b"key_frames_decoded", "last_packet_received_timestamp", b"last_packet_received_timestamp", "mid", b"mid", "nack_count", b"nack_count", "packets_discarded", b"packets_discarded", "pause_count", b"pause_count", "playout_id", b"playout_id", "pli_count", b"pli_count", "power_efficient_decoder", b"power_efficient_decoder", "qp_sum", b"qp_sum", "remote_id", b"remote_id", "removed_samples_for_acceleration", b"removed_samples_for_acceleration", "retransmitted_bytes_received", b"retransmitted_bytes_received", "retransmitted_packets_received", b"retransmitted_packets_received", "rtx_ssrc", b"rtx_ssrc", "silent_concealed_samples", b"silent_concealed_samples", "total_assembly_time", b"total_assembly_time", "total_audio_energy", b"total_audio_energy", "total_decode_time", b"total_decode_time", "total_freeze_duration", b"total_freeze_duration", "total_inter_frame_delay", b"total_inter_frame_delay", "total_pause_duration", b"total_pause_duration", "total_processing_delay", b"total_processing_delay", "total_samples_duration", b"total_samples_duration", "total_samples_received", b"total_samples_received", "total_squared_inter_frame_delay", b"total_squared_inter_frame_delay", "track_identifier", b"track_identifier"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["audio_level", b"audio_level", "bytes_received", b"bytes_received", "concealed_samples", b"concealed_samples", "concealment_events", b"concealment_events", "decoder_implementation", b"decoder_implementation", "estimated_playout_timestamp", b"estimated_playout_timestamp", "fec_bytes_received", b"fec_bytes_received", "fec_packets_discarded", b"fec_packets_discarded", "fec_packets_received", b"fec_packets_received", "fec_ssrc", b"fec_ssrc", "fir_count", b"fir_count", "frame_height", b"frame_height", "frame_width", b"frame_width", "frames_assembled_from_multiple_packets", b"frames_assembled_from_multiple_packets", "frames_decoded", b"frames_decoded", "frames_dropped", b"frames_dropped", "frames_per_second", b"frames_per_second", "frames_received", b"frames_received", "frames_rendered", b"frames_rendered", "freeze_count", b"freeze_count", "header_bytes_received", b"header_bytes_received", "inserted_samples_for_deceleration", b"inserted_samples_for_deceleration", "jitter_buffer_delay", b"jitter_buffer_delay", "jitter_buffer_emitted_count", b"jitter_buffer_emitted_count", "jitter_buffer_minimum_delay", b"jitter_buffer_minimum_delay", "jitter_buffer_target_delay", b"jitter_buffer_target_delay", "key_frames_decoded", b"key_frames_decoded", "last_packet_received_timestamp", b"last_packet_received_timestamp", "mid", b"mid", "nack_count", b"nack_count", "packets_discarded", b"packets_discarded", "pause_count", b"pause_count", "playout_id", b"playout_id", "pli_count", b"pli_count", "power_efficient_decoder", b"power_efficient_decoder", "qp_sum", b"qp_sum", "remote_id", b"remote_id", "removed_samples_for_acceleration", b"removed_samples_for_acceleration", "retransmitted_bytes_received", b"retransmitted_bytes_received", "retransmitted_packets_received", b"retransmitted_packets_received", "rtx_ssrc", b"rtx_ssrc", "silent_concealed_samples", b"silent_concealed_samples", "total_assembly_time", b"total_assembly_time", "total_audio_energy", b"total_audio_energy", "total_decode_time", b"total_decode_time", "total_freeze_duration", b"total_freeze_duration", "total_inter_frame_delay", b"total_inter_frame_delay", "total_pause_duration", b"total_pause_duration", "total_processing_delay", b"total_processing_delay", "total_samples_duration", b"total_samples_duration", "total_samples_received", b"total_samples_received", "total_squared_inter_frame_delay", b"total_squared_inter_frame_delay", "track_identifier", b"track_identifier"]) -> None: ...

global___InboundRtpStreamStats = InboundRtpStreamStats

@typing.final
class SentRtpStreamStats(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PACKETS_SENT_FIELD_NUMBER: builtins.int
    BYTES_SENT_FIELD_NUMBER: builtins.int
    packets_sent: builtins.int
    bytes_sent: builtins.int
    def __init__(
        self,
        *,
        packets_sent: builtins.int | None = ...,
        bytes_sent: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["bytes_sent", b"bytes_sent", "packets_sent", b"packets_sent"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["bytes_sent", b"bytes_sent", "packets_sent", b"packets_sent"]) -> None: ...

global___SentRtpStreamStats = SentRtpStreamStats

@typing.final
class OutboundRtpStreamStats(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    @typing.final
    class QualityLimitationDurationsEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.str
        value: builtins.float
        def __init__(
            self,
            *,
            key: builtins.str | None = ...,
            value: builtins.float | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing.Literal["key", b"key", "value", b"value"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing.Literal["key", b"key", "value", b"value"]) -> None: ...

    MID_FIELD_NUMBER: builtins.int
    MEDIA_SOURCE_ID_FIELD_NUMBER: builtins.int
    REMOTE_ID_FIELD_NUMBER: builtins.int
    RID_FIELD_NUMBER: builtins.int
    HEADER_BYTES_SENT_FIELD_NUMBER: builtins.int
    RETRANSMITTED_PACKETS_SENT_FIELD_NUMBER: builtins.int
    RETRANSMITTED_BYTES_SENT_FIELD_NUMBER: builtins.int
    RTX_SSRC_FIELD_NUMBER: builtins.int
    TARGET_BITRATE_FIELD_NUMBER: builtins.int
    TOTAL_ENCODED_BYTES_TARGET_FIELD_NUMBER: builtins.int
    FRAME_WIDTH_FIELD_NUMBER: builtins.int
    FRAME_HEIGHT_FIELD_NUMBER: builtins.int
    FRAMES_PER_SECOND_FIELD_NUMBER: builtins.int
    FRAMES_SENT_FIELD_NUMBER: builtins.int
    HUGE_FRAMES_SENT_FIELD_NUMBER: builtins.int
    FRAMES_ENCODED_FIELD_NUMBER: builtins.int
    KEY_FRAMES_ENCODED_FIELD_NUMBER: builtins.int
    QP_SUM_FIELD_NUMBER: builtins.int
    TOTAL_ENCODE_TIME_FIELD_NUMBER: builtins.int
    TOTAL_PACKET_SEND_DELAY_FIELD_NUMBER: builtins.int
    QUALITY_LIMITATION_REASON_FIELD_NUMBER: builtins.int
    QUALITY_LIMITATION_DURATIONS_FIELD_NUMBER: builtins.int
    QUALITY_LIMITATION_RESOLUTION_CHANGES_FIELD_NUMBER: builtins.int
    NACK_COUNT_FIELD_NUMBER: builtins.int
    FIR_COUNT_FIELD_NUMBER: builtins.int
    PLI_COUNT_FIELD_NUMBER: builtins.int
    ENCODER_IMPLEMENTATION_FIELD_NUMBER: builtins.int
    POWER_EFFICIENT_ENCODER_FIELD_NUMBER: builtins.int
    ACTIVE_FIELD_NUMBER: builtins.int
    SCALABILITY_MODE_FIELD_NUMBER: builtins.int
    mid: builtins.str
    media_source_id: builtins.str
    remote_id: builtins.str
    rid: builtins.str
    header_bytes_sent: builtins.int
    retransmitted_packets_sent: builtins.int
    retransmitted_bytes_sent: builtins.int
    rtx_ssrc: builtins.int
    target_bitrate: builtins.float
    total_encoded_bytes_target: builtins.int
    frame_width: builtins.int
    frame_height: builtins.int
    frames_per_second: builtins.float
    frames_sent: builtins.int
    huge_frames_sent: builtins.int
    frames_encoded: builtins.int
    key_frames_encoded: builtins.int
    qp_sum: builtins.int
    total_encode_time: builtins.float
    total_packet_send_delay: builtins.float
    quality_limitation_reason: global___QualityLimitationReason.ValueType
    quality_limitation_resolution_changes: builtins.int
    nack_count: builtins.int
    fir_count: builtins.int
    pli_count: builtins.int
    encoder_implementation: builtins.str
    power_efficient_encoder: builtins.bool
    active: builtins.bool
    scalability_mode: builtins.str
    @property
    def quality_limitation_durations(self) -> google.protobuf.internal.containers.ScalarMap[builtins.str, builtins.float]: ...
    def __init__(
        self,
        *,
        mid: builtins.str | None = ...,
        media_source_id: builtins.str | None = ...,
        remote_id: builtins.str | None = ...,
        rid: builtins.str | None = ...,
        header_bytes_sent: builtins.int | None = ...,
        retransmitted_packets_sent: builtins.int | None = ...,
        retransmitted_bytes_sent: builtins.int | None = ...,
        rtx_ssrc: builtins.int | None = ...,
        target_bitrate: builtins.float | None = ...,
        total_encoded_bytes_target: builtins.int | None = ...,
        frame_width: builtins.int | None = ...,
        frame_height: builtins.int | None = ...,
        frames_per_second: builtins.float | None = ...,
        frames_sent: builtins.int | None = ...,
        huge_frames_sent: builtins.int | None = ...,
        frames_encoded: builtins.int | None = ...,
        key_frames_encoded: builtins.int | None = ...,
        qp_sum: builtins.int | None = ...,
        total_encode_time: builtins.float | None = ...,
        total_packet_send_delay: builtins.float | None = ...,
        quality_limitation_reason: global___QualityLimitationReason.ValueType | None = ...,
        quality_limitation_durations: collections.abc.Mapping[builtins.str, builtins.float] | None = ...,
        quality_limitation_resolution_changes: builtins.int | None = ...,
        nack_count: builtins.int | None = ...,
        fir_count: builtins.int | None = ...,
        pli_count: builtins.int | None = ...,
        encoder_implementation: builtins.str | None = ...,
        power_efficient_encoder: builtins.bool | None = ...,
        active: builtins.bool | None = ...,
        scalability_mode: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["active", b"active", "encoder_implementation", b"encoder_implementation", "fir_count", b"fir_count", "frame_height", b"frame_height", "frame_width", b"frame_width", "frames_encoded", b"frames_encoded", "frames_per_second", b"frames_per_second", "frames_sent", b"frames_sent", "header_bytes_sent", b"header_bytes_sent", "huge_frames_sent", b"huge_frames_sent", "key_frames_encoded", b"key_frames_encoded", "media_source_id", b"media_source_id", "mid", b"mid", "nack_count", b"nack_count", "pli_count", b"pli_count", "power_efficient_encoder", b"power_efficient_encoder", "qp_sum", b"qp_sum", "quality_limitation_reason", b"quality_limitation_reason", "quality_limitation_resolution_changes", b"quality_limitation_resolution_changes", "remote_id", b"remote_id", "retransmitted_bytes_sent", b"retransmitted_bytes_sent", "retransmitted_packets_sent", b"retransmitted_packets_sent", "rid", b"rid", "rtx_ssrc", b"rtx_ssrc", "scalability_mode", b"scalability_mode", "target_bitrate", b"target_bitrate", "total_encode_time", b"total_encode_time", "total_encoded_bytes_target", b"total_encoded_bytes_target", "total_packet_send_delay", b"total_packet_send_delay"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["active", b"active", "encoder_implementation", b"encoder_implementation", "fir_count", b"fir_count", "frame_height", b"frame_height", "frame_width", b"frame_width", "frames_encoded", b"frames_encoded", "frames_per_second", b"frames_per_second", "frames_sent", b"frames_sent", "header_bytes_sent", b"header_bytes_sent", "huge_frames_sent", b"huge_frames_sent", "key_frames_encoded", b"key_frames_encoded", "media_source_id", b"media_source_id", "mid", b"mid", "nack_count", b"nack_count", "pli_count", b"pli_count", "power_efficient_encoder", b"power_efficient_encoder", "qp_sum", b"qp_sum", "quality_limitation_durations", b"quality_limitation_durations", "quality_limitation_reason", b"quality_limitation_reason", "quality_limitation_resolution_changes", b"quality_limitation_resolution_changes", "remote_id", b"remote_id", "retransmitted_bytes_sent", b"retransmitted_bytes_sent", "retransmitted_packets_sent", b"retransmitted_packets_sent", "rid", b"rid", "rtx_ssrc", b"rtx_ssrc", "scalability_mode", b"scalability_mode", "target_bitrate", b"target_bitrate", "total_encode_time", b"total_encode_time", "total_encoded_bytes_target", b"total_encoded_bytes_target", "total_packet_send_delay", b"total_packet_send_delay"]) -> None: ...

global___OutboundRtpStreamStats = OutboundRtpStreamStats

@typing.final
class RemoteInboundRtpStreamStats(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LOCAL_ID_FIELD_NUMBER: builtins.int
    ROUND_TRIP_TIME_FIELD_NUMBER: builtins.int
    TOTAL_ROUND_TRIP_TIME_FIELD_NUMBER: builtins.int
    FRACTION_LOST_FIELD_NUMBER: builtins.int
    ROUND_TRIP_TIME_MEASUREMENTS_FIELD_NUMBER: builtins.int
    local_id: builtins.str
    round_trip_time: builtins.float
    total_round_trip_time: builtins.float
    fraction_lost: builtins.float
    round_trip_time_measurements: builtins.int
    def __init__(
        self,
        *,
        local_id: builtins.str | None = ...,
        round_trip_time: builtins.float | None = ...,
        total_round_trip_time: builtins.float | None = ...,
        fraction_lost: builtins.float | None = ...,
        round_trip_time_measurements: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["fraction_lost", b"fraction_lost", "local_id", b"local_id", "round_trip_time", b"round_trip_time", "round_trip_time_measurements", b"round_trip_time_measurements", "total_round_trip_time", b"total_round_trip_time"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["fraction_lost", b"fraction_lost", "local_id", b"local_id", "round_trip_time", b"round_trip_time", "round_trip_time_measurements", b"round_trip_time_measurements", "total_round_trip_time", b"total_round_trip_time"]) -> None: ...

global___RemoteInboundRtpStreamStats = RemoteInboundRtpStreamStats

@typing.final
class RemoteOutboundRtpStreamStats(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LOCAL_ID_FIELD_NUMBER: builtins.int
    REMOTE_TIMESTAMP_FIELD_NUMBER: builtins.int
    REPORTS_SENT_FIELD_NUMBER: builtins.int
    ROUND_TRIP_TIME_FIELD_NUMBER: builtins.int
    TOTAL_ROUND_TRIP_TIME_FIELD_NUMBER: builtins.int
    ROUND_TRIP_TIME_MEASUREMENTS_FIELD_NUMBER: builtins.int
    local_id: builtins.str
    remote_timestamp: builtins.float
    reports_sent: builtins.int
    round_trip_time: builtins.float
    total_round_trip_time: builtins.float
    round_trip_time_measurements: builtins.int
    def __init__(
        self,
        *,
        local_id: builtins.str | None = ...,
        remote_timestamp: builtins.float | None = ...,
        reports_sent: builtins.int | None = ...,
        round_trip_time: builtins.float | None = ...,
        total_round_trip_time: builtins.float | None = ...,
        round_trip_time_measurements: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["local_id", b"local_id", "remote_timestamp", b"remote_timestamp", "reports_sent", b"reports_sent", "round_trip_time", b"round_trip_time", "round_trip_time_measurements", b"round_trip_time_measurements", "total_round_trip_time", b"total_round_trip_time"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["local_id", b"local_id", "remote_timestamp", b"remote_timestamp", "reports_sent", b"reports_sent", "round_trip_time", b"round_trip_time", "round_trip_time_measurements", b"round_trip_time_measurements", "total_round_trip_time", b"total_round_trip_time"]) -> None: ...

global___RemoteOutboundRtpStreamStats = RemoteOutboundRtpStreamStats

@typing.final
class MediaSourceStats(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TRACK_IDENTIFIER_FIELD_NUMBER: builtins.int
    KIND_FIELD_NUMBER: builtins.int
    track_identifier: builtins.str
    kind: builtins.str
    def __init__(
        self,
        *,
        track_identifier: builtins.str | None = ...,
        kind: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["kind", b"kind", "track_identifier", b"track_identifier"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["kind", b"kind", "track_identifier", b"track_identifier"]) -> None: ...

global___MediaSourceStats = MediaSourceStats

@typing.final
class AudioSourceStats(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    AUDIO_LEVEL_FIELD_NUMBER: builtins.int
    TOTAL_AUDIO_ENERGY_FIELD_NUMBER: builtins.int
    TOTAL_SAMPLES_DURATION_FIELD_NUMBER: builtins.int
    ECHO_RETURN_LOSS_FIELD_NUMBER: builtins.int
    ECHO_RETURN_LOSS_ENHANCEMENT_FIELD_NUMBER: builtins.int
    DROPPED_SAMPLES_DURATION_FIELD_NUMBER: builtins.int
    DROPPED_SAMPLES_EVENTS_FIELD_NUMBER: builtins.int
    TOTAL_CAPTURE_DELAY_FIELD_NUMBER: builtins.int
    TOTAL_SAMPLES_CAPTURED_FIELD_NUMBER: builtins.int
    audio_level: builtins.float
    total_audio_energy: builtins.float
    total_samples_duration: builtins.float
    echo_return_loss: builtins.float
    echo_return_loss_enhancement: builtins.float
    dropped_samples_duration: builtins.float
    dropped_samples_events: builtins.int
    total_capture_delay: builtins.float
    total_samples_captured: builtins.int
    def __init__(
        self,
        *,
        audio_level: builtins.float | None = ...,
        total_audio_energy: builtins.float | None = ...,
        total_samples_duration: builtins.float | None = ...,
        echo_return_loss: builtins.float | None = ...,
        echo_return_loss_enhancement: builtins.float | None = ...,
        dropped_samples_duration: builtins.float | None = ...,
        dropped_samples_events: builtins.int | None = ...,
        total_capture_delay: builtins.float | None = ...,
        total_samples_captured: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["audio_level", b"audio_level", "dropped_samples_duration", b"dropped_samples_duration", "dropped_samples_events", b"dropped_samples_events", "echo_return_loss", b"echo_return_loss", "echo_return_loss_enhancement", b"echo_return_loss_enhancement", "total_audio_energy", b"total_audio_energy", "total_capture_delay", b"total_capture_delay", "total_samples_captured", b"total_samples_captured", "total_samples_duration", b"total_samples_duration"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["audio_level", b"audio_level", "dropped_samples_duration", b"dropped_samples_duration", "dropped_samples_events", b"dropped_samples_events", "echo_return_loss", b"echo_return_loss", "echo_return_loss_enhancement", b"echo_return_loss_enhancement", "total_audio_energy", b"total_audio_energy", "total_capture_delay", b"total_capture_delay", "total_samples_captured", b"total_samples_captured", "total_samples_duration", b"total_samples_duration"]) -> None: ...

global___AudioSourceStats = AudioSourceStats

@typing.final
class VideoSourceStats(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    WIDTH_FIELD_NUMBER: builtins.int
    HEIGHT_FIELD_NUMBER: builtins.int
    FRAMES_FIELD_NUMBER: builtins.int
    FRAMES_PER_SECOND_FIELD_NUMBER: builtins.int
    width: builtins.int
    height: builtins.int
    frames: builtins.int
    frames_per_second: builtins.float
    def __init__(
        self,
        *,
        width: builtins.int | None = ...,
        height: builtins.int | None = ...,
        frames: builtins.int | None = ...,
        frames_per_second: builtins.float | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["frames", b"frames", "frames_per_second", b"frames_per_second", "height", b"height", "width", b"width"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["frames", b"frames", "frames_per_second", b"frames_per_second", "height", b"height", "width", b"width"]) -> None: ...

global___VideoSourceStats = VideoSourceStats

@typing.final
class AudioPlayoutStats(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    KIND_FIELD_NUMBER: builtins.int
    SYNTHESIZED_SAMPLES_DURATION_FIELD_NUMBER: builtins.int
    SYNTHESIZED_SAMPLES_EVENTS_FIELD_NUMBER: builtins.int
    TOTAL_SAMPLES_DURATION_FIELD_NUMBER: builtins.int
    TOTAL_PLAYOUT_DELAY_FIELD_NUMBER: builtins.int
    TOTAL_SAMPLES_COUNT_FIELD_NUMBER: builtins.int
    kind: builtins.str
    synthesized_samples_duration: builtins.float
    synthesized_samples_events: builtins.int
    total_samples_duration: builtins.float
    total_playout_delay: builtins.float
    total_samples_count: builtins.int
    def __init__(
        self,
        *,
        kind: builtins.str | None = ...,
        synthesized_samples_duration: builtins.float | None = ...,
        synthesized_samples_events: builtins.int | None = ...,
        total_samples_duration: builtins.float | None = ...,
        total_playout_delay: builtins.float | None = ...,
        total_samples_count: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["kind", b"kind", "synthesized_samples_duration", b"synthesized_samples_duration", "synthesized_samples_events", b"synthesized_samples_events", "total_playout_delay", b"total_playout_delay", "total_samples_count", b"total_samples_count", "total_samples_duration", b"total_samples_duration"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["kind", b"kind", "synthesized_samples_duration", b"synthesized_samples_duration", "synthesized_samples_events", b"synthesized_samples_events", "total_playout_delay", b"total_playout_delay", "total_samples_count", b"total_samples_count", "total_samples_duration", b"total_samples_duration"]) -> None: ...

global___AudioPlayoutStats = AudioPlayoutStats

@typing.final
class PeerConnectionStats(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DATA_CHANNELS_OPENED_FIELD_NUMBER: builtins.int
    DATA_CHANNELS_CLOSED_FIELD_NUMBER: builtins.int
    data_channels_opened: builtins.int
    data_channels_closed: builtins.int
    def __init__(
        self,
        *,
        data_channels_opened: builtins.int | None = ...,
        data_channels_closed: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["data_channels_closed", b"data_channels_closed", "data_channels_opened", b"data_channels_opened"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["data_channels_closed", b"data_channels_closed", "data_channels_opened", b"data_channels_opened"]) -> None: ...

global___PeerConnectionStats = PeerConnectionStats

@typing.final
class DataChannelStats(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LABEL_FIELD_NUMBER: builtins.int
    PROTOCOL_FIELD_NUMBER: builtins.int
    DATA_CHANNEL_IDENTIFIER_FIELD_NUMBER: builtins.int
    STATE_FIELD_NUMBER: builtins.int
    MESSAGES_SENT_FIELD_NUMBER: builtins.int
    BYTES_SENT_FIELD_NUMBER: builtins.int
    MESSAGES_RECEIVED_FIELD_NUMBER: builtins.int
    BYTES_RECEIVED_FIELD_NUMBER: builtins.int
    label: builtins.str
    protocol: builtins.str
    data_channel_identifier: builtins.int
    state: global___DataChannelState.ValueType
    messages_sent: builtins.int
    bytes_sent: builtins.int
    messages_received: builtins.int
    bytes_received: builtins.int
    def __init__(
        self,
        *,
        label: builtins.str | None = ...,
        protocol: builtins.str | None = ...,
        data_channel_identifier: builtins.int | None = ...,
        state: global___DataChannelState.ValueType | None = ...,
        messages_sent: builtins.int | None = ...,
        bytes_sent: builtins.int | None = ...,
        messages_received: builtins.int | None = ...,
        bytes_received: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["bytes_received", b"bytes_received", "bytes_sent", b"bytes_sent", "data_channel_identifier", b"data_channel_identifier", "label", b"label", "messages_received", b"messages_received", "messages_sent", b"messages_sent", "protocol", b"protocol", "state", b"state"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["bytes_received", b"bytes_received", "bytes_sent", b"bytes_sent", "data_channel_identifier", b"data_channel_identifier", "label", b"label", "messages_received", b"messages_received", "messages_sent", b"messages_sent", "protocol", b"protocol", "state", b"state"]) -> None: ...

global___DataChannelStats = DataChannelStats

@typing.final
class TransportStats(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PACKETS_SENT_FIELD_NUMBER: builtins.int
    PACKETS_RECEIVED_FIELD_NUMBER: builtins.int
    BYTES_SENT_FIELD_NUMBER: builtins.int
    BYTES_RECEIVED_FIELD_NUMBER: builtins.int
    ICE_ROLE_FIELD_NUMBER: builtins.int
    ICE_LOCAL_USERNAME_FRAGMENT_FIELD_NUMBER: builtins.int
    DTLS_STATE_FIELD_NUMBER: builtins.int
    ICE_STATE_FIELD_NUMBER: builtins.int
    SELECTED_CANDIDATE_PAIR_ID_FIELD_NUMBER: builtins.int
    LOCAL_CERTIFICATE_ID_FIELD_NUMBER: builtins.int
    REMOTE_CERTIFICATE_ID_FIELD_NUMBER: builtins.int
    TLS_VERSION_FIELD_NUMBER: builtins.int
    DTLS_CIPHER_FIELD_NUMBER: builtins.int
    DTLS_ROLE_FIELD_NUMBER: builtins.int
    SRTP_CIPHER_FIELD_NUMBER: builtins.int
    SELECTED_CANDIDATE_PAIR_CHANGES_FIELD_NUMBER: builtins.int
    packets_sent: builtins.int
    packets_received: builtins.int
    bytes_sent: builtins.int
    bytes_received: builtins.int
    ice_role: global___IceRole.ValueType
    ice_local_username_fragment: builtins.str
    dtls_state: global___DtlsTransportState.ValueType
    ice_state: global___IceTransportState.ValueType
    selected_candidate_pair_id: builtins.str
    local_certificate_id: builtins.str
    remote_certificate_id: builtins.str
    tls_version: builtins.str
    dtls_cipher: builtins.str
    dtls_role: global___DtlsRole.ValueType
    srtp_cipher: builtins.str
    selected_candidate_pair_changes: builtins.int
    def __init__(
        self,
        *,
        packets_sent: builtins.int | None = ...,
        packets_received: builtins.int | None = ...,
        bytes_sent: builtins.int | None = ...,
        bytes_received: builtins.int | None = ...,
        ice_role: global___IceRole.ValueType | None = ...,
        ice_local_username_fragment: builtins.str | None = ...,
        dtls_state: global___DtlsTransportState.ValueType | None = ...,
        ice_state: global___IceTransportState.ValueType | None = ...,
        selected_candidate_pair_id: builtins.str | None = ...,
        local_certificate_id: builtins.str | None = ...,
        remote_certificate_id: builtins.str | None = ...,
        tls_version: builtins.str | None = ...,
        dtls_cipher: builtins.str | None = ...,
        dtls_role: global___DtlsRole.ValueType | None = ...,
        srtp_cipher: builtins.str | None = ...,
        selected_candidate_pair_changes: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["bytes_received", b"bytes_received", "bytes_sent", b"bytes_sent", "dtls_cipher", b"dtls_cipher", "dtls_role", b"dtls_role", "dtls_state", b"dtls_state", "ice_local_username_fragment", b"ice_local_username_fragment", "ice_role", b"ice_role", "ice_state", b"ice_state", "local_certificate_id", b"local_certificate_id", "packets_received", b"packets_received", "packets_sent", b"packets_sent", "remote_certificate_id", b"remote_certificate_id", "selected_candidate_pair_changes", b"selected_candidate_pair_changes", "selected_candidate_pair_id", b"selected_candidate_pair_id", "srtp_cipher", b"srtp_cipher", "tls_version", b"tls_version"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["bytes_received", b"bytes_received", "bytes_sent", b"bytes_sent", "dtls_cipher", b"dtls_cipher", "dtls_role", b"dtls_role", "dtls_state", b"dtls_state", "ice_local_username_fragment", b"ice_local_username_fragment", "ice_role", b"ice_role", "ice_state", b"ice_state", "local_certificate_id", b"local_certificate_id", "packets_received", b"packets_received", "packets_sent", b"packets_sent", "remote_certificate_id", b"remote_certificate_id", "selected_candidate_pair_changes", b"selected_candidate_pair_changes", "selected_candidate_pair_id", b"selected_candidate_pair_id", "srtp_cipher", b"srtp_cipher", "tls_version", b"tls_version"]) -> None: ...

global___TransportStats = TransportStats

@typing.final
class CandidatePairStats(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TRANSPORT_ID_FIELD_NUMBER: builtins.int
    LOCAL_CANDIDATE_ID_FIELD_NUMBER: builtins.int
    REMOTE_CANDIDATE_ID_FIELD_NUMBER: builtins.int
    STATE_FIELD_NUMBER: builtins.int
    NOMINATED_FIELD_NUMBER: builtins.int
    PACKETS_SENT_FIELD_NUMBER: builtins.int
    PACKETS_RECEIVED_FIELD_NUMBER: builtins.int
    BYTES_SENT_FIELD_NUMBER: builtins.int
    BYTES_RECEIVED_FIELD_NUMBER: builtins.int
    LAST_PACKET_SENT_TIMESTAMP_FIELD_NUMBER: builtins.int
    LAST_PACKET_RECEIVED_TIMESTAMP_FIELD_NUMBER: builtins.int
    TOTAL_ROUND_TRIP_TIME_FIELD_NUMBER: builtins.int
    CURRENT_ROUND_TRIP_TIME_FIELD_NUMBER: builtins.int
    AVAILABLE_OUTGOING_BITRATE_FIELD_NUMBER: builtins.int
    AVAILABLE_INCOMING_BITRATE_FIELD_NUMBER: builtins.int
    REQUESTS_RECEIVED_FIELD_NUMBER: builtins.int
    REQUESTS_SENT_FIELD_NUMBER: builtins.int
    RESPONSES_RECEIVED_FIELD_NUMBER: builtins.int
    RESPONSES_SENT_FIELD_NUMBER: builtins.int
    CONSENT_REQUESTS_SENT_FIELD_NUMBER: builtins.int
    PACKETS_DISCARDED_ON_SEND_FIELD_NUMBER: builtins.int
    BYTES_DISCARDED_ON_SEND_FIELD_NUMBER: builtins.int
    transport_id: builtins.str
    local_candidate_id: builtins.str
    remote_candidate_id: builtins.str
    state: global___IceCandidatePairState.ValueType
    nominated: builtins.bool
    packets_sent: builtins.int
    packets_received: builtins.int
    bytes_sent: builtins.int
    bytes_received: builtins.int
    last_packet_sent_timestamp: builtins.float
    last_packet_received_timestamp: builtins.float
    total_round_trip_time: builtins.float
    current_round_trip_time: builtins.float
    available_outgoing_bitrate: builtins.float
    available_incoming_bitrate: builtins.float
    requests_received: builtins.int
    requests_sent: builtins.int
    responses_received: builtins.int
    responses_sent: builtins.int
    consent_requests_sent: builtins.int
    packets_discarded_on_send: builtins.int
    bytes_discarded_on_send: builtins.int
    def __init__(
        self,
        *,
        transport_id: builtins.str | None = ...,
        local_candidate_id: builtins.str | None = ...,
        remote_candidate_id: builtins.str | None = ...,
        state: global___IceCandidatePairState.ValueType | None = ...,
        nominated: builtins.bool | None = ...,
        packets_sent: builtins.int | None = ...,
        packets_received: builtins.int | None = ...,
        bytes_sent: builtins.int | None = ...,
        bytes_received: builtins.int | None = ...,
        last_packet_sent_timestamp: builtins.float | None = ...,
        last_packet_received_timestamp: builtins.float | None = ...,
        total_round_trip_time: builtins.float | None = ...,
        current_round_trip_time: builtins.float | None = ...,
        available_outgoing_bitrate: builtins.float | None = ...,
        available_incoming_bitrate: builtins.float | None = ...,
        requests_received: builtins.int | None = ...,
        requests_sent: builtins.int | None = ...,
        responses_received: builtins.int | None = ...,
        responses_sent: builtins.int | None = ...,
        consent_requests_sent: builtins.int | None = ...,
        packets_discarded_on_send: builtins.int | None = ...,
        bytes_discarded_on_send: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["available_incoming_bitrate", b"available_incoming_bitrate", "available_outgoing_bitrate", b"available_outgoing_bitrate", "bytes_discarded_on_send", b"bytes_discarded_on_send", "bytes_received", b"bytes_received", "bytes_sent", b"bytes_sent", "consent_requests_sent", b"consent_requests_sent", "current_round_trip_time", b"current_round_trip_time", "last_packet_received_timestamp", b"last_packet_received_timestamp", "last_packet_sent_timestamp", b"last_packet_sent_timestamp", "local_candidate_id", b"local_candidate_id", "nominated", b"nominated", "packets_discarded_on_send", b"packets_discarded_on_send", "packets_received", b"packets_received", "packets_sent", b"packets_sent", "remote_candidate_id", b"remote_candidate_id", "requests_received", b"requests_received", "requests_sent", b"requests_sent", "responses_received", b"responses_received", "responses_sent", b"responses_sent", "state", b"state", "total_round_trip_time", b"total_round_trip_time", "transport_id", b"transport_id"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["available_incoming_bitrate", b"available_incoming_bitrate", "available_outgoing_bitrate", b"available_outgoing_bitrate", "bytes_discarded_on_send", b"bytes_discarded_on_send", "bytes_received", b"bytes_received", "bytes_sent", b"bytes_sent", "consent_requests_sent", b"consent_requests_sent", "current_round_trip_time", b"current_round_trip_time", "last_packet_received_timestamp", b"last_packet_received_timestamp", "last_packet_sent_timestamp", b"last_packet_sent_timestamp", "local_candidate_id", b"local_candidate_id", "nominated", b"nominated", "packets_discarded_on_send", b"packets_discarded_on_send", "packets_received", b"packets_received", "packets_sent", b"packets_sent", "remote_candidate_id", b"remote_candidate_id", "requests_received", b"requests_received", "requests_sent", b"requests_sent", "responses_received", b"responses_received", "responses_sent", b"responses_sent", "state", b"state", "total_round_trip_time", b"total_round_trip_time", "transport_id", b"transport_id"]) -> None: ...

global___CandidatePairStats = CandidatePairStats

@typing.final
class IceCandidateStats(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TRANSPORT_ID_FIELD_NUMBER: builtins.int
    ADDRESS_FIELD_NUMBER: builtins.int
    PORT_FIELD_NUMBER: builtins.int
    PROTOCOL_FIELD_NUMBER: builtins.int
    CANDIDATE_TYPE_FIELD_NUMBER: builtins.int
    PRIORITY_FIELD_NUMBER: builtins.int
    URL_FIELD_NUMBER: builtins.int
    RELAY_PROTOCOL_FIELD_NUMBER: builtins.int
    FOUNDATION_FIELD_NUMBER: builtins.int
    RELATED_ADDRESS_FIELD_NUMBER: builtins.int
    RELATED_PORT_FIELD_NUMBER: builtins.int
    USERNAME_FRAGMENT_FIELD_NUMBER: builtins.int
    TCP_TYPE_FIELD_NUMBER: builtins.int
    transport_id: builtins.str
    address: builtins.str
    port: builtins.int
    protocol: builtins.str
    candidate_type: global___IceCandidateType.ValueType
    priority: builtins.int
    url: builtins.str
    relay_protocol: global___IceServerTransportProtocol.ValueType
    foundation: builtins.str
    related_address: builtins.str
    related_port: builtins.int
    username_fragment: builtins.str
    tcp_type: global___IceTcpCandidateType.ValueType
    def __init__(
        self,
        *,
        transport_id: builtins.str | None = ...,
        address: builtins.str | None = ...,
        port: builtins.int | None = ...,
        protocol: builtins.str | None = ...,
        candidate_type: global___IceCandidateType.ValueType | None = ...,
        priority: builtins.int | None = ...,
        url: builtins.str | None = ...,
        relay_protocol: global___IceServerTransportProtocol.ValueType | None = ...,
        foundation: builtins.str | None = ...,
        related_address: builtins.str | None = ...,
        related_port: builtins.int | None = ...,
        username_fragment: builtins.str | None = ...,
        tcp_type: global___IceTcpCandidateType.ValueType | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["address", b"address", "candidate_type", b"candidate_type", "foundation", b"foundation", "port", b"port", "priority", b"priority", "protocol", b"protocol", "related_address", b"related_address", "related_port", b"related_port", "relay_protocol", b"relay_protocol", "tcp_type", b"tcp_type", "transport_id", b"transport_id", "url", b"url", "username_fragment", b"username_fragment"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["address", b"address", "candidate_type", b"candidate_type", "foundation", b"foundation", "port", b"port", "priority", b"priority", "protocol", b"protocol", "related_address", b"related_address", "related_port", b"related_port", "relay_protocol", b"relay_protocol", "tcp_type", b"tcp_type", "transport_id", b"transport_id", "url", b"url", "username_fragment", b"username_fragment"]) -> None: ...

global___IceCandidateStats = IceCandidateStats

@typing.final
class CertificateStats(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FINGERPRINT_FIELD_NUMBER: builtins.int
    FINGERPRINT_ALGORITHM_FIELD_NUMBER: builtins.int
    BASE64_CERTIFICATE_FIELD_NUMBER: builtins.int
    ISSUER_CERTIFICATE_ID_FIELD_NUMBER: builtins.int
    fingerprint: builtins.str
    fingerprint_algorithm: builtins.str
    base64_certificate: builtins.str
    issuer_certificate_id: builtins.str
    def __init__(
        self,
        *,
        fingerprint: builtins.str | None = ...,
        fingerprint_algorithm: builtins.str | None = ...,
        base64_certificate: builtins.str | None = ...,
        issuer_certificate_id: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["base64_certificate", b"base64_certificate", "fingerprint", b"fingerprint", "fingerprint_algorithm", b"fingerprint_algorithm", "issuer_certificate_id", b"issuer_certificate_id"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["base64_certificate", b"base64_certificate", "fingerprint", b"fingerprint", "fingerprint_algorithm", b"fingerprint_algorithm", "issuer_certificate_id", b"issuer_certificate_id"]) -> None: ...

global___CertificateStats = CertificateStats

@typing.final
class StreamStats(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ID_FIELD_NUMBER: builtins.int
    STREAM_IDENTIFIER_FIELD_NUMBER: builtins.int
    id: builtins.str
    stream_identifier: builtins.str
    """required int64 timestamp = 3;"""
    def __init__(
        self,
        *,
        id: builtins.str | None = ...,
        stream_identifier: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["id", b"id", "stream_identifier", b"stream_identifier"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["id", b"id", "stream_identifier", b"stream_identifier"]) -> None: ...

global___StreamStats = StreamStats
