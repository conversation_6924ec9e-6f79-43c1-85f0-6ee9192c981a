# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: livekit_room.proto
# Protobuf Python Version: 4.25.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from . import models as _models_
from . import egress as _egress_
from . import agent_dispatch as _agent__dispatch_


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x12livekit_room.proto\x12\x07livekit\x1a\x14livekit_models.proto\x1a\x14livekit_egress.proto\x1a\x1clivekit_agent_dispatch.proto\"\xda\x02\n\x11\x43reateRoomRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x13\n\x0broom_preset\x18\x0c \x01(\t\x12\x15\n\rempty_timeout\x18\x02 \x01(\r\x12\x19\n\x11\x64\x65parture_timeout\x18\n \x01(\r\x12\x18\n\x10max_participants\x18\x03 \x01(\r\x12\x0f\n\x07node_id\x18\x04 \x01(\t\x12\x10\n\x08metadata\x18\x05 \x01(\t\x12#\n\x06\x65gress\x18\x06 \x01(\x0b\x32\x13.livekit.RoomEgress\x12\x19\n\x11min_playout_delay\x18\x07 \x01(\r\x12\x19\n\x11max_playout_delay\x18\x08 \x01(\r\x12\x14\n\x0csync_streams\x18\t \x01(\x08\x12\x16\n\x0ereplay_enabled\x18\r \x01(\x08\x12*\n\x06\x61gents\x18\x0e \x03(\x0b\x32\x1a.livekit.RoomAgentDispatch\"\x9e\x01\n\nRoomEgress\x12\x31\n\x04room\x18\x01 \x01(\x0b\x32#.livekit.RoomCompositeEgressRequest\x12\x33\n\x0bparticipant\x18\x03 \x01(\x0b\x32\x1e.livekit.AutoParticipantEgress\x12(\n\x06tracks\x18\x02 \x01(\x0b\x32\x18.livekit.AutoTrackEgress\";\n\tRoomAgent\x12.\n\ndispatches\x18\x01 \x03(\x0b\x32\x1a.livekit.RoomAgentDispatch\"!\n\x10ListRoomsRequest\x12\r\n\x05names\x18\x01 \x03(\t\"1\n\x11ListRoomsResponse\x12\x1c\n\x05rooms\x18\x01 \x03(\x0b\x32\r.livekit.Room\"!\n\x11\x44\x65leteRoomRequest\x12\x0c\n\x04room\x18\x01 \x01(\t\"\x14\n\x12\x44\x65leteRoomResponse\"\'\n\x17ListParticipantsRequest\x12\x0c\n\x04room\x18\x01 \x01(\t\"J\n\x18ListParticipantsResponse\x12.\n\x0cparticipants\x18\x01 \x03(\x0b\x32\x18.livekit.ParticipantInfo\"9\n\x17RoomParticipantIdentity\x12\x0c\n\x04room\x18\x01 \x01(\t\x12\x10\n\x08identity\x18\x02 \x01(\t\"\x1b\n\x19RemoveParticipantResponse\"X\n\x14MuteRoomTrackRequest\x12\x0c\n\x04room\x18\x01 \x01(\t\x12\x10\n\x08identity\x18\x02 \x01(\t\x12\x11\n\ttrack_sid\x18\x03 \x01(\t\x12\r\n\x05muted\x18\x04 \x01(\x08\":\n\x15MuteRoomTrackResponse\x12!\n\x05track\x18\x01 \x01(\x0b\x32\x12.livekit.TrackInfo\"\x88\x02\n\x18UpdateParticipantRequest\x12\x0c\n\x04room\x18\x01 \x01(\t\x12\x10\n\x08identity\x18\x02 \x01(\t\x12\x10\n\x08metadata\x18\x03 \x01(\t\x12\x32\n\npermission\x18\x04 \x01(\x0b\x32\x1e.livekit.ParticipantPermission\x12\x0c\n\x04name\x18\x05 \x01(\t\x12\x45\n\nattributes\x18\x06 \x03(\x0b\x32\x31.livekit.UpdateParticipantRequest.AttributesEntry\x1a\x31\n\x0f\x41ttributesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"\x9b\x01\n\x1aUpdateSubscriptionsRequest\x12\x0c\n\x04room\x18\x01 \x01(\t\x12\x10\n\x08identity\x18\x02 \x01(\t\x12\x12\n\ntrack_sids\x18\x03 \x03(\t\x12\x11\n\tsubscribe\x18\x04 \x01(\x08\x12\x36\n\x12participant_tracks\x18\x05 \x03(\x0b\x32\x1a.livekit.ParticipantTracks\"\x1d\n\x1bUpdateSubscriptionsResponse\"\xc0\x01\n\x0fSendDataRequest\x12\x0c\n\x04room\x18\x01 \x01(\t\x12\x0c\n\x04\x64\x61ta\x18\x02 \x01(\x0c\x12&\n\x04kind\x18\x03 \x01(\x0e\x32\x18.livekit.DataPacket.Kind\x12\x1c\n\x10\x64\x65stination_sids\x18\x04 \x03(\tB\x02\x18\x01\x12\x1e\n\x16\x64\x65stination_identities\x18\x06 \x03(\t\x12\x12\n\x05topic\x18\x05 \x01(\tH\x00\x88\x01\x01\x12\r\n\x05nonce\x18\x07 \x01(\x0c\x42\x08\n\x06_topic\"\x12\n\x10SendDataResponse\";\n\x19UpdateRoomMetadataRequest\x12\x0c\n\x04room\x18\x01 \x01(\t\x12\x10\n\x08metadata\x18\x02 \x01(\t\"\x8a\x02\n\x11RoomConfiguration\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x15\n\rempty_timeout\x18\x02 \x01(\r\x12\x19\n\x11\x64\x65parture_timeout\x18\x03 \x01(\r\x12\x18\n\x10max_participants\x18\x04 \x01(\r\x12#\n\x06\x65gress\x18\x05 \x01(\x0b\x32\x13.livekit.RoomEgress\x12\x19\n\x11min_playout_delay\x18\x07 \x01(\r\x12\x19\n\x11max_playout_delay\x18\x08 \x01(\r\x12\x14\n\x0csync_streams\x18\t \x01(\x08\x12*\n\x06\x61gents\x18\n \x03(\x0b\x32\x1a.livekit.RoomAgentDispatch\"U\n\x19\x46orwardParticipantRequest\x12\x0c\n\x04room\x18\x01 \x01(\t\x12\x10\n\x08identity\x18\x02 \x01(\t\x12\x18\n\x10\x64\x65stination_room\x18\x03 \x01(\t\"\x1c\n\x1a\x46orwardParticipantResponse\"R\n\x16MoveParticipantRequest\x12\x0c\n\x04room\x18\x01 \x01(\t\x12\x10\n\x08identity\x18\x02 \x01(\t\x12\x18\n\x10\x64\x65stination_room\x18\x03 \x01(\t\"\x19\n\x17MoveParticipantResponse2\x9b\x08\n\x0bRoomService\x12\x37\n\nCreateRoom\x12\x1a.livekit.CreateRoomRequest\x1a\r.livekit.Room\x12\x42\n\tListRooms\x12\x19.livekit.ListRoomsRequest\x1a\x1a.livekit.ListRoomsResponse\x12\x45\n\nDeleteRoom\x12\x1a.livekit.DeleteRoomRequest\x1a\x1b.livekit.DeleteRoomResponse\x12W\n\x10ListParticipants\x12 .livekit.ListParticipantsRequest\x1a!.livekit.ListParticipantsResponse\x12L\n\x0eGetParticipant\x12 .livekit.RoomParticipantIdentity\x1a\x18.livekit.ParticipantInfo\x12Y\n\x11RemoveParticipant\x12 .livekit.RoomParticipantIdentity\x1a\".livekit.RemoveParticipantResponse\x12S\n\x12MutePublishedTrack\x12\x1d.livekit.MuteRoomTrackRequest\x1a\x1e.livekit.MuteRoomTrackResponse\x12P\n\x11UpdateParticipant\x12!.livekit.UpdateParticipantRequest\x1a\x18.livekit.ParticipantInfo\x12`\n\x13UpdateSubscriptions\x12#.livekit.UpdateSubscriptionsRequest\x1a$.livekit.UpdateSubscriptionsResponse\x12?\n\x08SendData\x12\x18.livekit.SendDataRequest\x1a\x19.livekit.SendDataResponse\x12G\n\x12UpdateRoomMetadata\x12\".livekit.UpdateRoomMetadataRequest\x1a\r.livekit.Room\x12]\n\x12\x46orwardParticipant\x12\".livekit.ForwardParticipantRequest\x1a#.livekit.ForwardParticipantResponse\x12T\n\x0fMoveParticipant\x12\x1f.livekit.MoveParticipantRequest\x1a .livekit.MoveParticipantResponseBFZ#github.com/livekit/protocol/livekit\xaa\x02\rLiveKit.Proto\xea\x02\x0eLiveKit::Protob\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'room', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  _globals['DESCRIPTOR']._options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z#github.com/livekit/protocol/livekit\252\002\rLiveKit.Proto\352\002\016LiveKit::Proto'
  _globals['_UPDATEPARTICIPANTREQUEST_ATTRIBUTESENTRY']._options = None
  _globals['_UPDATEPARTICIPANTREQUEST_ATTRIBUTESENTRY']._serialized_options = b'8\001'
  _globals['_SENDDATAREQUEST'].fields_by_name['destination_sids']._options = None
  _globals['_SENDDATAREQUEST'].fields_by_name['destination_sids']._serialized_options = b'\030\001'
  _globals['_CREATEROOMREQUEST']._serialized_start=106
  _globals['_CREATEROOMREQUEST']._serialized_end=452
  _globals['_ROOMEGRESS']._serialized_start=455
  _globals['_ROOMEGRESS']._serialized_end=613
  _globals['_ROOMAGENT']._serialized_start=615
  _globals['_ROOMAGENT']._serialized_end=674
  _globals['_LISTROOMSREQUEST']._serialized_start=676
  _globals['_LISTROOMSREQUEST']._serialized_end=709
  _globals['_LISTROOMSRESPONSE']._serialized_start=711
  _globals['_LISTROOMSRESPONSE']._serialized_end=760
  _globals['_DELETEROOMREQUEST']._serialized_start=762
  _globals['_DELETEROOMREQUEST']._serialized_end=795
  _globals['_DELETEROOMRESPONSE']._serialized_start=797
  _globals['_DELETEROOMRESPONSE']._serialized_end=817
  _globals['_LISTPARTICIPANTSREQUEST']._serialized_start=819
  _globals['_LISTPARTICIPANTSREQUEST']._serialized_end=858
  _globals['_LISTPARTICIPANTSRESPONSE']._serialized_start=860
  _globals['_LISTPARTICIPANTSRESPONSE']._serialized_end=934
  _globals['_ROOMPARTICIPANTIDENTITY']._serialized_start=936
  _globals['_ROOMPARTICIPANTIDENTITY']._serialized_end=993
  _globals['_REMOVEPARTICIPANTRESPONSE']._serialized_start=995
  _globals['_REMOVEPARTICIPANTRESPONSE']._serialized_end=1022
  _globals['_MUTEROOMTRACKREQUEST']._serialized_start=1024
  _globals['_MUTEROOMTRACKREQUEST']._serialized_end=1112
  _globals['_MUTEROOMTRACKRESPONSE']._serialized_start=1114
  _globals['_MUTEROOMTRACKRESPONSE']._serialized_end=1172
  _globals['_UPDATEPARTICIPANTREQUEST']._serialized_start=1175
  _globals['_UPDATEPARTICIPANTREQUEST']._serialized_end=1439
  _globals['_UPDATEPARTICIPANTREQUEST_ATTRIBUTESENTRY']._serialized_start=1390
  _globals['_UPDATEPARTICIPANTREQUEST_ATTRIBUTESENTRY']._serialized_end=1439
  _globals['_UPDATESUBSCRIPTIONSREQUEST']._serialized_start=1442
  _globals['_UPDATESUBSCRIPTIONSREQUEST']._serialized_end=1597
  _globals['_UPDATESUBSCRIPTIONSRESPONSE']._serialized_start=1599
  _globals['_UPDATESUBSCRIPTIONSRESPONSE']._serialized_end=1628
  _globals['_SENDDATAREQUEST']._serialized_start=1631
  _globals['_SENDDATAREQUEST']._serialized_end=1823
  _globals['_SENDDATARESPONSE']._serialized_start=1825
  _globals['_SENDDATARESPONSE']._serialized_end=1843
  _globals['_UPDATEROOMMETADATAREQUEST']._serialized_start=1845
  _globals['_UPDATEROOMMETADATAREQUEST']._serialized_end=1904
  _globals['_ROOMCONFIGURATION']._serialized_start=1907
  _globals['_ROOMCONFIGURATION']._serialized_end=2173
  _globals['_FORWARDPARTICIPANTREQUEST']._serialized_start=2175
  _globals['_FORWARDPARTICIPANTREQUEST']._serialized_end=2260
  _globals['_FORWARDPARTICIPANTRESPONSE']._serialized_start=2262
  _globals['_FORWARDPARTICIPANTRESPONSE']._serialized_end=2290
  _globals['_MOVEPARTICIPANTREQUEST']._serialized_start=2292
  _globals['_MOVEPARTICIPANTREQUEST']._serialized_end=2374
  _globals['_MOVEPARTICIPANTRESPONSE']._serialized_start=2376
  _globals['_MOVEPARTICIPANTRESPONSE']._serialized_end=2401
  _globals['_ROOMSERVICE']._serialized_start=2404
  _globals['_ROOMSERVICE']._serialized_end=3455
# @@protoc_insertion_point(module_scope)
