import asyncio
from dotenv import load_dotenv
from livekit.agents import (
    Agent,
    AgentSession,
    JobContext,
    WorkerOptions,
    cli,
)
from livekit.plugins import deepgram, cartesia, silero, openai

load_dotenv()

class SimpleAgent(Agent):
    """最简单的测试Agent"""

    def __init__(self) -> None:
        super().__init__(
            instructions="""
你是张大师，一位专业的风水大师。请用中文与用户交流。
""",
        )

async def entrypoint(ctx: JobContext):
    """Agent入口点"""
    await ctx.connect()
    print(f"🎯 Agent会话开始: {ctx.room.name}")

    # 初始化AI组件
    try:
        # STT - 中文语音识别
        stt = deepgram.STT(model="nova-2", language="zh-CN")
        print("✅ Deepgram STT初始化成功")

        # TTS - 中文语音合成
        tts = cartesia.TTS(
            model="sonic-2",
            voice="f786b574-daa5-4673-aa0c-cbe3e8534c02",  # 中文声音
            language="zh"
        )
        print("✅ Cartesia TTS初始化成功")

        # VAD - 语音活动检测
        vad = silero.VAD.load()
        print("✅ Silero VAD初始化成功")

        # LLM - DeepSeek
        llm = openai.LLM.with_deepseek(
            model="deepseek-chat",
            temperature=0.7
        )
        print("✅ DeepSeek LLM初始化成功")

    except Exception as e:
        print(f"❌ AI组件初始化失败: {e}")
        return

    agent = SimpleAgent()
    session = AgentSession(
        stt=stt,
        tts=tts,
        vad=vad,
        llm=llm,
    )

    await session.start(agent=agent, room=ctx.room)
    print(f"✅ Agent会话结束: {ctx.room.name}")

if __name__ == "__main__":
    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=entrypoint,
            # 确保Agent能处理所有房间的请求
            agent_name="fengshui-agent",
        )
    )
