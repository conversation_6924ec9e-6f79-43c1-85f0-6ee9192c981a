import asyncio
from dotenv import load_dotenv
from livekit.agents import (
    Agent,
    AgentSession,
    JobContext,
    WorkerOptions,
    cli,
)
from livekit.plugins import deepgram, cartesia, silero

load_dotenv()

class SimpleAgent(Agent):
    """最简单的测试Agent"""

    def __init__(self) -> None:
        super().__init__(
            instructions="""
你是张大师，一位专业的风水大师。请用中文与用户交流。
""",
        )

async def entrypoint(ctx: JobContext):
    """Agent入口点"""
    print(f"🎯 Agent会话开始: {ctx.room.name}")
    
    agent = SimpleAgent()
    session = AgentSession(
        ctx=ctx,
        agent=agent,
        stt=deepgram.STT(),
        tts=cartesia.TTS(),
        vad=silero.VAD.load(),
    )
    
    await session.start()
    print(f"✅ Agent会话结束: {ctx.room.name}")

if __name__ == "__main__":
    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=entrypoint,
        )
    )
