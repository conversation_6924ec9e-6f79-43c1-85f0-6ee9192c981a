"""
LiveKit风水AI助手 - 基于官方示例的完整实现
使用智谱AI替代OpenAI，按照LiveKit 1.0官方架构
"""

import asyncio
from dotenv import load_dotenv
from livekit.agents import (
    Agent,
    AgentSession,
    JobContext,
    WorkerOptions,
    cli,
    function_tool,
)
from livekit.plugins import deepgram, cartesia, silero

load_dotenv()


@function_tool
async def get_fengshui_advice(
    location: str,
    room_type: str,
):
    """获取风水建议的工具函数

    Args:
        location: 房间位置或方向
        room_type: 房间类型（客厅、卧室、厨房等）
    """

    advice_map = {
        "客厅": "客厅是家庭的核心，建议保持明亮通风，沙发背靠实墙，避免正对大门。",
        "卧室": "卧室要安静舒适，床头不要对着门，避免镜子正对床铺。",
        "厨房": "厨房代表财运，保持干净整洁，炉灶不要正对水槽。",
        "办公室": "办公桌背靠实墙，面向门口，桌面保持整洁有序。"
    }

    return {
        "advice": advice_map.get(room_type, "请提供更具体的房间类型以获得专业建议。"),
        "location": location,
        "room_type": room_type
    }


class FengshuiAgent(Agent):
    """风水AI助手"""

    def __init__(self) -> None:
        super().__init__(
            instructions="""
你是张大师，一位拥有30年经验的专业风水大师。

🎯 你的专长：住宅风水、商业风水、墓地风水

🗣️ 对话风格：
- 用温和、专业的语气与用户交流
- 用简洁明了的语言解释复杂的风水概念
- 结合传统风水理论和现代生活实际
- 给出具体可行的建议

💡 回答原则：
- 每次回答控制在200字以内，简洁有力
- 重点突出，避免过于复杂的术语
- 始终保持专业和友善的态度

请用中文与用户交流，提供专业的风水咨询服务。
""",
            tools=[get_fengshui_advice],
        )


def prewarm(proc: any):
    """预热函数 - 避免lambda序列化问题"""
    print("🔥 预热风水AI助手...")


async def entrypoint(ctx: JobContext):
    """LiveKit Agent入口点 - 官方标准架构"""

    await ctx.connect()
    print("🏮 启动LiveKit风水AI助手")

    # 初始化AI组件
    try:
        # Deepgram STT (语音识别) - 使用Nova-2支持中文
        stt = deepgram.STT(model="nova-2", language="zh-CN")
        print("✅ Deepgram STT初始化成功")

        # 使用LiveKit官方支持的TTS (中文语音)
        tts = cartesia.TTS(
            model="sonic-2",
            voice="f786b574-daa5-4673-aa0c-cbe3e8534c02",  # 支持中文的声音
            language="zh"
        )
        print("✅ Cartesia TTS初始化成功 (官方支持，中文语音)")

        # Silero VAD (语音活动检测)
        vad = silero.VAD.load()
        print("✅ Silero VAD初始化成功")

        print("✅ AI组件初始化完成")

    except Exception as e:
        print(f"❌ AI组件初始化失败: {e}")
        return

    # 使用LiveKit官方支持的DeepSeek LLM
    from livekit.plugins import openai

    # 创建DeepSeek LLM - 官方集成
    deepseek_llm = openai.LLM.with_deepseek(
        model="deepseek-chat",  # DeepSeek-V3
        temperature=0.7
    )
    print("✅ DeepSeek LLM初始化成功")

    # 创建Agent会话
    session = AgentSession(
        stt=stt,
        llm=deepseek_llm,  # 使用DeepSeek作为LLM
        tts=tts,
        vad=vad,
    )

    # 启动会话
    await session.start(
        agent=FengshuiAgent(),
        room=ctx.room,
    )

    print("🎯 风水AI助手已就绪，等待用户交互")


if __name__ == "__main__":
    cli.run_app(WorkerOptions(entrypoint_fnc=entrypoint, prewarm_fnc=prewarm))
