"""
LiveKit风水AI助手 - 基于官方示例的完整实现
使用智谱AI替代OpenAI，按照LiveKit 1.0官方架构
"""

import asyncio
import json
import os
from dotenv import load_dotenv
from livekit.agents import (
    Agent,
    AgentSession,
    JobContext,
    WorkerOptions,
    cli,
    function_tool,
)
from livekit.plugins import deepgram, cartesia, silero

load_dotenv()

# 加载风水知识库
def load_fengshui_knowledge():
    """加载风水知识库文件"""
    knowledge_file = os.path.join(os.path.dirname(__file__), 'fengshui_knowledge.json')
    try:
        with open(knowledge_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print("⚠️ 风水知识库文件未找到，使用基础建议")
        return None
    except json.JSONDecodeError:
        print("⚠️ 风水知识库文件格式错误，使用基础建议")
        return None

# 全局加载知识库
FENGSHUI_KNOWLEDGE = load_fengshui_knowledge()


@function_tool
async def get_fengshui_advice(
    location: str,
    room_type: str,
):
    """获取风水建议的工具函数

    Args:
        location: 房间位置或方向
        room_type: 房间类型（客厅、卧室、厨房等）
    """

    # 基础建议映射（作为后备方案）
    basic_advice_map = {
        "客厅": "客厅是家庭的核心，建议保持明亮通风，沙发背靠实墙，避免正对大门。",
        "卧室": "卧室要安静舒适，床头不要对着门，避免镜子正对床铺。",
        "厨房": "厨房代表财运，保持干净整洁，炉灶不要正对水槽。",
        "办公室": "办公桌背靠实墙，面向门口，桌面保持整洁有序。"
    }

    # 如果知识库可用，使用详细建议
    if FENGSHUI_KNOWLEDGE and "room_advice" in FENGSHUI_KNOWLEDGE:
        room_data = FENGSHUI_KNOWLEDGE["room_advice"].get(room_type)
        if room_data:
            detailed = room_data.get("detailed", {})
            advice_parts = [room_data.get("basic", "")]

            # 添加详细建议
            for category, content in detailed.items():
                advice_parts.append(f"\n【{category}】{content}")

            advice = "".join(advice_parts)
        else:
            advice = basic_advice_map.get(room_type, "请提供更具体的房间类型以获得专业建议。")
    else:
        # 使用基础建议作为后备
        advice = basic_advice_map.get(room_type, "请提供更具体的房间类型以获得专业建议。")

    return {
        "advice": advice,
        "location": location,
        "room_type": room_type
    }


@function_tool
async def get_direction_advice(
    direction: str,
):
    """获取方位风水建议的工具函数

    Args:
        direction: 方位（东、南、西、北、东南、西南、西北、东北）
    """

    if FENGSHUI_KNOWLEDGE and "direction_advice" in FENGSHUI_KNOWLEDGE:
        direction_data = FENGSHUI_KNOWLEDGE["direction_advice"].get(direction)
        if direction_data:
            advice = f"【{direction}方位风水建议】\n"
            advice += f"五行属性：{direction_data.get('属性', '未知')}\n"
            advice += f"适合布置：{direction_data.get('适合', '无特殊建议')}\n"
            advice += f"推荐颜色：{direction_data.get('颜色', '无特殊要求')}\n"
            advice += f"注意禁忌：{direction_data.get('禁忌', '无特殊禁忌')}"
        else:
            advice = f"抱歉，暂时没有关于{direction}方位的详细建议，请咨询更具体的方位信息。"
    else:
        advice = "风水知识库暂时不可用，建议咨询专业风水师。"

    return {
        "advice": advice,
        "direction": direction
    }


@function_tool
async def get_color_advice(
    color: str,
):
    """获取颜色风水建议的工具函数

    Args:
        color: 颜色（红色、黄色、绿色、蓝色、白色、黑色等）
    """

    if FENGSHUI_KNOWLEDGE and "color_advice" in FENGSHUI_KNOWLEDGE:
        color_data = FENGSHUI_KNOWLEDGE["color_advice"].get(color)
        if color_data:
            advice = f"【{color}在风水中的寓意】\n"
            advice += f"象征意义：{color_data.get('寓意', '无特殊寓意')}\n"
            advice += f"适用场所：{color_data.get('适用', '无特殊要求')}\n"
            advice += f"使用禁忌：{color_data.get('禁忌', '无特殊禁忌')}"
        else:
            advice = f"抱歉，暂时没有关于{color}的详细风水建议。"
    else:
        advice = "风水知识库暂时不可用，建议咨询专业风水师。"

    return {
        "advice": advice,
        "color": color
    }


class FengshuiAgent(Agent):
    """风水AI助手 - 扩展版本"""

    def __init__(self) -> None:
        super().__init__(
            instructions="""
你是张大师，一位拥有30年经验的专业风水大师。

🎯 你的专长：住宅风水、商业风水、墓地风水、方位布局、色彩搭配

🗣️ 对话风格：
- 用温和、专业的语气与用户交流
- 用简洁明了的语言解释复杂的风水概念
- 结合传统风水理论和现代生活实际
- 给出具体可行的建议

💡 回答原则：
- 每次回答控制在300字以内，内容丰富但简洁有力
- 重点突出，避免过于复杂的术语
- 始终保持专业和友善的态度
- 可以使用工具获取详细的专业建议

🔧 可用工具：
- get_fengshui_advice: 获取房间布局风水建议
- get_direction_advice: 获取方位风水建议
- get_color_advice: 获取颜色风水建议

请用中文与用户交流，提供专业的风水咨询服务。当用户询问具体房间、方位或颜色时，主动使用相应工具获取详细建议。
""",
            tools=[get_fengshui_advice, get_direction_advice, get_color_advice],
        )


def prewarm(proc: any):
    """预热函数 - 避免lambda序列化问题"""
    print("🔥 预热风水AI助手...")


async def entrypoint(ctx: JobContext):
    """LiveKit Agent入口点 - 官方标准架构"""

    await ctx.connect()
    print("🏮 启动LiveKit风水AI助手")

    # 初始化AI组件
    try:
        # Deepgram STT (语音识别) - 使用Nova-2支持中文
        stt = deepgram.STT(model="nova-2", language="zh-CN")
        print("✅ Deepgram STT初始化成功")

        # 使用LiveKit官方支持的TTS (中文语音)
        tts = cartesia.TTS(
            model="sonic-2",
            voice="f786b574-daa5-4673-aa0c-cbe3e8534c02",  # 支持中文的声音
            language="zh"
        )
        print("✅ Cartesia TTS初始化成功 (官方支持，中文语音)")

        # Silero VAD (语音活动检测)
        vad = silero.VAD.load()
        print("✅ Silero VAD初始化成功")

        print("✅ AI组件初始化完成")

    except Exception as e:
        print(f"❌ AI组件初始化失败: {e}")
        return

    # 使用LiveKit官方支持的DeepSeek LLM
    from livekit.plugins import openai

    # 创建DeepSeek LLM - 官方集成
    deepseek_llm = openai.LLM.with_deepseek(
        model="deepseek-chat",  # DeepSeek-V3
        temperature=0.7
    )
    print("✅ DeepSeek LLM初始化成功")

    # 创建Agent会话
    session = AgentSession(
        stt=stt,
        llm=deepseek_llm,  # 使用DeepSeek作为LLM
        tts=tts,
        vad=vad,
    )

    # 启动会话
    await session.start(
        agent=FengshuiAgent(),
        room=ctx.room,
    )

    print("🎯 风水AI助手已就绪，等待用户交互")


if __name__ == "__main__":
    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=entrypoint,
            prewarm_fnc=prewarm,
            # 强制Agent处理所有房间请求
            agent_name="fengshui-master",
        )
    )
