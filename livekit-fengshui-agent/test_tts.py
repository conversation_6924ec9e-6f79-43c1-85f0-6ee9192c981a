#!/usr/bin/env python3
"""
TTS测试脚本 - 比较不同TTS提供商的中文语音效果
"""

import asyncio
import os
from dotenv import load_dotenv

load_dotenv()

# 测试文本
TEST_TEXT = "您好！我是张大师，一位专业的风水顾问。今天我将为您提供专业的风水咨询服务。请告诉我您想了解哪方面的风水知识？"

async def test_azure_tts():
    """测试Azure AI Speech TTS"""
    print("🧪 测试 Azure AI Speech TTS (中文优化)")
    try:
        from livekit.plugins import azure
        
        tts = azure.TTS(
            voice="zh-CN-XiaoxiaoNeural",  # 中文女声
            language="zh-CN"
        )
        
        print("✅ Azure TTS初始化成功")
        print(f"📝 测试文本: {TEST_TEXT}")
        
        # 这里只是测试初始化，实际语音合成需要在LiveKit环境中
        return True
        
    except Exception as e:
        print(f"❌ Azure TTS测试失败: {e}")
        return False

async def test_elevenlabs_tts():
    """测试ElevenLabs TTS"""
    print("\n🧪 测试 ElevenLabs TTS (高质量)")
    try:
        from livekit.plugins import elevenlabs
        
        tts = elevenlabs.TTS(
            model="eleven_multilingual_v2",
            language="zh"
        )
        
        print("✅ ElevenLabs TTS初始化成功")
        print(f"📝 测试文本: {TEST_TEXT}")
        return True
        
    except Exception as e:
        print(f"❌ ElevenLabs TTS测试失败: {e}")
        return False

async def test_openai_tts():
    """测试OpenAI TTS"""
    print("\n🧪 测试 OpenAI TTS (简单可靠)")
    try:
        from livekit.plugins import openai
        
        tts = openai.TTS(
            model="tts-1",
            voice="alloy"
        )
        
        print("✅ OpenAI TTS初始化成功")
        print(f"📝 测试文本: {TEST_TEXT}")
        return True
        
    except Exception as e:
        print(f"❌ OpenAI TTS测试失败: {e}")
        return False

async def test_cartesia_tts():
    """测试Cartesia TTS"""
    print("\n🧪 测试 Cartesia TTS (原始)")
    try:
        from livekit.plugins import cartesia
        
        tts = cartesia.TTS(
            model="sonic-2",
            voice="f786b574-daa5-4673-aa0c-cbe3e8534c02",
            language="zh"
        )
        
        print("✅ Cartesia TTS初始化成功")
        print(f"📝 测试文本: {TEST_TEXT}")
        return True
        
    except Exception as e:
        print(f"❌ Cartesia TTS测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🎯 LiveKit TTS 中文语音测试")
    print("=" * 50)
    
    results = []
    
    # 测试所有TTS提供商
    results.append(("Azure AI Speech", await test_azure_tts()))
    results.append(("ElevenLabs", await test_elevenlabs_tts()))
    results.append(("OpenAI", await test_openai_tts()))
    results.append(("Cartesia", await test_cartesia_tts()))
    
    # 显示测试结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    for name, success in results:
        status = "✅ 可用" if success else "❌ 不可用"
        print(f"  {name}: {status}")
    
    # 推荐
    print("\n🎯 推荐使用顺序:")
    print("  1. Azure AI Speech - 中文语音质量最佳")
    print("  2. ElevenLabs - 高质量多语言支持")
    print("  3. OpenAI - 简单可靠")
    print("  4. Cartesia - 低延迟备用")
    
    print("\n💡 提示:")
    print("  - Azure需要AZURE_SPEECH_KEY和AZURE_SPEECH_REGION")
    print("  - ElevenLabs需要ELEVEN_API_KEY")
    print("  - OpenAI需要OPENAI_API_KEY")
    print("  - Cartesia需要CARTESIA_API_KEY")

if __name__ == "__main__":
    asyncio.run(main())
