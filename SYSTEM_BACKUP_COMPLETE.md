# 🎯 LiveKit风水AI助手系统 - 完整备份文档

**备份时间**: 2025年8月2日 13:50 UTC
**系统状态**: ✅ 完全正常运行 (已验证用户交互)
**备份版本**: v1.0-stable
**Git提交**: 8e6412d0

## 📊 系统组件状态

### ✅ 前端服务 (livekit-frontend)
- **状态**: 正常运行
- **端口**: 7000
- **框架**: Next.js 15.4.5 + React + TypeScript
- **关键功能**: 
  - HTTPS安全访问 (https://su.guiyunai.fun)
  - 麦克风权限获取正常
  - WebSocket连接稳定
  - Token自动刷新机制
  - 风水AI界面完整

### ✅ 后端Agent (livekit-fengshui-agent)
- **状态**: 正常运行
- **Worker ID**: AW_dgeqQNvzbUSz
- **连接**: LiveKit Cloud Singapore
- **功能**: 
  - 3个专业风水工具 (房间、方位、颜色建议)
  - DeepSeek LLM集成
  - Deepgram STT + Cartesia TTS
  - 完整知识库 (fengshui_knowledge.json)

### ✅ 系统配置
- **域名**: su.guiyunai.fun
- **SSL证书**: Let's Encrypt (有效至2025年10月28日)
- **反向代理**: Nginx 1.24.0
- **LiveKit**: Cloud服务 (wss://kjh-a5mlk6sq.livekit.cloud)

## 🗂️ 备份内容清单

### 📁 前端代码 (livekit-frontend/)
```
├── app/
│   ├── api/connection-details/route.ts    # Token生成API
│   ├── globals.css                        # 全局样式
│   └── layout.tsx                         # 应用布局
├── components/
│   ├── app.tsx                           # 主应用组件
│   ├── session-view.tsx                  # 会话视图
│   ├── welcome.tsx                       # 欢迎页面
│   └── ui/                               # UI组件库
├── hooks/
│   ├── useConnectionDetails.ts           # 连接管理 (含自动刷新)
│   └── useDebug.ts                       # 调试工具
├── lib/
│   ├── types.ts                          # 类型定义
│   └── utils.ts                          # 工具函数
├── public/
│   ├── fengshui-logo.svg                 # 风水Logo
│   └── fengshui-logo-dark.svg            # 深色Logo
├── .env.local                            # 环境变量
├── .env.example                          # 环境变量示例
├── package.json                          # 依赖配置
├── tailwind.config.ts                    # Tailwind配置
└── tsconfig.json                         # TypeScript配置
```

### 📁 后端Agent (livekit-fengshui-agent/)
```
├── agent.py                              # 主Agent代码
├── simple_agent.py                       # 简化测试Agent
├── fengshui_knowledge.json               # 风水知识库
├── .env                                  # 环境变量
├── requirements.txt                      # Python依赖
└── venv/                                 # 虚拟环境 (完整)
```

### 📁 系统配置文件
```
├── /etc/nginx/sites-available/su.guiyunai.fun  # Nginx配置
├── /etc/letsencrypt/live/su.guiyunai.fun/       # SSL证书
├── deploy.sh                                    # 部署脚本
├── start-production.sh                          # 启动脚本
├── stop-production.sh                           # 停止脚本
└── status.sh                                    # 状态检查脚本
```

## 🔧 关键配置信息

### LiveKit Cloud配置
- **API Key**: API7Na43BYsXGmi
- **WebSocket URL**: wss://kjh-a5mlk6sq.livekit.cloud
- **Region**: Singapore
- **Token TTL**: 15分钟 (自动刷新每10分钟)

### AI服务配置
- **DeepSeek API**: ***********************************
- **Deepgram STT**: 已配置
- **Cartesia TTS**: 已配置

### 域名和SSL
- **域名**: su.guiyunai.fun
- **IP地址**: ***************
- **SSL证书**: 自动续期 Let's Encrypt
- **HTTP/2**: 已启用

## 🚀 系统恢复步骤

### 1. 环境准备
```bash
# 克隆备份
git clone <repository> /www/wwwroot/su.guiyunai.fun
cd /www/wwwroot/su.guiyunai.fun

# 安装系统依赖
apt update && apt install -y nginx certbot python3-certbot-nginx nodejs npm
npm install -g pnpm
```

### 2. 前端部署
```bash
cd livekit-frontend
pnpm install
pnpm build
pnpm dev  # 开发模式，或使用 pnpm start 生产模式
```

### 3. 后端部署
```bash
cd livekit-fengshui-agent
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
python agent.py dev
```

### 4. 系统配置
```bash
# 恢复Nginx配置
cp /etc/nginx/sites-available/su.guiyunai.fun.backup /etc/nginx/sites-available/su.guiyunai.fun
systemctl reload nginx

# 配置SSL (如需要)
certbot --nginx -d su.guiyunai.fun
```

## ✅ 验证清单

### 前端验证
- [ ] https://su.guiyunai.fun 可访问
- [ ] SSL证书有效
- [ ] 麦克风权限获取正常
- [ ] 页面加载完整

### 后端验证
- [ ] Agent成功注册到LiveKit Cloud
- [ ] 3个风水工具函数正常
- [ ] DeepSeek LLM响应正常
- [ ] 语音识别和合成正常

### 连接验证
- [ ] WebSocket连接成功
- [ ] Token自动刷新正常
- [ ] Agent状态检测正确
- [ ] 语音交互流畅

## 🔄 已修复的关键问题

1. **WebSocket连接问题**: Token过期自动刷新机制
2. **Agent状态检测**: 修复isAgentAvailable函数逻辑
3. **HTTPS配置**: 完整的SSL和反向代理设置
4. **媒体权限**: HTTPS环境下麦克风访问正常
5. **知识库扩展**: 3个专业风水工具完整实现

## 📞 技术支持信息

- **LiveKit版本**: Agents v1.2.2, Client SDK v2.15.4
- **Node.js版本**: v18+
- **Python版本**: 3.12
- **操作系统**: Ubuntu 24.04 LTS

## 🎉 备份验证结果

### ✅ 实时验证 (2025-08-02 13:50 UTC)

**前端验证**:
- ✅ https://su.guiyunai.fun 正常访问 (HTTP/2 200)
- ✅ SSL证书有效 (Let's Encrypt)
- ✅ Nginx反向代理正常
- ✅ 静态资源加载正常

**后端验证**:
- ✅ Agent正在处理真实用户交互
- ✅ Job ID: AJ_LTqJ5V446U8u (活跃会话)
- ✅ 中文语音识别正常 ("你好", "你好吗", "会唱歌吗")
- ✅ DeepSeek LLM响应正常
- ✅ 所有AI组件初始化成功

**连接验证**:
- ✅ WebSocket连接稳定
- ✅ Token自动刷新机制正常
- ✅ 用户语音交互流畅
- ✅ 实时转录功能正常

### 📊 备份完整性确认
- ✅ 所有关键文件已包含在Git提交中
- ✅ 环境变量和配置文件完整
- ✅ 前端和后端代码完全备份
- ✅ 系统配置和SSL证书信息已记录
- ✅ 恢复步骤文档详细完整

---

**备份创建者**: Augment Agent
**最后更新**: 2025-08-02 13:50 UTC
**备份完整性**: ✅ 已验证 (包含实时用户交互测试)
**Git提交哈希**: 8e6412d0
