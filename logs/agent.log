nohup: ignoring input
{"message": "starting worker", "level": "INFO", "name": "livekit.agents", "version": "1.2.2", "rtc-version": "1.0.12", "timestamp": "2025-08-02T07:33:58.585323+00:00"}
{"message": "preloading plugins", "level": "INFO", "name": "livekit.agents", "packages": ["livekit.plugins.deepgram", "livekit.plugins.cartesia", "livekit.plugins.silero", "av"], "timestamp": "2025-08-02T07:33:58.585772+00:00"}
{"message": "initializing process", "level": "INFO", "name": "livekit.agents", "pid": 7653, "timestamp": "2025-08-02T07:34:00.260316+00:00"}
{"message": "initializing process", "level": "INFO", "name": "livekit.agents", "pid": 7655, "timestamp": "2025-08-02T07:34:00.268645+00:00"}
{"message": "initializing process", "level": "INFO", "name": "livekit.agents", "pid": 7657, "timestamp": "2025-08-02T07:34:00.273605+00:00"}
{"message": "initializing process", "level": "INFO", "name": "livekit.agents", "pid": 7659, "timestamp": "2025-08-02T07:34:00.286872+00:00"}
{"message": "process initialized", "level": "INFO", "name": "livekit.agents", "pid": 7653, "elapsed_time": 0.05, "timestamp": "2025-08-02T07:34:00.314038+00:00"}
{"message": "process initialized", "level": "INFO", "name": "livekit.agents", "pid": 7659, "elapsed_time": 0.06, "timestamp": "2025-08-02T07:34:00.348035+00:00"}
{"message": "process initialized", "level": "INFO", "name": "livekit.agents", "pid": 7655, "elapsed_time": 0.08, "timestamp": "2025-08-02T07:34:00.348637+00:00"}
{"message": "process initialized", "level": "INFO", "name": "livekit.agents", "pid": 7657, "elapsed_time": 0.08, "timestamp": "2025-08-02T07:34:00.358718+00:00"}
{"message": "registered worker", "level": "INFO", "name": "livekit.agents", "id": "AW_er3XzXUsE9dW", "url": "wss://kjh-a5mlk6sq.livekit.cloud", "region": "Singapore", "protocol": 16, "timestamp": "2025-08-02T07:34:01.174751+00:00"}
{"message": "received job request", "level": "INFO", "name": "livekit.agents", "job_id": "AJ_fuCzqGuFJTYH", "dispatch_id": "", "room_name": "voice_assistant_room_815", "agent_name": "", "resuming": false, "timestamp": "2025-08-02T08:12:19.719748+00:00"}
{"message": "initializing process", "level": "INFO", "name": "livekit.agents", "pid": 10033, "timestamp": "2025-08-02T08:12:19.934478+00:00"}
{"message": "process initialized", "level": "INFO", "name": "livekit.agents", "pid": 10033, "elapsed_time": 0.05, "timestamp": "2025-08-02T08:12:19.983326+00:00"}
{"message": "closing agent session due to participant disconnect (disable via `RoomInputOptions.close_on_disconnect=False`)", "level": "INFO", "name": "livekit.agents", "participant": "voice_assistant_user_5661", "reason": "CLIENT_INITIATED", "pid": 7653, "job_id": "AJ_fuCzqGuFJTYH", "timestamp": "2025-08-02T08:12:28.588319+00:00"}
{"message": "received job request", "level": "INFO", "name": "livekit.agents", "job_id": "AJ_VYm2BEdfzDLe", "dispatch_id": "", "room_name": "voice_assistant_room_1593", "agent_name": "", "resuming": false, "timestamp": "2025-08-02T08:12:33.614486+00:00"}
{"message": "initializing process", "level": "INFO", "name": "livekit.agents", "pid": 10092, "timestamp": "2025-08-02T08:12:34.332142+00:00"}
{"message": "process initialized", "level": "INFO", "name": "livekit.agents", "pid": 10092, "elapsed_time": 0.04, "timestamp": "2025-08-02T08:12:34.372764+00:00"}
{"message": "closing agent session due to participant disconnect (disable via `RoomInputOptions.close_on_disconnect=False`)", "level": "INFO", "name": "livekit.agents", "participant": "voice_assistant_user_9040", "reason": "CLIENT_INITIATED", "pid": 7659, "job_id": "AJ_VYm2BEdfzDLe", "timestamp": "2025-08-02T08:12:41.276777+00:00"}
{"message": "livekit::rtc_engine:453:livekit::rtc_engine - received session close: \"signal client closed: \\\"stream closed\\\"\" UnknownReason Resume", "level": "WARNING", "name": "livekit", "pid": 7653, "job_id": "AJ_fuCzqGuFJTYH", "timestamp": "2025-08-02T08:12:54.750502+00:00"}
{"message": "process exiting", "level": "INFO", "name": "livekit.agents", "reason": "", "pid": 7653, "job_id": "AJ_fuCzqGuFJTYH", "timestamp": "2025-08-02T08:12:54.757219+00:00"}
🔥 预热风水AI助手...
🏮 启动LiveKit风水AI助手
✅ Deepgram STT初始化成功
✅ Cartesia TTS初始化成功 (官方支持，中文语音)
✅ Silero VAD初始化成功
✅ AI组件初始化完成
✅ DeepSeek LLM初始化成功
🎯 风水AI助手已就绪，等待用户交互
{"message": "process exiting", "level": "INFO", "name": "livekit.agents", "reason": "room disconnected", "pid": 7659, "job_id": "AJ_VYm2BEdfzDLe", "timestamp": "2025-08-02T08:13:05.707537+00:00"}
🔥 预热风水AI助手...
🏮 启动LiveKit风水AI助手
✅ Deepgram STT初始化成功
✅ Cartesia TTS初始化成功 (官方支持，中文语音)
✅ Silero VAD初始化成功
✅ AI组件初始化完成
✅ DeepSeek LLM初始化成功
🎯 风水AI助手已就绪，等待用户交互
{"message": "worker is at full capacity, marking as unavailable", "level": "INFO", "name": "livekit.agents", "load": 0.9374227999999999, "threshold": "_WorkerEnvOption(dev_default=inf, prod_default=0.75)", "timestamp": "2025-08-02T08:20:49.861138+00:00"}
{"message": "worker is below capacity, marking as available", "level": "INFO", "name": "livekit.agents", "load": 0.6813436999999999, "threshold": "_WorkerEnvOption(dev_default=inf, prod_default=0.75)", "timestamp": "2025-08-02T08:20:52.362099+00:00"}
{"message": "worker is at full capacity, marking as unavailable", "level": "INFO", "name": "livekit.agents", "load": 0.7813842999999999, "threshold": "_WorkerEnvOption(dev_default=inf, prod_default=0.75)", "timestamp": "2025-08-02T08:20:54.864075+00:00"}
{"message": "worker is below capacity, marking as available", "level": "INFO", "name": "livekit.agents", "load": 0.7035268, "threshold": "_WorkerEnvOption(dev_default=inf, prod_default=0.75)", "timestamp": "2025-08-02T08:21:07.372648+00:00"}
{"message": "received job request", "level": "INFO", "name": "livekit.agents", "job_id": "AJ_6QpySGgbNcDf", "dispatch_id": "", "room_name": "voice_assistant_room_5731", "agent_name": "", "resuming": false, "timestamp": "2025-08-02T08:32:31.500928+00:00"}
{"message": "initializing process", "level": "INFO", "name": "livekit.agents", "pid": 12616, "timestamp": "2025-08-02T08:32:31.787124+00:00"}
{"message": "process initialized", "level": "INFO", "name": "livekit.agents", "pid": 12616, "elapsed_time": 0.04, "timestamp": "2025-08-02T08:32:31.829190+00:00"}
{"message": "closing agent session due to participant disconnect (disable via `RoomInputOptions.close_on_disconnect=False`)", "level": "INFO", "name": "livekit.agents", "participant": "voice_assistant_user_9332", "reason": "CLIENT_INITIATED", "pid": 7655, "job_id": "AJ_6QpySGgbNcDf", "timestamp": "2025-08-02T08:32:40.027142+00:00"}
{"message": "received job request", "level": "INFO", "name": "livekit.agents", "job_id": "AJ_vAJ4rWSda2Up", "dispatch_id": "", "room_name": "voice_assistant_room_2756", "agent_name": "", "resuming": false, "timestamp": "2025-08-02T08:32:42.414557+00:00"}
{"message": "initializing process", "level": "INFO", "name": "livekit.agents", "pid": 12678, "timestamp": "2025-08-02T08:32:42.879025+00:00"}
{"message": "process initialized", "level": "INFO", "name": "livekit.agents", "pid": 12678, "elapsed_time": 0.04, "timestamp": "2025-08-02T08:32:42.924188+00:00"}
{"message": "process exiting", "level": "INFO", "name": "livekit.agents", "reason": "", "pid": 7655, "job_id": "AJ_6QpySGgbNcDf", "timestamp": "2025-08-02T08:33:05.247910+00:00"}
{"message": "livekit::rtc_engine:453:livekit::rtc_engine - received session close: \"signal client closed: \\\"stream closed\\\"\" UnknownReason Resume", "level": "WARNING", "name": "livekit", "pid": 7655, "job_id": "AJ_6QpySGgbNcDf", "timestamp": "2025-08-02T08:33:05.250001+00:00"}
🔥 预热风水AI助手...
🏮 启动LiveKit风水AI助手
✅ Deepgram STT初始化成功
✅ Cartesia TTS初始化成功 (官方支持，中文语音)
✅ Silero VAD初始化成功
✅ AI组件初始化完成
✅ DeepSeek LLM初始化成功
🎯 风水AI助手已就绪，等待用户交互
{"message": "worker is at full capacity, marking as unavailable", "level": "INFO", "name": "livekit.agents", "load": 0.9196475999999999, "threshold": "_WorkerEnvOption(dev_default=inf, prod_default=0.75)", "timestamp": "2025-08-02T09:00:40.880999+00:00"}
{"message": "worker is below capacity, marking as available", "level": "INFO", "name": "livekit.agents", "load": 0.7181232999999999, "threshold": "_WorkerEnvOption(dev_default=inf, prod_default=0.75)", "timestamp": "2025-08-02T09:01:10.891578+00:00"}
{"message": "draining worker", "level": "INFO", "name": "livekit.agents", "id": "AW_er3XzXUsE9dW", "timeout": 1800, "timestamp": "2025-08-02T09:05:04.939708+00:00"}
